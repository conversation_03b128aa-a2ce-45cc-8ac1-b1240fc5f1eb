{"permissions": {"allow": ["Bash(rm:*)", "Bash(grep:*)", "Bash(git checkout:*)", "Bash(npm run dev:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run build:*)", "Bash(ls:*)", "Bash(npm run lint)", "Bash(node:*)", "Bash(npm run type-check:*)", "Bash(npx next lint:*)", "Bash(rg:*)", "<PERSON><PERSON>(cat:*)", "Bash(npx supabase db reset:*)", "Bash(npx supabase:*)", "Bash(npm run:*)"], "deny": []}, "enableAllProjectMcpServers": false}
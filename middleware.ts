import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { type NextRequest, NextResponse } from 'next/server';

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set({
            name,
            value,
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({
            name,
            value: '',
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({
            name,
            value: '',
            ...options,
          });
        },
      },
    }
  );

  // Check for PASSWORD_RECOVERY event
  const { data } = await supabase.auth.getSession();
  
  // If this is a password recovery session and the user is not on an auth route,
  // redirect them to the password reset page
  if (data?.session) {
    const isAuthRoute = request.nextUrl.pathname.startsWith('/auth');
    const isPasswordRecovery = request.cookies.get('sb-recovery-mode')?.value === 'true';
    
    // The special case for PASSWORD_RECOVERY sessions
    if (!isAuthRoute && isPasswordRecovery) {
      // Redirect to password reset page
      return NextResponse.redirect(new URL('/auth/reset-password', request.url));
    }
  }

  // Refresh session if expired - important to do before accessing Server Components
  await supabase.auth.getUser();

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}; 
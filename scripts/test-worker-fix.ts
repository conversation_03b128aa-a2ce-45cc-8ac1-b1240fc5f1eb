#!/usr/bin/env tsx

/**
 * Test script to verify the worker scoping fix
 */

import { supabaseAdmin } from '../lib/supabase/admin';

async function testWorkerFix() {
  console.log('🧪 Testing worker scoping fix...');
  
  try {
    // Test the claim_pending_conversation function directly
    console.log('   Testing atomic claiming function...');
    const { data, error } = await supabaseAdmin
      .rpc('claim_pending_conversation', {
        worker_id: 'test-worker-fix'
      });
    
    if (error) {
      console.error('   ❌ Error calling claim function:', error.message);
      return false;
    }
    
    console.log('   ✅ Atomic claiming function works correctly');
    console.log(`   📊 Claimed conversations: ${data?.length || 0}`);
    
    if (data && data.length > 0) {
      console.log('   📋 Claimed conversation details:');
      data.forEach((conv: any, index: number) => {
        console.log(`     ${index + 1}. ID: ${conv.id}, Video: ${conv.youtube_video_id}`);
      });
      
      // Release the claimed conversations back
      for (const conv of data) {
        const { error: releaseError } = await supabaseAdmin
          .rpc('release_conversation', {
            conversation_id: conv.id,
            worker_id: 'test-worker-fix'
          });
        
        if (releaseError) {
          console.warn(`   ⚠️  Failed to release conversation ${conv.id}:`, releaseError.message);
        } else {
          console.log(`   ✅ Released conversation ${conv.id} back to pending`);
        }
      }
    }
    
    return true;
  } catch (error) {
    console.error('   ❌ Test failed:', error);
    return false;
  }
}

async function checkDatabaseStatus() {
  console.log('📊 Checking current database status...');
  
  try {
    const { data, error } = await supabaseAdmin
      .from('conversations')
      .select('processing_status')
      .not('processing_status', 'is', null);
    
    if (error) {
      console.error('   ❌ Failed to check database status:', error.message);
      return;
    }
    
    const statusCounts = data.reduce((acc: any, conv: any) => {
      acc[conv.processing_status] = (acc[conv.processing_status] || 0) + 1;
      return acc;
    }, {});
    
    console.log('   📈 Conversation status counts:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`     ${status}: ${count}`);
    });
    
  } catch (error) {
    console.error('   ❌ Database status check failed:', error);
  }
}

async function main() {
  console.log('🚀 Starting worker fix verification...\n');
  
  try {
    // Check current database status
    await checkDatabaseStatus();
    console.log('');
    
    // Test the worker fix
    const testPassed = await testWorkerFix();
    console.log('');
    
    if (testPassed) {
      console.log('🎉 Worker scoping fix verification completed successfully!');
      console.log('   The "conversations is not defined" error should now be resolved.');
      console.log('   Workers can now properly claim and process conversations.');
    } else {
      console.log('⚠️  Worker fix verification completed with warnings.');
      console.log('   Please check the error messages above.');
    }
    
  } catch (error) {
    console.error('💥 Worker fix verification failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

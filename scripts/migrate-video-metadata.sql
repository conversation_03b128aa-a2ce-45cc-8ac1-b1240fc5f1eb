-- Video Metadata Migration Script
-- This script adds support for storing rich YouTube video metadata

-- Create video_metadata table
CREATE TABLE IF NOT EXISTS video_metadata (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  video_id VARCHAR(11) NOT NULL UNIQUE, -- YouTube video ID is always 11 characters
  title TEXT,
  description TEXT,
  duration INTEGER, -- Duration in seconds
  channel_id VARCHAR(100),
  channel_name TEXT,
  tags TEXT[], -- Array of tags
  thumbnail_url TEXT,
  upload_date TIMESTAMP WITH TIME ZONE,
  view_count BIGINT,
  like_count BIGINT,
  transcript_languages TEXT[], -- Array of available transcript languages
  raw_metadata JSONB, -- Store the full metadata response
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add index on video_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_video_metadata_video_id ON video_metadata(video_id);

-- Add index on created_at for sorting
CREATE INDEX IF NOT EXISTS idx_video_metadata_created_at ON video_metadata(created_at);

-- Add video_metadata_id column to conversations table
ALTER TABLE conversations 
ADD COLUMN IF NOT EXISTS video_metadata_id UUID REFERENCES video_metadata(id);

-- Add index on video_metadata_id for joins
CREATE INDEX IF NOT EXISTS idx_conversations_video_metadata_id ON conversations(video_metadata_id);

-- Enable RLS on video_metadata table
ALTER TABLE video_metadata ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for video_metadata table (allow all users to read, since video metadata is public)
CREATE POLICY "Allow all users to read video metadata" 
ON video_metadata FOR SELECT 
USING (true);

-- Create RLS policy for video_metadata table (allow authenticated users to insert)
CREATE POLICY "Allow authenticated users to insert video metadata" 
ON video_metadata FOR INSERT 
TO authenticated 
WITH CHECK (true);

-- Create RLS policy for video_metadata table (allow authenticated users to update)
CREATE POLICY "Allow authenticated users to update video metadata" 
ON video_metadata FOR UPDATE 
TO authenticated 
USING (true);

-- Create trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_video_metadata_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_video_metadata_updated_at
  BEFORE UPDATE ON video_metadata
  FOR EACH ROW
  EXECUTE FUNCTION update_video_metadata_updated_at();

-- Show completion message
SELECT 'Video metadata migration completed successfully!' AS status; 
#!/usr/bin/env tsx

/**
 * Test script to demonstrate the race condition fix
 * This script simulates multiple workers trying to claim the same conversation
 */

import { supabaseAdmin } from '../lib/supabase/admin';

// Simulate the old race condition method
async function claimConversationOldMethod(workerId: string): Promise<any[]> {
  const { data: conversations, error } = await supabaseAdmin
    .from('conversations')
    .update({ 
      processing_status: 'processing',
      processing_started_at: new Date().toISOString()
    })
    .eq('processing_status', 'pending')
    .select('id, user_id, youtube_video_id')
    .order('created_at', { ascending: true })
    .limit(1);

  if (error) {
    throw error;
  }

  return conversations || [];
}

// Use the new atomic method
async function claimConversationNewMethod(workerId: string): Promise<any[]> {
  const { data: conversations, error } = await supabaseAdmin
    .rpc('claim_pending_conversation', {
      worker_id: workerId
    });

  if (error) {
    throw error;
  }

  return conversations || [];
}

async function createTestConversation(): Promise<string> {
  const { data, error } = await supabaseAdmin
    .from('conversations')
    .insert({
      title: 'Test Race Condition',
      processing_status: 'pending',
      youtube_video_id: 'dQw4w9WgXcQ', // Rick Roll for testing
      is_anonymous: true,
      session_id: 'test-session'
    })
    .select('id')
    .single();

  if (error) {
    throw error;
  }

  return data.id;
}

async function resetConversation(conversationId: string) {
  await supabaseAdmin
    .from('conversations')
    .update({
      processing_status: 'pending',
      processing_started_at: null,
      worker_id: null
    })
    .eq('id', conversationId);
}

async function deleteTestConversation(conversationId: string) {
  await supabaseAdmin
    .from('conversations')
    .delete()
    .eq('id', conversationId);
}

async function testRaceCondition(method: 'old' | 'new', numWorkers: number = 5): Promise<{
  totalClaims: number;
  duplicateClaims: number;
  workerResults: { workerId: string; claimed: any[] }[];
}> {
  console.log(`\n🧪 Testing ${method} method with ${numWorkers} workers...`);
  
  // Create a test conversation
  const conversationId = await createTestConversation();
  console.log(`   Created test conversation: ${conversationId}`);
  
  try {
    // Simulate multiple workers trying to claim the same conversation simultaneously
    const workerPromises = [];
    for (let i = 0; i < numWorkers; i++) {
      const workerId = `test-worker-${i + 1}`;
      const claimMethod = method === 'old' ? claimConversationOldMethod : claimConversationNewMethod;
      workerPromises.push(
        claimMethod(workerId).then(claimed => ({ workerId, claimed }))
      );
    }
    
    // Wait for all workers to complete
    const workerResults = await Promise.all(workerPromises);
    
    // Count total claims and duplicates
    let totalClaims = 0;
    const claimedConversations = new Set();
    let duplicateClaims = 0;
    
    workerResults.forEach(result => {
      result.claimed.forEach(conv => {
        totalClaims++;
        if (claimedConversations.has(conv.id)) {
          duplicateClaims++;
        } else {
          claimedConversations.add(conv.id);
        }
      });
    });
    
    console.log(`   Results:`);
    console.log(`     Total claims: ${totalClaims}`);
    console.log(`     Unique conversations claimed: ${claimedConversations.size}`);
    console.log(`     Duplicate claims: ${duplicateClaims}`);
    
    workerResults.forEach(result => {
      console.log(`     ${result.workerId}: claimed ${result.claimed.length} conversations`);
    });
    
    return { totalClaims, duplicateClaims, workerResults };
    
  } finally {
    // Clean up
    await deleteTestConversation(conversationId);
  }
}

async function testAtomicRelease() {
  console.log(`\n🧪 Testing atomic conversation release...`);
  
  // Create a test conversation
  const conversationId = await createTestConversation();
  console.log(`   Created test conversation: ${conversationId}`);
  
  try {
    // Claim it with the new method
    const claimed = await claimConversationNewMethod('test-worker-release');
    
    if (claimed.length === 0) {
      console.log(`   ❌ Failed to claim conversation for release test`);
      return false;
    }
    
    console.log(`   ✅ Claimed conversation: ${claimed[0].id}`);
    
    // Try to release it
    const { data, error } = await supabaseAdmin
      .rpc('release_conversation', {
        conversation_id: claimed[0].id,
        worker_id: 'test-worker-release'
      });
    
    if (error) {
      console.log(`   ❌ Failed to release conversation: ${error.message}`);
      return false;
    }
    
    console.log(`   ✅ Successfully released conversation`);
    
    // Verify it's back to pending
    const { data: conversation, error: checkError } = await supabaseAdmin
      .from('conversations')
      .select('processing_status, worker_id')
      .eq('id', claimed[0].id)
      .single();
    
    if (checkError) {
      console.log(`   ❌ Failed to check conversation status: ${checkError.message}`);
      return false;
    }
    
    if (conversation.processing_status === 'pending' && !conversation.worker_id) {
      console.log(`   ✅ Conversation is back to pending status`);
      return true;
    } else {
      console.log(`   ❌ Conversation status: ${conversation.processing_status}, worker: ${conversation.worker_id}`);
      return false;
    }
    
  } finally {
    // Clean up
    await deleteTestConversation(conversationId);
  }
}

async function main() {
  console.log('🚀 Starting race condition tests...');
  
  try {
    // Test 1: Check if new method is available
    console.log('\n📋 Checking if atomic claiming function is available...');
    try {
      await supabaseAdmin.rpc('claim_pending_conversation', { worker_id: 'test' });
      console.log('   ✅ Atomic claiming function is available');
    } catch (error: any) {
      if (error.message?.includes('function claim_pending_conversation') || error.code === '42883') {
        console.log('   ❌ Atomic claiming function is NOT available');
        console.log('   Please run the migration first: npm run fix-race-condition');
        return;
      }
    }
    
    // Test 2: Test the old method (should show race condition)
    console.log('\n🔴 Testing OLD method (should show race condition):');
    const oldResults = await testRaceCondition('old', 5);
    
    // Test 3: Test the new method (should prevent race condition)
    console.log('\n🟢 Testing NEW method (should prevent race condition):');
    const newResults = await testRaceCondition('new', 5);
    
    // Test 4: Test atomic release
    const releaseTest = await testAtomicRelease();
    
    // Summary
    console.log('\n📊 Test Summary:');
    console.log(`   Old method duplicate claims: ${oldResults.duplicateClaims}`);
    console.log(`   New method duplicate claims: ${newResults.duplicateClaims}`);
    console.log(`   Atomic release test: ${releaseTest ? 'PASSED' : 'FAILED'}`);
    
    if (newResults.duplicateClaims === 0 && releaseTest) {
      console.log('\n🎉 All tests passed! Race condition is fixed.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check the implementation.');
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

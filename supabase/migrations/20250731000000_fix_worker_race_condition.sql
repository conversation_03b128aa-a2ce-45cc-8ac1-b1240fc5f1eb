-- Fix race condition in worker processing by implementing atomic conversation claiming
-- This migration adds a PostgreSQL function that uses FOR UPDATE SKIP LOCKED to prevent
-- multiple workers from claiming the same conversation

-- Add worker_id column to track which worker is processing each conversation
ALTER TABLE conversations 
ADD COLUMN IF NOT EXISTS worker_id VARCHAR(50);

-- Add index on processing_status for faster worker queries
CREATE INDEX IF NOT EXISTS idx_conversations_processing_status ON conversations(processing_status);

-- Add composite index for worker queries (processing_status + created_at)
CREATE INDEX IF NOT EXISTS idx_conversations_worker_queue ON conversations(processing_status, created_at) 
WHERE processing_status IN ('pending', 'processing');

-- Create atomic conversation claiming function
CREATE OR REPLACE FUNCTION claim_pending_conversation(worker_id TEXT)
RETURNS TABLE(
  id UUID,
  user_id UUID,
  youtube_video_id VARCHAR(11)
) 
LANGUAGE plpgsql
AS $$
DECLARE
  claimed_conversation RECORD;
BEGIN
  -- Use FOR UPDATE SKIP LOCKED to atomically claim a conversation
  -- This prevents race conditions by locking the row before updating
  SELECT c.id, c.user_id, c.youtube_video_id
  INTO claimed_conversation
  FROM conversations c
  WHERE c.processing_status = 'pending'
  ORDER BY c.created_at ASC
  FOR UPDATE SKIP LOCKED
  LIMIT 1;
  
  -- If we found a conversation, claim it
  IF FOUND THEN
    -- Update the conversation to mark it as being processed by this worker
    UPDATE conversations 
    SET 
      processing_status = 'processing',
      processing_started_at = NOW(),
      worker_id = claim_pending_conversation.worker_id
    WHERE conversations.id = claimed_conversation.id;
    
    -- Return the claimed conversation
    RETURN QUERY SELECT 
      claimed_conversation.id,
      claimed_conversation.user_id,
      claimed_conversation.youtube_video_id;
  END IF;
  
  -- If no conversation was found/claimed, return empty result
  RETURN;
END;
$$;

-- Create function to release a conversation back to pending state
-- This is useful for error recovery or worker shutdown
CREATE OR REPLACE FUNCTION release_conversation(conversation_id UUID, worker_id TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
  -- Only allow the worker that claimed the conversation to release it
  UPDATE conversations 
  SET 
    processing_status = 'pending',
    processing_started_at = NULL,
    worker_id = NULL
  WHERE 
    id = conversation_id 
    AND conversations.worker_id = release_conversation.worker_id
    AND processing_status = 'processing';
  
  -- Return true if a row was updated (conversation was released)
  RETURN FOUND;
END;
$$;

-- Create function to clean up stale processing conversations
-- This handles cases where workers crash or are forcefully terminated
CREATE OR REPLACE FUNCTION cleanup_stale_conversations(timeout_minutes INTEGER DEFAULT 30)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
  cleaned_count INTEGER;
BEGIN
  -- Reset conversations that have been processing for too long
  UPDATE conversations 
  SET 
    processing_status = 'pending',
    processing_started_at = NULL,
    worker_id = NULL,
    processing_error = 'Processing timed out - worker may have crashed'
  WHERE 
    processing_status = 'processing'
    AND processing_started_at < NOW() - INTERVAL '1 minute' * timeout_minutes;
  
  GET DIAGNOSTICS cleaned_count = ROW_COUNT;
  
  RETURN cleaned_count;
END;
$$;

-- Grant execute permissions on the functions
GRANT EXECUTE ON FUNCTION claim_pending_conversation(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION release_conversation(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_stale_conversations(INTEGER) TO authenticated;

-- Also grant to anon for anonymous conversations
GRANT EXECUTE ON FUNCTION claim_pending_conversation(TEXT) TO anon;
GRANT EXECUTE ON FUNCTION release_conversation(UUID, TEXT) TO anon;
GRANT EXECUTE ON FUNCTION cleanup_stale_conversations(INTEGER) TO anon;

-- Show completion message
SELECT 'Worker race condition fix migration completed successfully!' AS status;

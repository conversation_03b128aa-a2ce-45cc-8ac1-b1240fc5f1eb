import { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { User } from '@supabase/supabase-js';

export interface AuthContext {
  user: User | null;
  sessionId: string | null;
  isAuthenticated: boolean;
  isAnonymous: boolean;
}

export interface ConversationFilter {
  user_id?: string;
  session_id?: string;
  is_anonymous?: boolean;
}

/**
 * Get authentication context from request
 */
export async function getAuthContext(request: NextRequest): Promise<AuthContext> {
  const supabase = await createClient();
  const sessionId = request.headers.get('x-session-id');
  
  // Try to get authenticated user
  const { data: { user }, error } = await supabase.auth.getUser();
  
  const isAuthenticated = !error && !!user;
  const isAnonymous = !isAuthenticated && !!sessionId;

  return {
    user: isAuthenticated ? user : null,
    sessionId,
    isAuthenticated,
    isAnonymous
  };
}

/**
 * Validate that request has either valid authentication or valid session
 */
export function validateAccess(authContext: AuthContext): { isValid: boolean; error?: string } {
  if (authContext.isAuthenticated) {
    return { isValid: true };
  }
  
  if (authContext.isAnonymous && authContext.sessionId) {
    return { isValid: true };
  }
  
  return { 
    isValid: false, 
    error: 'Authentication required. Please log in or provide a valid session.' 
  };
}

/**
 * Get database filter for conversations based on auth context
 */
export function getConversationFilter(authContext: AuthContext): ConversationFilter {
  if (authContext.isAuthenticated && authContext.user) {
    return { user_id: authContext.user.id };
  }
  
  if (authContext.isAnonymous && authContext.sessionId) {
    return { 
      session_id: authContext.sessionId, 
      is_anonymous: true 
    };
  }
  
  throw new Error('Invalid auth context for conversation filter');
}

/**
 * Get user ID for database operations (authenticated users only)
 */
export function getUserId(authContext: AuthContext): string {
  if (!authContext.isAuthenticated || !authContext.user) {
    throw new Error('User ID only available for authenticated users');
  }
  return authContext.user.id;
}

/**
 * Check if user owns a conversation
 */
export function matchesConversationOwnership(
  conversation: { user_id?: string; session_id?: string; is_anonymous?: boolean },
  authContext: AuthContext
): boolean {
  if (authContext.isAuthenticated && authContext.user) {
    return conversation.user_id === authContext.user.id;
  }
  
  if (authContext.isAnonymous && authContext.sessionId) {
    return conversation.session_id === authContext.sessionId && conversation.is_anonymous === true;
  }
  
  return false;
}

/**
 * Create conversation data for insertion
 */
export function createConversationData(
  authContext: AuthContext,
  additionalData: Record<string, any> = {}
): Record<string, any> {
  const baseData = {
    title: 'Processing…',
    is_pinned: false,
    ...additionalData
  };

  if (authContext.isAuthenticated && authContext.user) {
    return {
      ...baseData,
      user_id: authContext.user.id,
      is_anonymous: false
    };
  }
  
  if (authContext.isAnonymous && authContext.sessionId) {
    return {
      ...baseData,
      session_id: authContext.sessionId,
      is_anonymous: true,
      created_anonymously_at: new Date().toISOString()
    };
  }
  
  throw new Error('Invalid auth context for conversation creation');
}

/**
 * Create message data for insertion
 */
export function createMessageData(
  authContext: AuthContext,
  conversationId: string,
  messageNumber: number,
  content: string,
  isUserMessage: boolean
): Record<string, any> {
  const baseData = {
    conversation_id: conversationId,
    message_number: messageNumber,
    content,
    user_message: isUserMessage
  };

  if (authContext.isAuthenticated && authContext.user) {
    return {
      ...baseData,
      user_id: authContext.user.id
    };
  }
  
  if (authContext.isAnonymous) {
    // For anonymous users, we don't set user_id
    // The message is associated with the conversation which has the session_id
    return baseData;
  }
  
  throw new Error('Invalid auth context for message creation');
}

import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Define a type for your product structure, matching the Supabase table exactly
// This should be kept in sync with your table DDL and potentially shared
export interface ProductMeta {
  stripe_product_id: string; // Primary Key
  name: string;
  description?: string | null;
  active: boolean; // Reflects Stripe product status
  image_url?: string | null;
  default_price_id?: string | null; // Stripe Price ID
  is_visible_on_subscribe_page: boolean;
  exclusivity_group?: string | null;
  display_order: number;
  // created_at and updated_at are handled by db defaults/triggers
}

let supabaseAdmin: SupabaseClient;

export const initializeSupabaseAdmin = () => {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL is not set in environment variables.');
  }
  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is not set in environment variables.');
  }
  if (!supabaseAdmin) {
    supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          persistSession: false, // Service role clients don't need to persist sessions
          autoRefreshToken: false,
        }
      }
    );
  }
  return supabaseAdmin;
};

// Initialize and export the admin client directly
const adminClient = initializeSupabaseAdmin();
export { adminClient as supabaseAdmin };
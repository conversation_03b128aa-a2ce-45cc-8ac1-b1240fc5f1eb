import { supabaseAdmin } from '@/lib/supabase/admin';
import { cancellationManager } from './cancellationManager';

const SUPADATA_API_KEY = process.env.SUPADATA_API_KEY;
const DIFY_API_KEY_OPTIMIZE = process.env.DIFY_API_KEY_OPTIMIZE;
const DIFY_API_KEY_SUMMARY = process.env.DIFY_API_KEY_SUMMARY;
const DIFY_API_KEY_RENAME = process.env.DIFY_API_KEY_RENAME;
const DIFY_BASE_URL = 'http://localhost/v1';

function validateYouTubeUrl(url: string): string | null {
  const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/);
  return match ? match[1] : null;
}

export class TranscriptService {
  async fetchTranscript(conversationId: string, youtubeUrl: string): Promise<any> {
    console.log(`[TS] Fetching transcript for ${conversationId}`);
    
    const videoId = validateYouTubeUrl(youtubeUrl);
    if (!videoId) {
      throw new Error('Invalid YouTube URL format provided.');
    }

    if (!SUPADATA_API_KEY) {
      throw new Error('Supadata API key not configured.');
    }
    
    // Create AbortController for cancellation support
    const abortController = new AbortController();
    cancellationManager.registerAbortController(conversationId, abortController);
    
    // Fetch both transcript and metadata in parallel from Supadata
    console.log(`[TS] Fetching data from Supadata for ${videoId}`);
    const [transcriptResponse, metadataResponse] = await Promise.all([
      fetch(`https://api.supadata.ai/v1/youtube/transcript?videoId=${videoId}`, { 
        headers: { 'x-api-key': SUPADATA_API_KEY },
        signal: abortController.signal
      }),
      fetch(`https://api.supadata.ai/v1/youtube/video?id=${videoId}`, {
        headers: { 'x-api-key': SUPADATA_API_KEY },
        signal: abortController.signal
      })
    ]);

    if (!transcriptResponse.ok) {
      console.error(`[TS] Supadata transcript API failed: ${transcriptResponse.status} - ${transcriptResponse.statusText}`);
      throw new Error('Failed to fetch transcript.');
    }

    if (!metadataResponse.ok) {
      console.error(`[TS] Supadata metadata API failed: ${metadataResponse.status} - ${metadataResponse.statusText}`);
      throw new Error('Failed to fetch video metadata.');
    }
    
    const transcriptData = await transcriptResponse.json();
    const metadataData = await metadataResponse.json();
    
    // Create the combined JSON following the provided schema
    const combinedData = {
      videoId: videoId,
      metadata: {
        id: `metadata-${videoId}-${Date.now()}`,
        title: metadataData.title || '',
        duration: metadataData.duration || 0,
        video_id: videoId,
        channel_id: metadataData.channel?.id || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        view_count: metadataData.viewCount || 0,
        like_count: metadataData.likeCount || 0,
        description: metadataData.description || '',
        upload_date: metadataData.uploadDate || new Date().toISOString(),
        channel_name: metadataData.channel?.name || '',
        thumbnail_url: metadataData.thumbnail || '',
        transcript_languages: metadataData.transcriptLanguages || [],
        tags: metadataData.tags || [],
        raw_metadata: metadataData
      },
      combinedAt: new Date().toISOString(),
      transcript: {
        lang: transcriptData.lang || 'en',
        content: transcriptData.content || transcriptData || []
      }
    };

    // Update conversation with the combined data using admin client
    await supabaseAdmin.from('conversations').update({
      youtube_video_id: videoId,
      transcript: combinedData,
      updated_at: new Date().toISOString()
    }).eq('id', conversationId);

    return {
      success: true,
      transcriptLength: JSON.stringify(combinedData.transcript).length,
      metadata: combinedData.metadata
    };
  }

  async generateTitle(conversationId: string, videoTitle: string, userId: string): Promise<void> {
    if (!DIFY_API_KEY_RENAME) {
      console.warn('[TS] Dify rename API key not configured. Using simple title.');
      // Fallback to simple title generation
      const cleanTitle = videoTitle.length > 50 ? videoTitle.substring(0, 47) + '...' : videoTitle;
      await supabaseAdmin.from('conversations').update({ title: cleanTitle }).eq('id', conversationId);
      return;
    }

    try {
      console.log(`[TS] Generating smart title for conversation ${conversationId}`);
      const difyResponse = await fetch(`${DIFY_BASE_URL}/workflows/run`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${DIFY_API_KEY_RENAME}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          inputs: {
            title: videoTitle
          },
          response_mode: 'blocking',
          user: userId
        })
      });

      if (!difyResponse.ok) {
        const errorText = await difyResponse.text();
        console.error(`[TS] Dify title generation API error: ${difyResponse.status} - ${errorText}`);
        throw new Error('Failed to generate chat title from Dify.');
      }

      const difyData = await difyResponse.json();
      let generatedTitle = 'Processing…';
      
      if (difyData.data?.outputs) {
        const outputs = difyData.data.outputs;
        generatedTitle = outputs.title || outputs.text || outputs.result || outputs.answer || 'Processing…';
      }

      generatedTitle = generatedTitle.toString().replace(/^["']|["']$/g, '').trim();
      
      if (!generatedTitle || generatedTitle === '') {
        generatedTitle = videoTitle; // Fallback to original title if Dify returns empty
      }

      console.log(`[TS] Generated title: "${generatedTitle}"`);

      await supabaseAdmin
        .from('conversations')
        .update({ title: generatedTitle })
        .eq('id', conversationId);

    } catch (error) {
      console.error(`[TS] Error generating smart title for ${conversationId}:`, error);
      // Fallback to simple title on error
      const cleanTitle = videoTitle.length > 50 ? videoTitle.substring(0, 47) + '...' : videoTitle;
      await supabaseAdmin.from('conversations').update({ title: cleanTitle }).eq('id', conversationId);
    }
  }

  async optimizeTranscript(conversationId: string, userId: string): Promise<AsyncGenerator<any, void, unknown>> {
    console.log(`[TS] Optimizing transcript for ${conversationId}`);
    
    // Check for cancellation before starting
    cancellationManager.checkCancellation(conversationId);
    
    // First, fetch the raw transcript from the database
    const { data: conversationData, error: fetchError } = await supabaseAdmin
      .from('conversations')
      .select('transcript')
      .eq('id', conversationId)
      .single();

    if (fetchError || !conversationData?.transcript) {
      throw new Error('Conversation or transcript not found');
    }

    const fullTranscriptJson = conversationData.transcript;

    console.log(`[TS] Raw transcript stats: size=${JSON.stringify(fullTranscriptJson).length}, keys=[${Object.keys(fullTranscriptJson).join(',')}]`);

    if (!DIFY_API_KEY_OPTIMIZE) {
      throw new Error('Dify API credentials not configured');
    }

    // Create AbortController for cancellation support
    const abortController = new AbortController();
    cancellationManager.registerAbortController(conversationId, abortController);
    
    cancellationManager.checkCancellation(conversationId);

    const requestBody = {
      inputs: {
        "transcript": JSON.stringify(fullTranscriptJson)
      },
      query: "Please optimize this transcript for better readability while maintaining all important information.",
      response_mode: 'streaming',
      user: userId,
      conversation_id: '',
    };

    console.log(`[TS] Dify API request: size=${JSON.stringify(requestBody).length}, userId=${userId}`);

    const response = await fetch(`${DIFY_BASE_URL}/chat-messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${DIFY_API_KEY_OPTIMIZE}`,
      },
      body: JSON.stringify(requestBody),
      signal: abortController.signal
    });

    console.log(`[TS] Dify API response: status=${response.status}, ok=${response.ok}`);

    if (!response.ok) {
      let errorText = 'Unknown error';
      try {
        errorText = await response.text();
      } catch (e) {
        console.error(`[TS] Failed to read error response for ${conversationId}:`, e);
      }
      console.error(`[TS] Dify API error for ${conversationId}:`, {
        status: response.status,
        statusText: response.statusText,
        errorText: errorText.substring(0, 500) // Limit error text length
      });
      throw new Error(`Dify API error ${response.status}: ${errorText}`);
    }

    if (!response.body) {
      throw new Error('No response body from Dify API');
    }

    return this.processOptimizeStream(response.body, conversationId, userId);
  }

  private async *processOptimizeStream(stream: ReadableStream, conversationId: string, userId: string): AsyncGenerator<any, void, unknown> {
    const reader = stream.getReader();
    const decoder = new TextDecoder();
    let accumulatedAnswer = '';
    let difyConversationId = '';
    let eventCount = 0;
    let messageCount = 0;
    let buffer = ''; // Buffer to handle partial JSON lines

    console.log(`[TS] Starting optimization stream for ${conversationId}`);

    try {
      while (true) {
        // Check for cancellation during stream processing
        cancellationManager.checkCancellation(conversationId);
        
        const { done, value } = await reader.read();
        if (done) {
          process.stdout.write('\n'); // New line after progress indicator
          console.log(`[TS] Stream ended for conversation ${conversationId}, processed ${eventCount} events, ${messageCount} message events`);
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        
        // Split by double newlines to get complete events
        const parts = buffer.split('\n\n');
        
        // Keep the last part in buffer (might be incomplete)
        buffer = parts.pop() || '';
        
        for (const part of parts) {
          if (!part.trim()) continue;
          
          const lines = part.split('\n');
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const jsonStr = line.slice(6).trim();
                if (!jsonStr) continue;
                
                const data = JSON.parse(jsonStr);
                eventCount++;
                
                if (data.event === 'message' && data.answer) {
                  messageCount++;
                  accumulatedAnswer += data.answer;
                }

                process.stdout.write(`\r[TS] Optimizing... Events: ${eventCount}, Chunks: ${messageCount}, Size: ${accumulatedAnswer.length}  `);
                
                yield data; // Yield the event data
                
                if (data.conversation_id && !difyConversationId) {
                  difyConversationId = data.conversation_id;
                  process.stdout.write('\n');
                  console.log(`[TS] Dify conversation ID: ${difyConversationId}`);
                }

                // Handle error events by yielding them as special error events
                if (data.event === 'error') {
                  process.stdout.write('\n');
                  console.error(`[TS] Dify error: ${data.message || 'Unknown'}, code=${data.code}`);
                  yield {
                    event: 'dify_error',
                    error: data.message || 'Unknown Dify error',
                    errorCode: data.code,
                    errorStatus: data.status,
                    conversationId: data.conversation_id,
                    messageId: data.message_id,
                    originalEvent: data
                  };
                }
              } catch (parseError) {
                process.stdout.write('\n');
                console.warn(`[TS] Failed to parse optimization stream line for ${conversationId}:`, {
                  line: line.substring(0, 200),
                  fullLine: line, // Log the full line for debugging
                  lineLength: line.length,
                  error: parseError instanceof Error ? parseError.message : parseError,
                  eventCount: eventCount
                });
              }
            }
          }
        }
      }

      // Process any remaining buffer content
      if (buffer.trim()) {
        console.log(`[TS] Processing remaining buffer for ${conversationId}:`, buffer.substring(0, 100));
        const lines = buffer.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.slice(6).trim();
              if (!jsonStr) continue;
              
              const data = JSON.parse(jsonStr);
              eventCount++;
              
              console.log(`[TS] Final optimization event ${eventCount} for ${conversationId}:`, {
                event: data.event,
                hasAnswer: !!data.answer,
                answerLength: data.answer?.length || 0,
                conversationId: data.conversation_id,
                status: data.data?.status,
                message: data.message,
                code: data.code,
                taskId: data.task_id,
                messageId: data.message_id,
                nodeId: data.data?.node_id,
                nodeType: data.data?.node_type,
                nodeTitle: data.data?.title,
                nodeError: data.data?.error,
                workflowId: data.data?.workflow_id,
                workflowRunId: data.workflow_run_id,
                fullData: data // Log the full event for debugging
              });
              
              yield data;
              
              if (data.event === 'message' && data.answer) {
                messageCount++;
                accumulatedAnswer += data.answer;
                console.log(`[TS] Final message ${messageCount} accumulated, total length: ${accumulatedAnswer.length}`);
              }
              
              if (data.conversation_id && !difyConversationId) {
                difyConversationId = data.conversation_id;
                console.log(`[TS] Got final Dify conversation ID: ${difyConversationId}`);
              }
            } catch (parseError) {
              console.warn(`[TS] Failed to parse final optimization stream line for ${conversationId}:`, {
                line: line.substring(0, 200),
                fullLine: line, // Log the full line for debugging
                lineLength: line.length,
                error: parseError instanceof Error ? parseError.message : parseError,
                eventCount: eventCount
              });
            }
          }
        }
      }

      process.stdout.write('\n'); // Ensure we are on a new line before final log

      console.log(`[TS] Final optimization results for ${conversationId}:`, {
        accumulatedAnswerLength: accumulatedAnswer.length,
        difyConversationId,
        messageCount,
        eventCount
      });

      // Store optimized transcript in database
      if (accumulatedAnswer) {
        console.log(`[TS] Storing optimized transcript: size=${accumulatedAnswer.length}`);
        
        const { data, error } = await supabaseAdmin
          .from('conversations')
          .update({ 
            optimized_transcript: accumulatedAnswer,
            dify_transcript_id: difyConversationId
          })
          .eq('id', conversationId)
          .select('id, optimized_transcript');

        if (error) {
          console.error(`[TS] Error storing optimized transcript for ${conversationId}:`, error);
          throw error;
        } else {
          console.log(`[TS] Stored transcript: records=${data?.length}, size=${data?.[0]?.optimized_transcript?.length || 0}`);
        }
      } else {
        console.warn(`[TS] No accumulated answer to store`);
      }

    } catch (streamError) {
      console.error(`[TS] Stream error: ${streamError}`);
      throw streamError;
    } finally {
      try {
        reader.releaseLock();
      } catch (e) {
        console.warn(`[TS] Error releasing lock: ${e}`);
      }
    }
  }

  async generateSummary(conversationId: string, userId: string): Promise<AsyncGenerator<any, void, unknown>> {
    console.log(`[TS] Generating summary for ${conversationId}`);
    
    // Check for cancellation before starting
    cancellationManager.checkCancellation(conversationId);
    
    // Fetch the optimized transcript from the database
    console.log(`[TS] Fetching optimized transcript`);
    const { data: conversationData, error: fetchError } = await supabaseAdmin
      .from('conversations')
      .select('optimized_transcript, dify_transcript_id, transcript')
      .eq('id', conversationId)
      .single();

    console.log(`[TS] Fetch result: size=${conversationData?.optimized_transcript?.length || 0}, difyId=${conversationData?.dify_transcript_id}`);

    if (fetchError) {
      console.error(`[TS] DB error: ${fetchError.message}`);
      throw new Error(`Database error: ${fetchError.message}`);
    }

    if (!conversationData) {
      console.error(`[TS] No conversation data found`);
      throw new Error('Conversation not found');
    }

    if (!conversationData?.optimized_transcript) {
      console.error(`[TS] No optimized transcript found. Raw transcript exists: ${!!conversationData.transcript}`);
      throw new Error('Optimized transcript not found');
    }

    const optimizedTranscript = conversationData.optimized_transcript;

    // Convert to plain text if needed
    const plainTextOptimizedTranscript = typeof optimizedTranscript === 'string' 
      ? optimizedTranscript 
      : JSON.stringify(optimizedTranscript);

    if (!DIFY_API_KEY_SUMMARY) {
      throw new Error('Dify API credentials not configured');
    }

    // Create AbortController for cancellation support
    const abortController = new AbortController();
    cancellationManager.registerAbortController(conversationId, abortController);
    
    cancellationManager.checkCancellation(conversationId);

    const response = await fetch(`${DIFY_BASE_URL}/chat-messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${DIFY_API_KEY_SUMMARY}`,
      },
      body: JSON.stringify({
        inputs: {
          "transcript_string": plainTextOptimizedTranscript,
          "message_type": 'first'
        },
        query: "Please provide a comprehensive summary of this transcript.",
        response_mode: 'streaming',
        user: userId,
        conversation_id: '',
      }),
      signal: abortController.signal
    });

    if (!response.ok || !response.body) {
      throw new Error('Failed to process with Dify API');
    }

    return this.processSummaryStream(response.body, conversationId, userId);
  }

  private async *processSummaryStream(stream: ReadableStream, conversationId: string, userId: string): AsyncGenerator<any, void, unknown> {
    const reader = stream.getReader();
    const decoder = new TextDecoder();
    let accumulatedAnswer = '';
    let difyConversationId = '';
    let eventCount = 0;
    let messageCount = 0;
    let buffer = ''; // Buffer to handle partial JSON lines

    console.log(`[TS] Starting summary stream for ${conversationId}`);
    
    try {
      while (true) {
        // Check for cancellation during stream processing
        cancellationManager.checkCancellation(conversationId);
        
        const { done, value } = await reader.read();
        if (done) {
          process.stdout.write('\n');
          console.log(`[TS] Summary stream ended for conversation ${conversationId}, processed ${eventCount} events, ${messageCount} message events`);
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        
        // Split by double newlines to get complete events
        const parts = buffer.split('\n\n');
        
        // Keep the last part in buffer (might be incomplete)
        buffer = parts.pop() || '';
        
        for (const part of parts) {
          if (!part.trim()) continue;
          
          const lines = part.split('\n');
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const jsonStr = line.slice(6).trim();
                if (!jsonStr) continue;
                
                const data = JSON.parse(jsonStr);
                eventCount++;

                if (data.event === 'message' && data.answer) {
                  messageCount++;
                  accumulatedAnswer += data.answer;
                }

                process.stdout.write(`\r[TS] Summarizing... Events: ${eventCount}, Chunks: ${messageCount}, Size: ${accumulatedAnswer.length}  `);

                // Only yield node status events for progress tracking, not message content
                if (data.event === 'node_started' || data.event === 'workflow_started' || data.event === 'workflow_finished') {
                  yield data;
                } else if (data.event === 'node_finished' && data.data?.status) {
                  yield data;
                }
                
                if (data.conversation_id && !difyConversationId) {
                  difyConversationId = data.conversation_id;
                  process.stdout.write('\n');
                  console.log(`[TS] Dify summary ID: ${difyConversationId}`);
                }

                // Handle error events by yielding them as special error events
                if (data.event === 'error') {
                  process.stdout.write('\n');
                  console.error(`[TS] Dify error during summary: ${data.message || 'Unknown'}, code=${data.code}`);
                  yield {
                    event: 'dify_error',
                    error: data.message || 'Unknown Dify error',
                    errorCode: data.code,
                    errorStatus: data.status,
                    conversationId: data.conversation_id,
                    messageId: data.message_id,
                    originalEvent: data
                  };
                }
              } catch (parseError) {
                process.stdout.write('\n');
                console.warn(`[TS] Parse error: ${parseError instanceof Error ? parseError.message : parseError}`);
              }
            }
          }
        }
      }

      // Process any remaining buffer content
      if (buffer.trim()) {
        process.stdout.write('\n');
        console.log(`[TS] Processing remaining summary buffer for ${conversationId}:`, buffer.substring(0, 100));
        const lines = buffer.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.slice(6).trim();
              if (!jsonStr) continue;
              
              const data = JSON.parse(jsonStr);
              eventCount++;
              
              console.log(`[TS] Final summary event ${eventCount} for ${conversationId}:`, {
                event: data.event,
                hasAnswer: !!data.answer,
                answerLength: data.answer?.length || 0,
                conversationId: data.conversation_id,
                status: data.data?.status
              });
              
              // Only yield node status events for progress tracking, not message content
              if (data.event === 'node_started' || data.event === 'workflow_started' || data.event === 'workflow_finished') {
                yield data;
              } else if (data.event === 'node_finished' && data.data?.status) {
                yield data;
              }
              
              if (data.event === 'message' && data.answer) {
                messageCount++;
                accumulatedAnswer += data.answer;
                console.log(`[TS] Final summary message ${messageCount} accumulated, total length: ${accumulatedAnswer.length}`);
              }
              
              if (data.conversation_id && !difyConversationId) {
                difyConversationId = data.conversation_id;
                console.log(`[TS] Got final Dify summary conversation ID: ${difyConversationId}`);
              }
            } catch (parseError) {
              console.warn(`[TS] Failed to parse final summary stream line:`, {
                line: line.substring(0, 150),
                error: parseError instanceof Error ? parseError.message : parseError
              });
            }
          }
        }
      }
      
      process.stdout.write('\n');

      console.log(`[TS] Final summary results for ${conversationId}:`, {
        accumulatedAnswerLength: accumulatedAnswer.length,
        difyConversationId,
        messageCount,
        eventCount
      });

      // Store summary in the content table
      if (accumulatedAnswer && difyConversationId) {
        console.log(`[TS] Storing summary: size=${accumulatedAnswer.length}`);
        
        // First check if summary already exists to avoid duplicate attempts
        const { data: existingContent, error: checkError } = await supabaseAdmin
          .from('content')
          .select('id, content')
          .eq('conversation_id', conversationId)
          .eq('version_number', 1)
          .single();

        if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found"
          console.error(`[TS] Error checking existing summary for ${conversationId}:`, checkError);
        }

        if (existingContent) {
          console.log(`[TS] Summary already exists for ${conversationId}, updating instead`);
          // Update existing summary
          const { data: updateData, error: updateError } = await supabaseAdmin
            .from('content')
            .update({
              content: { content: accumulatedAnswer }
            })
            .eq('conversation_id', conversationId)
            .eq('version_number', 1)
            .select('id, conversation_id, content');

          if (updateError) {
            console.error(`[TS] Error updating existing summary for ${conversationId}:`, updateError);
            throw updateError;
          } else {
            console.log(`[TS] Successfully updated summary: records=${updateData?.length}, size=${JSON.stringify(updateData?.[0]?.content || {}).length}`);
          }
        } else {
          // Insert new summary with better error handling
          const { data: insertData, error: insertError } = await supabaseAdmin
            .from('content')
            .insert({
              conversation_id: conversationId,
              content_id: conversationId,
              version_number: 1,
              content: { content: accumulatedAnswer },
              message_id: null
            })
            .select('id, conversation_id, content');

          if (insertError) {
            // Check if it's a duplicate key error (which might happen due to race conditions)
            if (insertError.code === '23505' && insertError.message?.includes('unique_version_per_content')) {
              console.log(`[TS] Duplicate key detected for ${conversationId}, attempting update instead`);
              // Try to update the existing record
              const { data: retryUpdateData, error: retryUpdateError } = await supabaseAdmin
                .from('content')
                .update({
                  content: { content: accumulatedAnswer }
                })
                .eq('conversation_id', conversationId)
                .eq('version_number', 1)
                .select('id, conversation_id, content');

              if (retryUpdateError) {
                console.error(`[TS] Error in retry update for ${conversationId}:`, retryUpdateError);
                throw retryUpdateError;
              } else {
                console.log(`[TS] Successfully updated summary after duplicate key: records=${retryUpdateData?.length}, size=${JSON.stringify(retryUpdateData?.[0]?.content || {}).length}`);
              }
            } else {
              console.error(`[TS] Error inserting summary for ${conversationId}:`, insertError);
              throw insertError;
            }
          } else {
            console.log(`[TS] Successfully inserted summary: records=${insertData?.length}, size=${JSON.stringify(insertData?.[0]?.content || {}).length}`);
          }
        }

        // Update conversation with dify_summary_id
        const { data: updateData, error: updateError } = await supabaseAdmin
          .from('conversations')
          .update({ 
            dify_summary_id: difyConversationId
          })
          .eq('id', conversationId)
          .select('id, dify_summary_id');

        if (updateError) {
          console.error(`[TS] Error updating conversation with dify_summary_id for ${conversationId}:`, updateError);
          throw updateError;
        } else {
          console.log(`[TS] Updated conversation with difyId=${updateData?.[0]?.dify_summary_id}`);
        }
      } else {
        console.warn(`[TS] Missing data for storage: answer=${!!accumulatedAnswer}, difyId=${!!difyConversationId}`);
      }

    } catch (streamError) {
      console.error(`[TS] Summary stream error: ${streamError}`);
      throw streamError;
    } finally {
      try {
        reader.releaseLock();
      } catch (e) {
        console.warn(`[TS] Error releasing lock: ${e}`);
      }
    }
  }
} 
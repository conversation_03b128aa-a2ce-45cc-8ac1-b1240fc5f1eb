import { supabaseAdmin } from '@/lib/supabase/admin';
import { TranscriptService } from './transcriptService';
import { cancellationManager } from './cancellationManager';

export interface ConversationProgress {
  currentStep: 'fetching' | 'optimizing' | 'summarizing' | 'completed' | 'failed';
  transcriptProgress: number;
  optimizationProgress: number;
  summaryProgress: number;
  transcriptLength: number;
  currentStepText: string;
}

export class ConversationProcessor {
  private transcriptService = new TranscriptService();
  
  private getSupabase() {
    return supabaseAdmin;
  }

  async processConversation(conversationId: string, youtubeUrl: string, userId: string): Promise<void> {
    try {
      console.log(`[CP] Starting processing for ${conversationId}`);
      
      // Check for cancellation before starting
      cancellationManager.checkCancellation(conversationId);
      
      // Update progress (status already set to 'processing' by worker)
      await this.updateConversationProgress(conversationId, {
        currentStep: 'fetching',
        transcriptProgress: 0,
        optimizationProgress: 0,
        summaryProgress: 0,
        transcriptLength: 0,
        currentStepText: 'Fetching video data from YouTube...'
      });

      // Step 1: Fetch transcript and metadata
      console.log(`[CP] Step 1: Fetching transcript`);
      cancellationManager.checkCancellation(conversationId);
      await this.fetchTranscript(conversationId, youtubeUrl, userId);
      console.log(`[CP] Step 1 completed`);

      // Small delay to ensure database write is committed
      await new Promise(resolve => setTimeout(resolve, 1000));
      cancellationManager.checkCancellation(conversationId);

      // Step 2: Optimize transcript  
      console.log(`[CP] Step 2: Optimizing transcript`);
      cancellationManager.checkCancellation(conversationId);
      await this.optimizeTranscript(conversationId, userId);
      console.log(`[CP] Step 2 completed`);

      // Small delay to ensure database write is committed
      await new Promise(resolve => setTimeout(resolve, 1000));
      cancellationManager.checkCancellation(conversationId);

      // Step 3: Generate summary
      console.log(`[CP] Step 3: Generating summary`);
      cancellationManager.checkCancellation(conversationId);
      await this.generateSummary(conversationId, userId);
      console.log(`[CP] Step 3 completed`);

      // Final cancellation check before marking complete
      cancellationManager.checkCancellation(conversationId);

      // Mark as completed
      await this.updateConversationStatus(conversationId, 'completed', {
        currentStep: 'completed',
        transcriptProgress: 100,
        optimizationProgress: 100,
        summaryProgress: 100,
        transcriptLength: 0,
        currentStepText: 'All processing completed'
      });

      console.log(`[CP] Processing completed for ${conversationId}`);
      
      // Clean up cancellation tracking on successful completion
      cancellationManager.cleanup(conversationId);

    } catch (error) {
      console.error(`[CP] Error processing ${conversationId}:`, error);
      
      // Check if the error was due to cancellation
      if (error instanceof Error && error.message.includes('Processing cancelled')) {
        console.log(`[CP] Processing cancelled for ${conversationId}`);
        await this.updateConversationStatus(conversationId, 'failed', null, 'Processing was cancelled');
      } else {
        // Log the error details for better debugging
        const errorMessage = error instanceof Error ? error.message : 'Processing failed';
        const errorCode = (error as any)?.code;
        
        if (errorCode === '23505') {
          console.log(`[CP] Duplicate key constraint detected for ${conversationId}, this may indicate concurrent processing attempts`);
        }
        
        await this.updateConversationStatus(conversationId, 'failed', null, errorMessage);
      }
      
      // Clean up cancellation tracking on error/cancellation
      cancellationManager.cleanup(conversationId);
    }
  }

    private async fetchTranscript(conversationId: string, youtubeUrl: string, userId: string): Promise<void> {
    console.log(`[CP] Fetching transcript for ${conversationId}`);
    
    cancellationManager.checkCancellation(conversationId);
    const result = await this.transcriptService.fetchTranscript(conversationId, youtubeUrl);
    const transcriptLength = result.transcriptLength || 0;

    cancellationManager.checkCancellation(conversationId);
    
    // Update progress
    await this.updateConversationProgress(conversationId, {
      transcriptProgress: 100,
      transcriptLength,
      currentStepText: 'Video data fetched successfully'
    });

    // Generate title if we have metadata
    if (result.metadata?.title) {
      try {
        await this.transcriptService.generateTitle(conversationId, result.metadata.title, userId);
        // Trigger progress update to notify frontend of title change
        await this.updateConversationProgress(conversationId, {
          currentStepText: 'Title generated, continuing...'
        });
      } catch (titleError) {
        console.warn(`[CP] Title generation failed: ${titleError}`);
      }
    }
  }

  private async optimizeTranscript(conversationId: string, userId: string): Promise<void> {
    console.log(`[CP] Optimizing transcript for ${conversationId}`);
    
    cancellationManager.checkCancellation(conversationId);
    
    await this.updateConversationProgress(conversationId, {
      currentStep: 'optimizing',
      transcriptProgress: 100,
      optimizationProgress: 0,
      summaryProgress: 0,
      transcriptLength: 0,
      currentStepText: 'Starting transcript optimization'
    });

    try {
      cancellationManager.checkCancellation(conversationId);
      const streamGenerator = await this.transcriptService.optimizeTranscript(conversationId, userId);
      let eventCount = 0;

      // Process streaming response
      for await (const event of streamGenerator) {
        // Check for cancellation during streaming
        cancellationManager.checkCancellation(conversationId);
        
        eventCount++;
        try {
          process.stdout.write(`\r[CP] Optimization event ${eventCount}`);

          if (event.event === 'node_started') {
            await this.updateConversationProgress(conversationId, {
              currentStepText: event.data?.title || 'Optimizing...'
            });
          } else if (event.event === 'node_finished' && event.data?.status === 'succeeded') {
            await this.updateConversationProgress(conversationId, {
              optimizationProgress: Math.min(75, 100) // Increment progress
            });
          } else if (event.event === 'workflow_finished' && event.data?.status === 'succeeded') {
            await this.updateConversationProgress(conversationId, {
              optimizationProgress: 100,
              currentStepText: 'Transcript optimization completed'
            });
          } else if (event.event === 'dify_error') {
            process.stdout.write('\n');
            // Handle Dify-specific errors and update conversation status
            console.error(`[CP] Dify error: ${event.error || 'Unknown'}, code=${event.code}`);
            
            // Extract meaningful error message
            let errorMessage = event.error || 'Unknown Dify error';
            if (errorMessage.includes('429 RESOURCE_EXHAUSTED')) {
              errorMessage = 'API quota exceeded. Please try again later or upgrade your plan.';
            } else if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
              errorMessage = 'API authentication failed. Please check your API configuration.';
            } else if (errorMessage.includes('400') || errorMessage.includes('invalid_param')) {
              errorMessage = 'Invalid request parameters. Please try again.';
            }
            
            // Update conversation with the specific error
            await this.updateConversationStatus(conversationId, 'failed', null, errorMessage);
            
            // Throw error to stop processing
            throw new Error(errorMessage);
          }
        } catch (e) {
          process.stdout.write('\n');
          console.warn(`[CP] Failed to handle event ${eventCount}: ${e}`);
        }
      }

      process.stdout.write('\n');
      console.log(`[CP] Optimization stream completed: ${eventCount} events`);

        // Final check before verification
        cancellationManager.checkCancellation(conversationId);
        
        // Wait a bit longer to ensure database write is committed before verification
        await new Promise(resolve => setTimeout(resolve, 3000)); // 3 seconds
        
        // Verify optimization was stored (non-critical verification)
        try {
          await this.verifyOptimizationStored(conversationId);
        } catch (verificationError) {
          console.error(`[CP] Optimization verification failed: ${verificationError}`);
          // Don't fail the entire process, just log the verification failure
        }

    } catch (error) {
      console.error(`[CP] Error in optimization for ${conversationId}:`, error);
      throw error;
    }
  }

  private async verifyOptimizationStored(conversationId: string): Promise<void> {
    console.log(`[CP] Verifying optimization was stored for ${conversationId}`);
    
    const supabase = this.getSupabase();
    let attempts = 0;
    const maxAttempts = 5;
    const retryDelay = 2000; // 2 seconds

    while (attempts < maxAttempts) {
      attempts++;
      
      try {
        const { data, error } = await supabase
          .from('conversations')
          .select('optimized_transcript, dify_transcript_id')
          .eq('id', conversationId)
          .single();

        if (error) {
          console.error(`[CP] Error verifying optimization for ${conversationId} (attempt ${attempts}):`, error);
          if (attempts === maxAttempts) {
            console.error(`[CP] Final optimization verification error for ${conversationId}:`, error);
            return; // Don't fail the whole process
          }
        } else {
          console.log(`[CP] Verification attempt ${attempts}: size=${data?.optimized_transcript?.length || 0}, difyId=${data?.dify_transcript_id}`);

          if (data?.optimized_transcript) {
            console.log(`[CP] Verification successful`);
            return; // Success!
          }
          
          if (attempts === maxAttempts) {
            // Log warning but don't fail the entire process
            console.warn(`[CP] Verification failed: no transcript after ${maxAttempts} attempts`);
            return; // Don't fail the whole process
          }
        }
              } catch (verificationError) {
          if (attempts === maxAttempts) {
            console.error(`[CP] Verification error on final attempt for ${conversationId}:`, verificationError);
            return; // Don't fail the whole process
          }
        }

      // Wait before retrying
      console.log(`[CP] Retrying verification in ${retryDelay}ms (${attempts + 1}/${maxAttempts})`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  private async generateSummary(conversationId: string, userId: string): Promise<void> {
    console.log(`[CP] Generating summary for ${conversationId}`);
    
    cancellationManager.checkCancellation(conversationId);
    
    await this.updateConversationProgress(conversationId, {
      currentStep: 'summarizing',
      transcriptProgress: 100,
      optimizationProgress: 100,
      summaryProgress: 0,
      transcriptLength: 0,
      currentStepText: 'Starting summary generation'
    });

    try {
      cancellationManager.checkCancellation(conversationId);
      const streamGenerator = await this.transcriptService.generateSummary(conversationId, userId);
      let eventCount = 0;
      let messageCount = 0;

      // Process streaming response
      for await (const event of streamGenerator) {
        // Check for cancellation during streaming
        cancellationManager.checkCancellation(conversationId);
        
        eventCount++;
        try {
          process.stdout.write(`\r[CP] Summary event ${eventCount}, messages ${messageCount}`);
          
          if (event.event === 'node_started') {
            await this.updateConversationProgress(conversationId, {
              currentStepText: event.data?.title || 'Generating summary...'
            });
          } else if (event.event === 'message' && event.answer) {
            messageCount++;
            // Update progress incrementally without saving partial content
            await this.updateConversationProgress(conversationId, {
              summaryProgress: Math.min(95, await this.getCurrentProgress(conversationId, 'summaryProgress') + 1)
            });
          } else if (event.event === 'workflow_finished' && event.data?.status === 'succeeded') {
            await this.updateConversationProgress(conversationId, {
              summaryProgress: 100,
              currentStepText: 'Summary generation completed'
            });
          } else if (event.event === 'dify_error') {
            process.stdout.write('\n');
            // Handle Dify-specific errors and update conversation status
            console.error(`[CP] Dify error: ${event.error || 'Unknown'}, code=${event.code}`);
            
            // Extract meaningful error message
            let errorMessage = event.error || 'Unknown Dify error';
            if (errorMessage.includes('429 RESOURCE_EXHAUSTED')) {
              errorMessage = 'API quota exceeded. Please try again later or upgrade your plan.';
            } else if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
              errorMessage = 'API authentication failed. Please check your API configuration.';
            } else if (errorMessage.includes('400') || errorMessage.includes('invalid_param')) {
              errorMessage = 'Invalid request parameters. Please try again.';
            }
            
            // Update conversation with the specific error
            await this.updateConversationStatus(conversationId, 'failed', null, errorMessage);
            
            // Throw error to stop processing
            throw new Error(errorMessage);
          }
        } catch (e) {
          process.stdout.write('\n');
          console.warn(`[CP] Failed to handle event ${eventCount}: ${e}`);
        }
      }

      process.stdout.write('\n');
      console.log(`[CP] Summary stream completed: ${eventCount} events, ${messageCount} messages`);
      
              // Update progress with final completion status
        await this.updateConversationProgress(conversationId, {
          summaryProgress: 100,
          currentStepText: `Summary completed: ${eventCount} events processed`
        });

        // Wait to ensure database write is committed before verification
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2 seconds

        // Verify summary was stored (non-critical verification)
        try {
          await this.verifySummaryStored(conversationId);
        } catch (verificationError) {
          console.error(`[CP] Summary verification failed: ${verificationError}`);
          // Don't fail the entire process, just log the verification failure
        }

    } catch (error) {
      console.error(`[CP] Error in summary generation for ${conversationId}:`, error);
      throw error;
    }
  }

  private async verifySummaryStored(conversationId: string): Promise<void> {
    console.log(`[CP] Verifying summary was stored for ${conversationId}`);
    
    const supabase = this.getSupabase();
    let attempts = 0;
    const maxAttempts = 3;
    const retryDelay = 1000; // 1 second (shorter for summary as it's less critical)

    while (attempts < maxAttempts) {
      attempts++;
      
      try {
        const { data, error } = await supabase
          .from('content')
          .select('content, content_id')
          .eq('conversation_id', conversationId)
          .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
          console.error(`[CP] Error verifying summary for ${conversationId} (attempt ${attempts}):`, error);
          if (attempts === maxAttempts) {
            console.error(`[CP] Final summary verification error for ${conversationId}:`, error);
            return; // Don't fail the whole process
          }
        } else {
          console.log(`[CP] Summary verification attempt ${attempts}: size=${JSON.stringify(data?.content || {}).length}, id=${data?.content_id}`);

          if (data?.content) {
            console.log(`[CP] Summary verification successful`);
            return; // Success!
          }
          
          if (attempts === maxAttempts) {
            // For summary, just warn rather than fail completely
            console.warn(`[CP] No summary found after ${maxAttempts} attempts`);
            return; // Don't fail the whole process for missing summary
          }
        }
      } catch (verificationError) {
        if (attempts === maxAttempts) {
          // For summary verification, log error but don't fail the process
          console.error(`[CP] Summary verification failed: ${verificationError}`);
          return;
        }
      }

      // Wait before retrying
      console.log(`[CP] Retrying verification in ${retryDelay}ms (${attempts + 1}/${maxAttempts})`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  private async updateConversationStatus(
    conversationId: string, 
    status: string, 
    progress?: ConversationProgress | null, 
    error?: string
  ): Promise<void> {
    const updateData: any = {
      processing_status: status,
      updated_at: new Date().toISOString()
    };

    if (progress) {
      updateData.processing_progress = progress;
      updateData.current_step_text = progress.currentStepText;
    }

    if (error) {
      updateData.processing_error = error;
    }

    if (status === 'fetching' && !updateData.processing_started_at) {
      updateData.processing_started_at = new Date().toISOString();
    }

    if (status === 'completed' || status === 'failed') {
      updateData.processing_completed_at = new Date().toISOString();
    }

    const supabase = this.getSupabase();
    const { error: updateError } = await supabase
      .from('conversations')
      .update(updateData)
      .eq('id', conversationId);

    if (updateError) {
      console.error('Failed to update conversation status:', updateError);
    }
  }

  private async updateConversationProgress(conversationId: string, partialProgress: Partial<ConversationProgress>): Promise<void> {
    // Get current progress
    const supabase = this.getSupabase();
    const { data: conversation } = await supabase
      .from('conversations')
      .select('processing_progress')
      .eq('id', conversationId)
      .single();

    const currentProgress = conversation?.processing_progress || {};
    
    const newProgress = { ...currentProgress, ...partialProgress };

    await supabase
      .from('conversations')
      .update({ 
        processing_progress: newProgress,
        current_step_text: partialProgress.currentStepText || currentProgress.currentStepText
      })
      .eq('id', conversationId);
  }

  private async getCurrentProgress(conversationId: string, field: keyof ConversationProgress): Promise<number> {
    const supabase = this.getSupabase();
    const { data: conversation } = await supabase
      .from('conversations')
      .select('processing_progress')
      .eq('id', conversationId)
      .single();

    return conversation?.processing_progress?.[field] || 0;
  }
} 
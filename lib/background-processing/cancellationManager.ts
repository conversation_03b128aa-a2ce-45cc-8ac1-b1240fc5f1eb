export class CancellationManager {
  private static instance: CancellationManager;
  private cancelledConversations: Set<string> = new Set();
  private abortControllers: Map<string, AbortController> = new Map();

  static getInstance(): CancellationManager {
    if (!CancellationManager.instance) {
      CancellationManager.instance = new CancellationManager();
    }
    return CancellationManager.instance;
  }

  /**
   * Request cancellation for a conversation
   */
  cancelConversation(conversationId: string): void {
    console.log(`[CM] Cancelling ${conversationId}`);
    this.cancelledConversations.add(conversationId);
    
    // Abort any ongoing HTTP requests
    const abortController = this.abortControllers.get(conversationId);
    if (abortController) {
      abortController.abort();
      this.abortControllers.delete(conversationId);
    }
  }

  /**
   * Check if a conversation has been cancelled
   */
  isCancelled(conversationId: string): boolean {
    return this.cancelledConversations.has(conversationId);
  }

  /**
   * Register an AbortController for a conversation to enable HTTP request cancellation
   */
  registerAbortController(conversationId: string, controller: AbortController): void {
    this.abortControllers.set(conversationId, controller);
  }

  /**
   * Clean up cancellation tracking for a conversation (call when processing completes or fails)
   */
  cleanup(conversationId: string): void {
    this.cancelledConversations.delete(conversationId);
    const abortController = this.abortControllers.get(conversationId);
    if (abortController) {
      abortController.abort();
      this.abortControllers.delete(conversationId);
    }
  }

  /**
   * Throw an error if conversation is cancelled (use for cancellation checkpoints)
   */
  checkCancellation(conversationId: string): void {
    if (this.isCancelled(conversationId)) {
      throw new Error(`Processing cancelled for conversation ${conversationId}`);
    }
  }

  /**
   * Get all currently cancelled conversations
   */
  getCancelledConversations(): string[] {
    return Array.from(this.cancelledConversations);
  }
}

// Export singleton instance
export const cancellationManager = CancellationManager.getInstance();
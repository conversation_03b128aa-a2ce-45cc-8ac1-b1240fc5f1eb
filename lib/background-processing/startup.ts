import { startBackgroundWorker } from './worker';

let isInitialized = false;

export function initializeBackgroundProcessing() {
  if (isInitialized) return;
  
  console.log('[Startup] Initializing background processing...');
  
  // Start the background worker
  startBackgroundWorker();
  
  isInitialized = true;
  console.log('[Startup] Background processing initialized');
}

// Auto-initialize when imported
if (typeof window === 'undefined') {
  // Only run on server side
  initializeBackgroundProcessing();
} 
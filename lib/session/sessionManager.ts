import { v4 as uuidv4 } from 'uuid';

const SESSION_KEY = 'videotext_session_id';
const SESSION_EXPIRY_KEY = 'videotext_session_expiry';
const SESSION_DURATION = 48 * 60 * 60 * 1000; // 48 hours in milliseconds

export interface SessionInfo {
  sessionId: string;
  isAnonymous: boolean;
  expiresAt: number;
}

class SessionManager {
  private sessionId: string | null = null;
  private isInitialized = false;

  /**
   * Initialize session management - call this on app startup
   */
  initialize(): SessionInfo {
    if (typeof window === 'undefined') {
      // Server-side rendering - return a temporary session
      return {
        sessionId: '',
        isAnonymous: true,
        expiresAt: Date.now() + SESSION_DURATION
      };
    }

    if (this.isInitialized) {
      return this.getSessionInfo();
    }

    // Check for existing session
    const existingSessionId = localStorage.getItem(SESSION_KEY);
    const existingExpiry = localStorage.getItem(SESSION_EXPIRY_KEY);

    if (existingSessionId && existingExpiry) {
      const expiryTime = parseInt(existingExpiry, 10);
      
      if (Date.now() < expiryTime) {
        // Valid existing session
        this.sessionId = existingSessionId;
        this.isInitialized = true;
        return {
          sessionId: existingSessionId,
          isAnonymous: true,
          expiresAt: expiryTime
        };
      } else {
        // Expired session - clean up
        this.clearSession();
      }
    }

    // Create new session
    this.sessionId = uuidv4();
    const expiresAt = Date.now() + SESSION_DURATION;
    
    localStorage.setItem(SESSION_KEY, this.sessionId);
    localStorage.setItem(SESSION_EXPIRY_KEY, expiresAt.toString());
    
    this.isInitialized = true;

    return {
      sessionId: this.sessionId,
      isAnonymous: true,
      expiresAt
    };
  }

  /**
   * Get current session information
   */
  getSessionInfo(): SessionInfo {
    if (typeof window === 'undefined') {
      return {
        sessionId: '',
        isAnonymous: true,
        expiresAt: Date.now() + SESSION_DURATION
      };
    }

    if (!this.isInitialized) {
      return this.initialize();
    }

    const sessionId = this.sessionId || localStorage.getItem(SESSION_KEY) || '';
    const expiryStr = localStorage.getItem(SESSION_EXPIRY_KEY);
    const expiresAt = expiryStr ? parseInt(expiryStr, 10) : Date.now() + SESSION_DURATION;

    return {
      sessionId,
      isAnonymous: true,
      expiresAt
    };
  }

  /**
   * Get session ID for API requests
   */
  getSessionId(): string | null {
    if (typeof window === 'undefined') {
      return null;
    }

    if (!this.isInitialized) {
      this.initialize();
    }

    return this.sessionId || localStorage.getItem(SESSION_KEY);
  }

  /**
   * Clear current session (for "start over" functionality)
   */
  clearSession(): void {
    if (typeof window === 'undefined') {
      return;
    }

    this.sessionId = null;
    this.isInitialized = false;
    localStorage.removeItem(SESSION_KEY);
    localStorage.removeItem(SESSION_EXPIRY_KEY);
  }

  /**
   * Transfer session to authenticated user (called after login/signup)
   */
  transferToUser(): string | null {
    const sessionId = this.getSessionId();
    this.clearSession();
    return sessionId;
  }

  /**
   * Extend session expiry (called on user activity)
   */
  extendSession(): void {
    if (typeof window === 'undefined' || !this.sessionId) {
      return;
    }

    const newExpiry = Date.now() + SESSION_DURATION;
    localStorage.setItem(SESSION_EXPIRY_KEY, newExpiry.toString());
  }

  /**
   * Check if session is expired
   */
  isSessionExpired(): boolean {
    if (typeof window === 'undefined') {
      return false;
    }

    const expiryStr = localStorage.getItem(SESSION_EXPIRY_KEY);
    if (!expiryStr) {
      return true;
    }

    return Date.now() >= parseInt(expiryStr, 10);
  }
}

// Export singleton instance
export const sessionManager = new SessionManager();

// Utility function to get session headers for API requests
export function getSessionHeaders(): Record<string, string> {
  const sessionId = sessionManager.getSessionId();
  return sessionId ? { 'x-session-id': sessionId } : {};
}

// Utility function to check if user is in anonymous mode
export function isAnonymousMode(): boolean {
  return sessionManager.getSessionId() !== null;
}

import Stripe from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables.');
}

// Initialize Stripe with the secret key and API version.
// The API version is pinned to ensure that updates to the Stripe API
// don't break your integration without you knowing.
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  // apiVersion: '2023-10-16', // Temporarily removed to avoid type issue
  typescript: true,
});

/**
 * Fetches all active products from Stripe.
 * Handles pagination to retrieve all products if there are more than 100.
 */
export const getAllStripeProducts = async (): Promise<Stripe.Product[]> => {
  const allProducts: Stripe.Product[] = [];
  let hasMore = true;
  let startingAfter: string | undefined = undefined;

  console.log('Fetching all active products from Stripe...');

  while (hasMore) {
    try {
      const products: Stripe.ApiList<Stripe.Product> = await stripe.products.list({
        active: true, // Only fetch active products
        limit: 100, // Max limit per request
        starting_after: startingAfter,
        expand: ['data.default_price'], // Expand default_price to get price details
      });

      if (products.data && products.data.length > 0) {
        allProducts.push(...products.data);
        startingAfter = products.data[products.data.length - 1].id;
      } else {
        hasMore = false;
      }

      if (!products.has_more) {
        hasMore = false;
      }

    } catch (error) {
      console.error('Error fetching products from Stripe:', error);
      // Depending on your error handling strategy, you might want to throw the error,
      // return a partial list, or an empty list.
      throw error; // Re-throwing the error for the caller to handle
    }
  }
  console.log(`Fetched a total of ${allProducts.length} products from Stripe.`);
  return allProducts;
};

/**
 * Fetches all active prices for a specific product from Stripe.
 * Handles pagination.
 * @param productId The ID of the Stripe product.
 */
export const getPricesForProduct = async (productId: string): Promise<Stripe.Price[]> => {
  if (!productId) {
    console.warn('getPricesForProduct called without a productId.');
    return [];
  }

  const allPrices: Stripe.Price[] = [];
  let hasMore = true;
  let startingAfter: string | undefined = undefined;

  console.log(`Fetching all active prices for product ${productId} from Stripe...`);

  while (hasMore) {
    try {
      const prices: Stripe.ApiList<Stripe.Price> = await stripe.prices.list({
        product: productId,
        active: true,
        limit: 100, // Max limit per request
        starting_after: startingAfter,
        expand: ['data.product'], // Optionally expand product details if needed elsewhere
      });

      if (prices.data && prices.data.length > 0) {
        allPrices.push(...prices.data);
        startingAfter = prices.data[prices.data.length - 1].id;
      } else {
        hasMore = false;
      }

      if (!prices.has_more) {
        hasMore = false;
      }

    } catch (error) {
      console.error(`Error fetching prices for product ${productId} from Stripe:`, error);
      throw error; // Re-throwing the error for the caller to handle
    }
  }
  console.log(`Fetched a total of ${allPrices.length} prices for product ${productId}.`);
  return allPrices;
};

// You can add more Stripe admin-related functions here, for example:
// - getProductById(productId: string)
// - getPriceById(priceId: string)
// - getAllPricesForProduct(productId: string) // This is now implemented as getPricesForProduct 
<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="513" height="513" viewBox="0 0 513 513"><image width="513" height="513" xlink:href="data:image/png;base64,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"></image><style>@media (prefers-color-scheme: light) { :root { filter: none; } }
@media (prefers-color-scheme: dark) { :root { filter: none; } }
</style></svg>
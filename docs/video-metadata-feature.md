# Video Metadata Feature

This feature adds rich YouTube video metadata display to the conversation interface, using the Supadata API to fetch comprehensive video information.

## Overview

The video metadata feature provides:
- Video title, description, and duration
- Channel information (name and ID)
- View count and like count
- Upload date
- Video tags
- Available transcript languages
- High-quality thumbnail

## Components

### 1. Database Schema

A new `video_metadata` table stores rich video information:

```sql
CREATE TABLE video_metadata (
  id UUID PRIMARY KEY,
  video_id VARCHAR(11) UNIQUE,
  title TEXT,
  description TEXT,
  duration INTEGER,
  channel_id VARCHAR(100),
  channel_name TEXT,
  tags TEXT[],
  thumbnail_url TEXT,
  upload_date TIMESTAMP WITH TIME ZONE,
  view_count BIGINT,
  like_count BIGINT,
  transcript_languages TEXT[],
  raw_metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

The `conversations` table has been extended with a `video_metadata_id` foreign key.

### 2. API Endpoints

#### POST `/api/product/supadata`
Fetches video metadata from the Supadata API and stores it in the database.

**Request Body:**
```json
{
  "youtubeUrl": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
  "conversationId": "conversation-uuid"
}
```

**Response:**
```json
{
  "success": true,
  "metadata": {
    "id": "metadata-uuid",
    "video_id": "dQw4w9WgXcQ",
    "title": "Rick Astley - Never Gonna Give You Up",
    "description": "The official music video...",
    "duration": 213,
    "channel_name": "Rick Astley",
    "view_count": 1234567890,
    "like_count": 12345678,
    "tags": ["Rick Astley", "Official Video", "Music"],
    "transcript_languages": ["en", "es", "fr"]
  },
  "fromCache": false
}
```

#### GET `/api/product/supadata`
Retrieves existing video metadata.

**Query Parameters:**
- `conversationId`: Get metadata for a specific conversation
- `videoId`: Get metadata for a specific video ID

### 3. UI Components

#### VideoMetadataCard
A rich component that displays video metadata with two variants:
- **Compact mode**: Shows essential information (title, stats, channel)
- **Full mode**: Shows comprehensive metadata including description, tags, and transcript languages

The component is now directly integrated into the ChatInterface for optimal performance and reduced redundancy.

## Integration Flow

1. **Conversation Creation**: When a user creates a new conversation with a YouTube URL:
   - The `fetch_transcript` API calls the new `/api/product/supadata` endpoint
   - Video metadata is fetched from Supadata and stored in the database
   - The conversation is linked to the metadata record

2. **Conversation Loading**: When loading an existing conversation:
   - The conversation fetch API includes video metadata in the response
   - The UI displays the rich metadata using VideoMetadataCard directly in the ChatInterface

3. **Caching**: Video metadata is cached in the database, so subsequent conversations using the same video will reuse the existing metadata.

## Migration

Run the migration script to add the video metadata table:

```bash
psql -d your_database -f scripts/migrate-video-metadata.sql
```

Or apply the migration file:

```bash
psql -d your_database -f supabase/migrations/20240101000000_create_video_metadata_table.sql
```

## Environment Variables

Ensure `SUPADATA_API_KEY` is set in your environment variables to enable metadata fetching.

## Benefits

1. **Enhanced User Experience**: Users see rich information about the videos they're analyzing
2. **Performance**: Metadata is cached, reducing API calls
3. **Consistency**: All video information is centralized and consistent
4. **Extensibility**: The raw metadata field stores the complete API response for future features
5. **Simplified Architecture**: Eliminated redundant wrapper components for cleaner, more maintainable code

## Fallback Behavior

The VideoMetadataCard component gracefully handles cases where metadata might be incomplete, ensuring a consistent user experience even with partial data.

## Future Enhancements

- Video thumbnail display
- Channel avatar integration
- Video category information
- Related video suggestions
- Metadata-based search and filtering 
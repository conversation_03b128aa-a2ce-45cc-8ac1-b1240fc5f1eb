# Anonymous Videotext System

This document describes the implementation of the anonymous videotext system that allows users to create videotexts without being logged in, with automatic account association upon login/signup.

## Overview

The system enables users to:
1. Create videotexts without authentication using browser session tracking
2. Automatically transfer conversations to their account when they log in or sign up
3. Clean up abandoned conversations manually ("start over") or automatically

## Architecture

### Session Management
- **File**: `lib/session/sessionManager.ts`
- **Purpose**: Manages browser-based sessions using localStorage
- **Session Duration**: 48 hours
- **Storage Keys**: 
  - `videotext_session_id`: UUID for the session
  - `videotext_session_expiry`: Expiration timestamp

### Authentication Layer
- **File**: `lib/auth/apiAuth.ts`
- **Purpose**: Unified authentication handling for both authenticated and anonymous users
- **Key Functions**:
  - `getAuthContext()`: Determines if request is authenticated or anonymous
  - `validateAccess()`: Validates that request has valid auth or session
  - `getConversationFilter()`: Creates database filters based on auth context
  - `matchesConversationOwnership()`: Checks if user/session owns a conversation

### Database Schema Changes

The following columns were added to the `conversations` table:
```sql
ALTER TABLE conversations 
ADD COLUMN session_id VARCHAR(36),
ADD COLUMN is_anonymous BOOLEAN DEFAULT FALSE,
ADD COLUMN created_anonymously_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN transferred_to_user_at TIMESTAMP WITH TIME ZONE;

-- Make user_id nullable for anonymous conversations
ALTER TABLE conversations 
ALTER COLUMN user_id DROP NOT NULL;
```

## API Endpoints

### Updated Existing Endpoints
All conversation and message endpoints now support both authenticated and anonymous access:
- `GET/POST /api/product/conversations`
- `GET/POST /api/product/messages`
- `POST /api/product/submit_message`
- `POST /api/product/conversations/[id]/start-processing`

### New Endpoints

#### Transfer Anonymous Conversations
- **Endpoint**: `POST /api/product/transfer-anonymous-conversations`
- **Purpose**: Transfer anonymous conversations to authenticated user account
- **Body**: `{ sessionId: string }`
- **Authentication**: Required (user must be logged in)

#### Cleanup Anonymous Conversations
- **Endpoint**: `POST/GET /api/product/cleanup-anonymous-conversations`
- **Purpose**: Clean up abandoned anonymous conversations
- **POST Body**: `{ sessionId?: string, maxAgeHours?: number }`
- **GET**: Automated cleanup of conversations older than 48 hours

## Frontend Components

### Session Management
- **Hook**: `app/hooks/useAnonymousTransfer.ts`
- **Functions**:
  - `transferAnonymousConversations()`: Transfer conversations to user account
  - `clearAnonymousSession()`: Clean up current session
  - `isAnonymousMode()`: Check if user is in anonymous mode

### UI Components

#### Start Over Button
- **File**: `app/components/ui/StartOverButton.tsx`
- **Purpose**: Allows anonymous users to clear their session and start fresh
- **Features**: Confirmation modal, automatic redirect

#### Anonymous Transfer Handler
- **File**: `app/components/auth/AnonymousTransferHandler.tsx`
- **Purpose**: Automatically transfers conversations when user logs in
- **Features**: Success/error notifications, automatic cleanup

#### Session Initializer
- **File**: `app/components/session/SessionInitializer.tsx`
- **Purpose**: Initializes session management on app startup
- **Usage**: Place in root layout

### Updated Components
- **useConversationCreation**: Now supports anonymous mode with session headers
- **NewChatInput**: Automatically works with anonymous mode
- **LandingPageChatCreation**: No changes needed, inherits anonymous support

## Integration Guide

### 1. Add Session Initialization
Add to your root layout:
```tsx
import { SessionInitializer } from '@/app/components/session/SessionInitializer';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <SessionInitializer />
        {children}
      </body>
    </html>
  );
}
```

### 2. Add Transfer Handler
Add to your authenticated layout or auth callback:
```tsx
import { AnonymousTransferHandler } from '@/app/components/auth/AnonymousTransferHandler';

export default function AuthenticatedLayout({ children }) {
  return (
    <>
      <AnonymousTransferHandler />
      {children}
    </>
  );
}
```

### 3. Add Start Over Button
Add to your conversation interface for anonymous users:
```tsx
import { StartOverButton } from '@/app/components/ui/StartOverButton';

export default function ConversationHeader() {
  return (
    <div>
      {/* Other header content */}
      <StartOverButton />
    </div>
  );
}
```

## Security Considerations

1. **Session Validation**: All API endpoints validate session ownership
2. **Admin Client Usage**: Database operations use admin client to bypass RLS
3. **Session Expiry**: Sessions automatically expire after 48 hours
4. **Cleanup**: Automated cleanup prevents database bloat
5. **Transfer Security**: Only authenticated users can transfer conversations

## Monitoring and Maintenance

### Automated Cleanup
Set up a cron job to call the cleanup endpoint:
```bash
# Clean up abandoned conversations daily
0 2 * * * curl -X GET https://your-domain.com/api/product/cleanup-anonymous-conversations
```

### Monitoring Queries
```sql
-- Count anonymous conversations
SELECT COUNT(*) FROM conversations WHERE is_anonymous = true AND user_id IS NULL;

-- Count conversations older than 48 hours
SELECT COUNT(*) FROM conversations 
WHERE is_anonymous = true 
AND user_id IS NULL 
AND created_anonymously_at < NOW() - INTERVAL '48 hours';

-- Check transfer success rate
SELECT 
  COUNT(*) as total_transferred,
  AVG(EXTRACT(EPOCH FROM (transferred_to_user_at - created_anonymously_at))/3600) as avg_hours_before_transfer
FROM conversations 
WHERE transferred_to_user_at IS NOT NULL;
```

## Testing

### Manual Testing Flow
1. Visit landing page without logging in
2. Create a videotext conversation
3. Verify conversation is created with session_id
4. Log in or sign up
5. Verify conversation is transferred to user account
6. Test "Start Over" functionality

### API Testing
```bash
# Test anonymous conversation creation
curl -X POST http://localhost:3000/api/product/conversations \
  -H "Content-Type: application/json" \
  -H "x-session-id: test-session-123"

# Test transfer
curl -X POST http://localhost:3000/api/product/transfer-anonymous-conversations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"sessionId": "test-session-123"}'
```

## Troubleshooting

### Common Issues
1. **Session not persisting**: Check localStorage in browser dev tools
2. **Transfer not working**: Verify user is authenticated when transfer is called
3. **Conversations not showing**: Check session headers are being sent
4. **Cleanup not working**: Verify database permissions for admin client

### Debug Logging
Enable debug logging by checking browser console and server logs for:
- Session initialization
- API request headers
- Transfer operations
- Cleanup operations

NEXT_PUBLIC_SUPABASE_URL=https://iwimfrskyfcandpopiny.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3aW1mcnNreWZjYW5kcG9waW55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ0NDY4MjIsImV4cCI6MjA2MDAyMjgyMn0.oNCmiM_ftbbyGdygIlZ3V-AazUBLDK180OyubAAs4y4

NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RM6VhD6e5d6MzhOrM8WG4Clnj0lURzDMgV70qu4iF4gM0rWRBEoW5YLXd3a4jOTQsqgSOoSE2Wl4GDd8Qkzv0gq00GjhpfHJi
STRIPE_SECRET_KEY=sk_test_51RM6VhD6e5d6MzhOErw0xgWv3b0ikASKaAqR7F99xvXKSBqemvGHSaRItYmzP6bOZpFWjFg7M1xYHHf9QeoA11WX00Q44iCIDB

NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Replace this with the webhook secret from `stripe listen` command
STRIPE_WEBHOOK_SECRET=whsec_REPLACE_WITH_NEW_SECRET_FROM_STRIPE_CLI

SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3aW1mcnNreWZjYW5kcG9waW55Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDQ0NjgyMiwiZXhwIjoyMDYwMDIyODIyfQ.aFPqc19bn9z35AgJU113VA9VpKilqC4O64xNcBnsmHs

# Dify API Keys - IMPORTANT: Replace with actual API keys!
DIFY_API_KEY_RENAME="app-oq60NZNejEtLLfsqv6tkJjez"
DIFY_API_KEY_CONDENSER="app-FoATGa2emKq0MMjIhf6I0QNt"
SUPADATA_API_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiIsImtpZCI6IjEifQ.eyJpc3MiOiJuYWRsZXMiLCJpYXQiOiIxNzQ0NjIwNTUxIiwicHVycG9zZSI6ImFwaV9hdXRoZW50aWNhdGlvbiIsInN1YiI6ImY1M2Y1NmZmYTQ5ZjRkN2Q4YjA3YzA3YjRkNDk5OWQ4In0.vY6SBnQaPUMADinweRHDRYTxjH7KTuz06YR_hb_sByQ

# Note: You need to:
# 1. Replace DIFY_API_KEY_RENAME with your actual Dify API key for the rename workflow
# 2. Replace SUPADATA_API_KEY with your actual Supadata API key
# 3. Make sure your Dify workflow expects an input called "title"
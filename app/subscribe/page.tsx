'use client';

import React from 'react';
import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';
import ProductList from '@/app/components/products/ProductList';
import { Container, Title, Accordion, Stack } from '@mantine/core';
import { HeaderMenu } from '../components/ui/HeaderMenu';
import { Footer } from '../components/ui/Footer';

const faqData = [
  {
    value: 'cancellation',
    question: 'How do I cancel my subscription?',
    answer:
      'You can cancel your subscription at any time from your account dashboard. Your plan will remain active until the end of the current billing period, and you will not be charged again.',
  },
  {
    value: 'upgrades',
    question: 'Can I upgrade or downgrade my plan?',
    answer:
      "Yes, you can change your plan at any time. Upgrades are applied immediately, and we'll credit the unused portion of your previous plan to your account. Downgrades become effective at the end of the current billing cycle.",
  },
  {
    value: 'content-use',
    question: 'How can I use the generated content?',
    answer:
      'The use of generated content is subject to the copyright of the original material. Please adhere to fair use policies and provide attribution to the original source where appropriate. You are responsible for ensuring your use of the content is lawful.',
  },
  {
    value: 'affordability',
    question: "What if I can't afford a subscription?",
    answer:
      'We believe everyone should have access to powerful tools. If you are a student, researcher, or facing financial hardship, please contact us. We are happy to arrange a complimentary subscription.',
  },
  {
    value: 'privacy',
    question: 'How is my data handled?',
    answer:
      'We take your privacy very seriously. All your data, including uploaded files and generated content, is stored securely and is never shared with third parties. You have full control and can delete your data from our servers at any time.',
  },
];

const SubscribePageContent = () => {
  const searchParams = useSearchParams();
  const priceId = searchParams.get('priceId');
  const productId = searchParams.get('productId');

  return (
    <Container size="full" p={0}>
      <HeaderMenu />

      <Container size="lg" py="xl">
        <Title order={1} ta="center" mb="md">
          Choose Your Plan
        </Title>

        <ProductList
          listType="subscriptions"
          displayMode="all"
          showCancelButton={true}
          gridColumns={4}
          initialCheckoutParams={priceId && productId ? { priceId, productId } : undefined}
        />
      </Container>

      <Container size="md" py="xl">
        <Stack>
            <Title order={3} ta="center" mb="lg">
              Frequently Asked Questions
            </Title>
            <Accordion variant="separated">
              {faqData.map((item) => (
                <Accordion.Item key={item.value} value={item.value}>
                  <Accordion.Control>{item.question}</Accordion.Control>
                  <Accordion.Panel>{item.answer}</Accordion.Panel>
                </Accordion.Item>
              ))}
            </Accordion>
          </Stack>
        </Container>
    </Container>
  );
}

const SubscribePage = () => {
  return (
    <Suspense>
      <SubscribePageContent />
      <Footer />
    </Suspense>
  );
};

export default SubscribePage; 

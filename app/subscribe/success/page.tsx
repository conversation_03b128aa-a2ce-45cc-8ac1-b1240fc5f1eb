'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

export default function SubscribeSuccessPage() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Optional: You could add a useEffect here to verify the session_id with your backend
  // and fetch more details about the subscription or payment.
  // For now, we'll just acknowledge the success based on reaching this page with a session_id.

  useEffect(() => {
    if (!sessionId) {
      setError('No session ID found. Your payment status is unclear.');
    }
    setIsLoading(false);
  }, [sessionId]);

  if (isLoading) {
    return (
      <div className="container mx-auto p-8 text-center">
        <h1 className="text-3xl  mb-4">Verifying your subscription...</h1>
        <p>Please wait a moment.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-8 text-center">
        <h1 className="text-2xl  text-red-600 mb-4">Verification Error</h1>
        <p className="text-red-500 mb-6">{error}</p>
        <Link href="/subscribe" className="text-blue-500 hover:underline">
          Return to Subscription Page
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-8 text-center">
      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-green-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <h1 className="text-3xl  text-green-600 mb-4">Subscription Successful!</h1>
      <p className="text-lg text-gray-700 mb-2">
        Thank you for subscribing. Your payment has been processed.
      </p>
      {sessionId && (
        <p className=" text-gray-500 mb-6">
          Your Checkout Session ID: {sessionId}
        </p>
      )}
      <p className="mb-8">
        You will receive a confirmation email shortly. You can now access your new features.
      </p>
      <div className="space-x-4">
        <Link href="/" className="text-white  py-3 px-6 rounded transition duration-150 ease-in-out">
          Go to Homepage
        </Link>
        {/* Link to a dashboard or account page if you have one */}
        {/* <Link href="/dashboard" className="bg-gray-200 hover:bg-gray-300 text-gray-700  py-3 px-6 rounded transition duration-150 ease-in-out">
          Go to Dashboard
        </Link> */}
      </div>
    </div>
  );
} 
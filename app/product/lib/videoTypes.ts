export interface VideoMetadata {
  id: string;
  video_id: string;
  title: string;
  description: string;
  duration: number;
  channel_id: string;
  channel_name: string;
  view_count: number;
  like_count: number;
  upload_date: string;
  thumbnail_url: string;
  transcript_languages: string[];
  tags?: string[];
  raw_metadata?: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

export interface ConversationWithMetadata {
  id: string;
  title: string;
  youtube_video_id?: string;
  video_metadata?: VideoMetadata;
  created_at: string;
  updated_at: string;
  user_id: string;
  is_pinned?: boolean;
  dify_transcript_id?: string;
  dify_summary_id?: string;
} 
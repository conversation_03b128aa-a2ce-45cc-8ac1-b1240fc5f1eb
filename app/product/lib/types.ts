// Define message types for the chat
export type MessageType = 'user' | 'ai';

export interface Message {
	id: string;
	type: 'user' | 'ai';
	content: string;
}

export interface TranscriptSegment {
	text: string;
	duration: number;
	offset: number;
	lang: string;
}

export interface TranscriptData {
	lang: string;
	content: TranscriptSegment[];
}

export interface TranscriptMetadata {
	id: string;
	title: string;
	duration: number;
	video_id: string;
	channel_id: string;
	created_at: string;
	updated_at: string;
	view_count: number;
	like_count: number;
	description: string;
	upload_date: string;
	channel_name: string;
	thumbnail_url: string;
	transcript_languages: string[];
	tags?: string[];
	raw_metadata?: Record<string, unknown>;
}

export interface CombinedTranscriptData {
	videoId: string;
	metadata: TranscriptMetadata;
	combinedAt: string;
	transcript: TranscriptData;
}

export interface ConversationMessage {
	id: string;
	content: string;
	message_number: number;
	created_at: string;
	user_message: boolean;
}

export interface TranscriptFile {
	transcript?: TranscriptData;
	metadata?: TranscriptMetadata;
	optimizedTranscript?: string | TranscriptSegment[];
	videoId?: string;
	fetchedAt?: string;
	optimizedAt?: string;
} 
// Update app/product/savedchats.tsx
'use client';

import { useState, useEffect, useRef } from 'react';
import { Stack, Box, Text, Input, Flex, Button, Loader, Badge, Modal } from '@mantine/core';
import { IoCreateOutline, IoSettingsOutline, IoLogOutOutline, IoCheckmarkOutline } from "react-icons/io5";
import { ConversationContextMenu } from './components/ui/ConversationContextMenu';
import { AnimatedTitle } from './components/ui/AnimatedTitle';
import { Logo } from "@/app/components/ui/Logo";
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import DashboardModal from "@/app/components/ui/DashboardModal";
import { 
  isToday, 
  isYesterday, 
  startOfWeek,
  startOfMonth,
  format
} from 'date-fns';
import sidebarClasses from '@/app/product/theme/Sidebar.module.css';
import { GoSidebarExpand, GoSidebarCollapse } from "react-icons/go";
import { getSessionHeaders } from '@/lib/session/sessionManager';


interface Conversation {
  id: string;
  name: string;
  updated_at: number;
  created_at: number;
  processing_status: string | null;
  youtube_video_id?: string;
  supabase_id: string;
}

interface SavedChatsProps {
  onSelectConversation: (conversationId: string | null) => void;
  currentConversationId: string | null;
  onConversationRenamed: () => void;
  refreshTrigger: number;
  onCreateNewClicked: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  creatingConversation?: boolean;
  pendingConversations?: Set<string>;
  titleAnimations?: Map<string, { oldTitle: string; newTitle: string }>;
  onTitleAnimationComplete?: (conversationId: string) => void;
}

export default function SavedChats({ 
  onSelectConversation, 
  currentConversationId,
  onConversationRenamed,
  refreshTrigger,
  onCreateNewClicked,
  isCollapsed = false,
  onToggleCollapse,
  creatingConversation = false,
  pendingConversations = new Set(),
  titleAnimations = new Map(),
  onTitleAnimationComplete
}: SavedChatsProps) {
  const router = useRouter();
  const supabase = createClient();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [renamingConversationId, setRenamingConversationId] = useState<string | null>(null);
  const [renamingValue, setRenamingValue] = useState('');
  const [isDashboardOpen, setIsDashboardOpen] = useState(false);
  const [deletionModalOpen, setDeletionModalOpen] = useState(false);
  const [conversationToDelete, setConversationToDelete] = useState<Conversation | null>(null);
  const renameInputRef = useRef<HTMLInputElement>(null);
  const isInitialLoad = useRef(true);

  useEffect(() => {
    fetchConversations();
  }, [refreshTrigger]);

  // Function to group conversations by date
  const groupConversationsByDate = (convs: Conversation[]) => {
    const groups: Record<string, Conversation[]> = {
      Today: [],
      Yesterday: [],
      'Previous 7 Days': [],
      'This Month': [],
      Older: [],
    };

    const now = new Date();
    const startOfThisWeek = startOfWeek(now);
    const startOfThisMonth = startOfMonth(now);

    convs.forEach(conv => {
      const updatedAt = new Date(conv.created_at * 1000);
      if (isToday(updatedAt)) {
        groups.Today.push(conv);
      } else if (isYesterday(updatedAt)) {
        groups.Yesterday.push(conv);
      } else if (updatedAt >= startOfThisWeek) {
        groups['Previous 7 Days'].push(conv);
      } else if (updatedAt >= startOfThisMonth) {
        groups['This Month'].push(conv);
      } else {
        groups.Older.push(conv);
      }
    });

    // Remove empty groups
    Object.keys(groups).forEach((key) => {
      if (groups[key].length === 0) {
        delete groups[key];
      }
    });

    return groups;
  };

  async function fetchConversations() {
    if (isInitialLoad.current) {
      setLoading(true);
    }
    
    try {
      const response = await fetch('/api/product/conversations', {
        headers: {
          ...getSessionHeaders()
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch conversations');
      }
      
      const data = await response.json();
      const sortedConversations = data.data
        .sort((a: Conversation, b: Conversation) => b.created_at - a.created_at);

      setConversations(sortedConversations);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(`Failed to load conversations: ${errorMessage}`);
    } finally {
      if (isInitialLoad.current) {
        setLoading(false);
        isInitialLoad.current = false;
      }
    }
  }

  function formatDate(timestamp: number): string {
    const date = new Date(timestamp * 1000);
    return format(date, 'MMM d, HH:mm');
  }



  const handleRename = async (conversationId: string, newName: string) => {
    const trimmedNewName = newName.trim();
    const originalConversation = conversations.find(c => c.id === conversationId);

    if (!trimmedNewName || !originalConversation || trimmedNewName === originalConversation.name) {
      setRenamingConversationId(null);
      return;
    }

    const originalConversations = [...conversations];
    const updatedConversations = conversations.map(conv => 
      conv.id === conversationId ? { ...conv, name: trimmedNewName } : conv
    );

    setConversations(updatedConversations);
    setRenamingConversationId(null);

    try {
      const response = await fetch(`/api/product/conversations/${conversationId}/rename`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: trimmedNewName }),
      });

      if (!response.ok) {
        throw new Error('Failed to rename conversation');
      }
      
      const serverUpdatedConv = await response.json();
      setConversations(prevConvs => {
        const finalConvs = prevConvs.map(conv => {
          if (conv.id === conversationId) {
            return {
              ...conv,
              name: serverUpdatedConv.title,
              updated_at: Math.floor(new Date(serverUpdatedConv.updated_at).getTime() / 1000)
            };
          }
          return conv;
        });
        return [...finalConvs].sort((a, b) => b.created_at - a.created_at);
      });

    } catch (error) {
      setConversations(originalConversations);
      setError(error instanceof Error ? error.message : 'Rename failed');
      onConversationRenamed();
    }
  };

  const handleDelete = async (conversationId: string) => {
    try {
      const response = await fetch(`/api/product/conversations/${conversationId}/delete`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        throw new Error('Failed to delete conversation');
      }
      
      setConversations(prev => prev.filter(c => c.id !== conversationId));
      
      if (currentConversationId === conversationId) {
        onSelectConversation(null);
        // Update URL without navigation when deleting current conversation
        window.history.replaceState(null, '', '/product');
      }
    } catch (error) {
      throw error;
    }
  };

  const handleStartRename = (conversationId: string) => {
    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation) {
      setRenamingConversationId(conversationId);
      setRenamingValue(conversation.name);
    }
  };

  const handleRenameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRenamingValue(e.target.value);
  };

  const handleRenameKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, conversationId: string) => {
    if (e.key === 'Enter') {
      handleRename(conversationId, renamingValue);
    }
    if (e.key === 'Escape') {
      setRenamingConversationId(null);
    }
  };

  const handleRenameBlur = (conversationId: string) => {
    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation && renamingValue.trim() && renamingValue !== conversation.name) {
      handleRename(conversationId, renamingValue);
    } else {
      setRenamingConversationId(null);
    }
  };

  const handleConversationSelect = async (conversationId: string) => {
    const conversation = conversations.find(c => c.id === conversationId);
    
    // If the conversation is completed, reset its status to null
    if (conversation && conversation.processing_status === 'completed') {
      await resetConversationStatus(conversation);
    }
    
    onSelectConversation(conversationId);
    // Update URL without navigation - use shallow routing to avoid page reload
    window.history.replaceState(
      null, 
      '', 
      `/product/${conversationId}`
    );
  };

  const resetConversationStatus = async (conversation: Conversation) => {
    try {
      const response = await fetch(`/api/product/conversations/${conversation.supabase_id}/reset-status`, {
        method: 'POST',
      });
      
      if (response.ok) {
        // Update local state to remove the completed status
        setConversations(prevConversations => 
          prevConversations.map(conv => 
            conv.id === conversation.id 
              ? { ...conv, processing_status: null }
              : conv
          )
        );
      }
    } catch (error) {
      console.error('Error resetting conversation status:', error);
    }
  };

  const handleFailedConversationClick = (conversation: Conversation, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent conversation selection
    setConversationToDelete(conversation);
    setDeletionModalOpen(true);
  };

  const handleConfirmDeletion = async () => {
    if (!conversationToDelete) return;
    
    try {
      await handleDelete(conversationToDelete.id);
      setDeletionModalOpen(false);
      setConversationToDelete(null);
    } catch (error) {
      console.error('Error deleting conversation:', error);
      // Keep modal open on error
    }
  };

  const handleCancelDeletion = () => {
    setDeletionModalOpen(false);
    setConversationToDelete(null);
  };

  const renderStatusIndicator = (conversation: Conversation) => {
    const status = conversation.processing_status;
    const isPending = pendingConversations.has(conversation.id);
    
    // Show pending loader if in pendingConversations set (real-time status)
    if (isPending) {
      return <Loader size="xs" />;
    }
    
    // Show status based on processing_status column
    switch (status) {
      case 'processing':
      case 'pending':
      case 'fetching':
      case 'optimizing':
      case 'summarizing':
        return <Loader size="xs" />;
      
      case 'failed':
        return (
          <Badge 
            color="red" 
            size="xs" 
            variant="filled"
            style={{ cursor: 'pointer' }}
            onClick={(e) => handleFailedConversationClick(conversation, e)}
          >
            Failed
          </Badge>
        );
      
      case 'completed':
        return (
          <Box title="Processing completed">
            <IoCheckmarkOutline size={14} color="green" />
          </Box>
        );
      
      default:
        return null;
    }
  };

  return (
    <Stack 
      className={sidebarClasses.sidebar}
      gap={0}
      data-collapsed={isCollapsed}
    >
      <Box className={sidebarClasses.sidebarLogo} data-collapsed={isCollapsed}>
        <Box style={{ zIndex: 2000 }}>
          <Logo/>
        </Box>
      </Box>

      {onToggleCollapse && (
        <Button
          variant="sidebar"
          onClick={onToggleCollapse}
          className={isCollapsed ? sidebarClasses.sidebarToggleCollapsed : sidebarClasses.sidebarToggle}
        >
          {!isCollapsed ? <GoSidebarExpand size={20} /> : <GoSidebarCollapse size={20} />}
        </Button>
      )}

      <Flex align="center" justify="space-between" style={{ padding: '0 4px' }}>
        <Box style={{ flex: 1 }}>
          <Button 
            variant="sidebar" 
            onClick={onCreateNewClicked}
            disabled={creatingConversation}
            className={sidebarClasses.sidebarButton}
            data-collapsed={isCollapsed}
          >
            {creatingConversation ? (
              <>
                <svg className="animate-spin h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className={sidebarClasses.sidebarButtonText} data-collapsed={isCollapsed}>Creating...</span>
              </>
            ) : (
              <>
                <IoCreateOutline className="-mt-[0.1em] mr-1" color="var(--mantine-color-gray-7)" size={20}/>
                <span className={sidebarClasses.sidebarButtonText} data-collapsed={isCollapsed}>Create New</span>
              </>
            )}
          </Button>
        </Box>
      </Flex>

      <Stack 
        gap="xs" 
        p="xs"
        className={sidebarClasses.sidebarContent}
        data-collapsed={isCollapsed}
      >
        {loading && <Loader size="sm" className={sidebarClasses.sidebarLoader}/>}
        {error && <Text c="red" ta="center" py="md" px="xs">{error}</Text>}
        {!loading && !error && conversations.length === 0 && (
          <Text c="dimmed" ta="center" py="md">No conversations yet</Text>
        )}

        {Object.entries(groupConversationsByDate(conversations)).map(([groupName, groupConversations]) => (
          <Stack key={groupName} gap="xs" mt="md">
            <Text size="xs" fw={600} c="dimmed" px="xs" py="xs" tt="uppercase">
              {groupName}
            </Text>
            <Stack gap="xs">
              {groupConversations.map((conversation) => (
                <Box key={conversation.id}>
                  <ConversationContextMenu
                    conversationId={conversation.id}
                    currentName={conversation.name}
                    onDelete={handleDelete}
                    onStartRename={handleStartRename}
                  >
                    <Box 
                      p="xs"
                      className={sidebarClasses.conversationItem}
                      onClick={() => handleConversationSelect(conversation.id)}
                      style={{
                        backgroundColor: currentConversationId === conversation.id ? 'var(--mantine-color-gray-2)' : 'transparent'
                      }}
                    >
                      {renamingConversationId === conversation.id ? (
                        <Input
                          variant="inline"
                          ref={renameInputRef}
                          value={renamingValue}
                          onChange={handleRenameInputChange}
                          onKeyDown={(e) => handleRenameKeyDown(e, conversation.id)}
                          onBlur={() => handleRenameBlur(conversation.id)}
                          autoFocus
                        />
                      ) : (
                        <>
                          <Flex align="center" gap="xs">
                            {renderStatusIndicator(conversation)}
                            <Box style={{ flex: 1 }}>
                              {titleAnimations.has(conversation.id) ? (
                                <AnimatedTitle
                                  oldTitle={titleAnimations.get(conversation.id)!.oldTitle}
                                  newTitle={titleAnimations.get(conversation.id)!.newTitle}
                                  onAnimationComplete={() => onTitleAnimationComplete?.(conversation.id)}
                                />
                              ) : (
                                <Text fw={500} size="sm" truncate c="dark">
                                  {conversation.name}
                                </Text>
                              )}
                            </Box>
                          </Flex>
                          <Text size="xs" c="dimmed" mt="xs">
                            {formatDate(conversation.created_at)}
                          </Text>
                        </>
                      )}
                    </Box>
                  </ConversationContextMenu>
                </Box>
              ))}
            </Stack>
          </Stack>
        ))}
      </Stack>

      {/* Dashboard and Logout buttons at bottom */}
      <Box 
        style={{ 
          marginTop: 'auto', 
          marginLeft: "-5px",
          marginRight: "-5px",
          borderTop: '1px solid var(--mantine-color-gray-3)',
          padding: 'calc(var(--mantine-spacing-xs) + 5px)'
        }}
      >
        <Stack gap="xs">
          <Button
            variant="sidebar"
            onClick={() => setIsDashboardOpen(true)}
            className={sidebarClasses.sidebarButton}
            data-collapsed={isCollapsed}
          >
            <IoSettingsOutline  className=" -ml-[0.15em] mr-1" color="var(--mantine-color-gray-7)" size={20} />
            <span className={sidebarClasses.sidebarButtonText} data-collapsed={isCollapsed}>
              Dashboard
            </span>
          </Button>
          
          <Button
            variant="sidebar"
            onClick={async () => {
              await supabase.auth.signOut();
              router.push('/');
            }}
            className={sidebarClasses.sidebarButton}
            data-collapsed={isCollapsed}
            color="red"
          >
            <IoLogOutOutline  className=" -ml-[0.15em] mr-1" color="var(--mantine-color-gray-7)" size={20} />
            <span className={sidebarClasses.sidebarButtonText} data-collapsed={isCollapsed}>
              Sign Out
            </span>
          </Button>
        </Stack>
      </Box>

      <DashboardModal 
        isOpen={isDashboardOpen} 
        onClose={() => setIsDashboardOpen(false)} 
      />

      <Modal
        opened={deletionModalOpen}
        onClose={handleCancelDeletion}
        title="Processing Failed"
        centered
        size="sm"
      >
        <Stack gap="md">
          <Text>
            Processing failed. This videotext will be deleted. Please try again.
          </Text>
          <Flex gap="sm" justify="flex-end">
            <Button variant="outline" onClick={handleCancelDeletion}>
              Cancel
            </Button>
            <Button color="red" onClick={handleConfirmDeletion}>
              Delete
            </Button>
          </Flex>
        </Stack>
      </Modal>
    </Stack>
  );
}
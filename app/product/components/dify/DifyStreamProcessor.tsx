'use client';

import { useStreamProcessor, StreamData } from '@/app/hooks/useStreamProcessor';
import { NodeStatus } from './NodeStatusDisplay';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
}

interface DifyStreamProcessorProps {
  inputText: string;
  conversationId: string | null;
  onStreamStart: () => void;
  onStreamEnd: () => void;
  onMessageReceived: (message: Message) => void;
  onConversationIdChange: (id: string) => void;
  onNodeStatusChange: (statusOrUpdater: NodeStatus | ((prev: NodeStatus) => NodeStatus)) => void;
  onSummaryReceived: (summaryContent: string) => void;
  onConversationRenamed: () => void;
}

export function DifyStreamProcessor({
  inputText,
  conversationId,
  onStreamStart,
  onStreamEnd,
  onMessageReceived,
  onConversationIdChange,
  onNodeStatusChange,
  onSummaryReceived,
  onConversationRenamed,
}: DifyStreamProcessorProps) {
  const handleData = async ({ accumulatedAnswer, messageId, conversationId: newConversationId }: StreamData) => {
    if (newConversationId && newConversationId !== conversationId) {
      onConversationIdChange(newConversationId);
    }

    if (accumulatedAnswer) {
      try {
        const finalResponse = JSON.parse(accumulatedAnswer);
        
        if (typeof finalResponse.content === 'string' && typeof finalResponse.chat_message === 'string') {
          onSummaryReceived(finalResponse.content);
          onMessageReceived({
            id: messageId || Date.now().toString() + '-ai',
            type: 'ai',
            content: finalResponse.chat_message,
          });

          if (newConversationId) {
            await fetch('/api/product/messages', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                conversationId: newConversationId,
                content: finalResponse.chat_message,
                messageId: messageId,
              }),
            });
          }

          if (finalResponse.video_title && newConversationId) {
            const renameResponse = await fetch(`/api/product/conversations/${newConversationId}/rename`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ name: finalResponse.video_title }),
            });
            if (renameResponse.ok) {
              onConversationRenamed();
            }
          }
        } else {
            onMessageReceived({
                id: messageId || Date.now().toString() + '-ai-fallback',
                type: 'ai',
                content: accumulatedAnswer,
              });
    
              if (newConversationId) {
                await fetch('/api/product/messages', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    conversationId: newConversationId,
                    content: accumulatedAnswer,
                    messageId: messageId,
                  }),
                });
              }
        }
      } catch {
        onMessageReceived({
          id: messageId || Date.now().toString() + '-ai-fallback',
          type: 'ai',
          content: accumulatedAnswer,
        });

        if (newConversationId) {
          await fetch('/api/product/messages', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              conversationId: newConversationId,
              content: accumulatedAnswer,
              messageId: messageId,
            }),
          });
        }
      }
    } else {
        onMessageReceived({
            id: Date.now().toString() + '-error',
            type: 'ai',
            content: 'No response was generated. Please try again.',
          });
    }
  };

  const handleError = (error: string) => {
    onMessageReceived({
      id: Date.now().toString() + '-error',
      type: 'ai',
      content: `Error getting response: ${error}. Please try again.`,
    });
  };

  useStreamProcessor({
    url: '/api/product/stream_response',
    body: { text: inputText, conversation_id: conversationId },
    enabled: !!inputText.trim(),
    onStreamStart,
    onStreamEnd,
    onNodeStatusChange,
    onData: handleData,
    onError: handleError,
  });

  return null;
} 
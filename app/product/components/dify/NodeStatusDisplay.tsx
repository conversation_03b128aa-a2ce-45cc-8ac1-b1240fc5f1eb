'use client';

import React from 'react';

// Node status interface for tracking workflow progress
export interface NodeStatus {
  currentNode: string;
  currentNodeIndex: number;
  totalNodes: number;
  workflowRunId: string | null;
  status: 'running' | 'finished' | 'none';
}

interface NodeStatusDisplayProps {
  nodeStatus: NodeStatus;
}

export function NodeStatusDisplay({ nodeStatus }: NodeStatusDisplayProps) {
  if (nodeStatus.status === 'none') return null;
  
  return (
    <div className="flex justify-start mb-4">
        <div className="flex flex-col">
          {nodeStatus.currentNode && (
            <div className="text-gray-500 animate-pulse p-[0.25em] px-[0.75em] font-medium rounded-full bg-gray-200 border border-gray-300">
              {nodeStatus.currentNode}
            </div>
          )}
      </div>
    </div>
  );
} 
'use client';

import React, { useState } from 'react';
import { Stack, Box, Flex, Text, Textarea, Button, Group, Loader } from '@mantine/core';
import { IconMinus } from '@tabler/icons-react';
import { IoChatboxEllipsesOutline } from "react-icons/io5";
import ReactMarkdown from 'react-markdown';
import { NodeStatusDisplay, NodeStatus } from '../dify/NodeStatusDisplay';
import { Message } from '../../lib/types';
import chatWindowClasses from '../../theme/ChatWindow.module.css';

interface ChatWindowProps {
  currentChatTitle: string;
  messages: Message[];
  loading: boolean;
  nodeStatus: NodeStatus;
  inputValue: string;
  onInputChange: (value: string) => void;
  onSendMessage: () => void;
  onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  validationError: string | null;
  onClearValidationError: () => void;
  chatContainerRef: React.Ref<HTMLDivElement>;
  chatMinimized: boolean;
  onToggleChatMinimized: () => void;
  isCollapsed: boolean;
  conversationReady: boolean;
}

const ChatWindow: React.FC<ChatWindowProps> = ({
  messages,
  loading,
  nodeStatus,
  inputValue,
  onInputChange,
  onSendMessage,
  onKeyDown,
  validationError,
  onClearValidationError,
  chatContainerRef,
  chatMinimized,
  onToggleChatMinimized,
  conversationReady,
}) => {
  const [isAnimating, setIsAnimating] = useState(false);

  const handleToggleChat = () => {
    if (chatMinimized) {
      // Expanding: Hide label first, then expand
      setIsAnimating(true);
      setTimeout(() => {
        onToggleChatMinimized();
        setIsAnimating(false);
      }, 200);
    } else {
      // Collapsing: Collapse first, then show label
      setIsAnimating(true);
      onToggleChatMinimized();
      setTimeout(() => {
        setIsAnimating(false);
      }, 300);
    }
  };

  const renderChatMessages = () => (
		<Stack>
			{messages.map((message) => {
				return (
					<Flex
						key={message.id}
						className={`${chatWindowClasses.messageContainer} ${
							message.type === 'user'
								? chatWindowClasses.userMessageContainer
								: chatWindowClasses.aiMessageContainer
						}`}
					>
						<Box
							className={`${chatWindowClasses.messageBubble} ${
								message.type === 'user'
									? chatWindowClasses.userMessageBubble
									: chatWindowClasses.aiMessageBubble
							}`}
						>
							{message.type === 'user' ? (
								<Text>{message.content}</Text>
							) : message.content === 'loading' ? (
								<Flex align="center" gap="sm">
									<Loader size="xs" />
									<Text c="dimmed">Getting transcript</Text>
								</Flex>
							) : (
								<Box>
									<ReactMarkdown>
										{message.content}
									</ReactMarkdown>
								</Box>
							)}
						</Box>
					</Flex>
				);
			})}
			{loading && nodeStatus.status !== 'none' && <NodeStatusDisplay nodeStatus={nodeStatus} />}
			{messages.length === 0 && !loading && (
				<Flex align="center" justify="center" h="100%" style={{ textAlign: 'center', color: '#d1d5db', padding: '32px 0', fontSize: '18px' }}>
					No messages yet.
				</Flex>
			)}
		</Stack>
	);

  return (
    <Box 
      className={chatWindowClasses.chatWindowRoot} 
    >
      {/* Minimized Button with Label */}
      {chatMinimized && (
        <Button
          className={chatWindowClasses.chatWindowExpandButton}
          onClick={handleToggleChat}
          disabled={isAnimating}
          data-ready={conversationReady}
        >
          <IoChatboxEllipsesOutline size={20} className="mt-[0.1em] mr-1" color="var(--mantine-color-gray-7)"/> Chat with video
        </Button>
      )}

      {/* Chat Window */}
      <Stack 
        className={chatWindowClasses.chatWindow}
        data-minimized={chatMinimized}
      >
        {/* Minimize Button */}
        <Button
          onClick={handleToggleChat}
          variant="light"
          size="xs"
          disabled={isAnimating}
          className={chatWindowClasses.minimizeButton}
        >
          <IconMinus size={16} />
        </Button>

        {/* Chat Messages */}
        <Box 
          className={chatWindowClasses.messagesContainer}
          ref={chatContainerRef}
        >
          {renderChatMessages()}
        </Box>
        
        {/* Error Display */}
        {validationError && (
          <Box p="sm" style={{ backgroundColor: '#fee2e2', border: '1px solid #fca5a5', borderRadius: '4px', margin: '0.5rem' }}>
            <Group justify="space-between" align="center">
              <Text size="sm" c="red">{validationError}</Text>
              <Button 
                variant="subtle" 
                size="xs" 
                c="red" 
                onClick={onClearValidationError}
                style={{ padding: '0.25rem' }}
              >
                ×
              </Button>
            </Group>
          </Box>
        )}

        {/* Chat Input */}

          <Flex 
            className={chatWindowClasses.chatMessageInput}
          >
            <Textarea
              value={inputValue}
              onChange={(e) => onInputChange(e.target.value)}
              onKeyDown={onKeyDown}
              placeholder="Your message..."
              disabled={loading || isAnimating}
              classNames={{
                input: chatWindowClasses.chatMessageTextareaInput,
                wrapper: chatWindowClasses.chatMessageInputWrapper,
                root: chatWindowClasses.chatMessageInputRoot,
                label: chatWindowClasses.chatMessageInputLabel,
              }}
            />
            <Button
              onClick={onSendMessage}
              disabled={loading || !inputValue.trim() || isAnimating}
              size="sm"
              className={chatWindowClasses.chatMessageSendButton}
            >
              {loading ? (
                <svg className="animate-spin h-4 w-4 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 0118-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : 'Send'}
            </Button>
          </Flex>
      </Stack>
    </Box>
  );
};

export default ChatWindow; 
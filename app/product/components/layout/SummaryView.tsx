'use client';

import { useState, useEffect, useCallback } from 'react';
import { Stack, Box, Text } from '@mantine/core';
import ReactMarkdown from 'react-markdown';
import ProgressBar from '../ui/ProgressBar';
import NewChatInput from '../ui/NewChatInput';
import { SummaryStreamProcessor } from '../dify/SummaryStreamProcessor';
import { NodeStatus } from '../dify/NodeStatusDisplay';
import summaryClasses from '../../theme/Summary.module.css';

interface SummaryViewProps {
  conversationId: string | null;
  summaryContent: string | null;
  pendingConversations?: Set<string>;
  currentVideoUrl?: string | null;
  onSummaryReceived?: (summaryContent: string) => void;
  showNewChatInput?: boolean;
  onConversationCreated?: (conversationId: string, title: string, summary: string | null, videoUrl?: string) => void;
  onNewChatError?: (error: string) => void;
  onNewChatCancel?: () => void;
  onCreateBackground?: (youtubeUrl: string) => void;
  conversationProgress?: { partialSummary?: string; currentStep?: string; [key: string]: any } | null;
}

const SummaryView: React.FC<SummaryViewProps> = ({ 
  conversationId, 
  summaryContent, 
  pendingConversations = new Set(),
  currentVideoUrl,
  onSummaryReceived,
  showNewChatInput = false,
  onConversationCreated,
  onNewChatError,
  onNewChatCancel,
  onCreateBackground,
  conversationProgress
}) => {
  const [progressPhase, setProgressPhase] = useState<'fetching' | 'processing' | 'completed'>('fetching');
  const [transcriptLength, setTranscriptLength] = useState(0);
  const [showProgress, setShowProgress] = useState(false);
  const [nodeStatus, setNodeStatus] = useState<NodeStatus>({
    currentNode: '',
    currentNodeIndex: 0,
    totalNodes: 0,
    workflowRunId: null,
    status: 'none'
  });
  const [streamingActive, setStreamingActive] = useState(false);
  const [progressMessage, setProgressMessage] = useState<string | null>(null);

  const isConversationPending = conversationId && pendingConversations.has(conversationId);
  
  // Reset progress when conversation changes
  useEffect(() => {
    if (isConversationPending) {
      setProgressPhase('fetching');
      setShowProgress(true);
      
      // Start with transcript fetching phase
      setNodeStatus({
        currentNode: 'Fetching transcript from YouTube...',
        currentNodeIndex: 0,
        totalNodes: 0,
        workflowRunId: null,
        status: 'running'
      });
      
      // Simulate transcript fetch completion after 3-5 seconds, then start streaming
      const transcriptTimer = setTimeout(() => {
        const estimatedLength = 8000 + Math.random() * 12000; // 8-20k characters
        setTranscriptLength(estimatedLength);
        setProgressPhase('processing');
        setStreamingActive(true); // Activate streaming for summary generation
      }, 3000 + Math.random() * 2000); // 3-5 seconds
      
      return () => clearTimeout(transcriptTimer);
    } else if (summaryContent) {
      setProgressPhase('completed');
      setStreamingActive(false);
      // Hide progress bar after a short delay to show completion
      const completeTimer = setTimeout(() => {
        setShowProgress(false);
        setNodeStatus({
          currentNode: '',
          currentNodeIndex: 0,
          totalNodes: 0,
          workflowRunId: null,
          status: 'none'
        });
      }, 500);
      return () => clearTimeout(completeTimer);
    } else {
      setShowProgress(false);
      setStreamingActive(false);
      setNodeStatus({
        currentNode: '',
        currentNodeIndex: 0,
        totalNodes: 0,
        workflowRunId: null,
        status: 'none'
      });
    }
  }, [isConversationPending, summaryContent]);

  // Handle progress completion
  const handleProgressComplete = () => {
    setShowProgress(false);
  };

  // Streaming handlers
  const handleStreamStart = useCallback(() => {
    console.log('Summary streaming started');
  }, []);

  const handleStreamEnd = useCallback(() => {
    console.log('Summary streaming ended');
    setStreamingActive(false);
  }, []);

  const handleNodeStatusChange = useCallback((statusOrUpdater: NodeStatus | ((prev: NodeStatus) => NodeStatus)) => {
    setNodeStatus(prev => {
      if (typeof statusOrUpdater === 'function') {
        return statusOrUpdater(prev);
      }
      return statusOrUpdater;
    });
  }, []);

  const handleSummaryReceived = useCallback((summaryContent: string) => {
    console.log('Summary received:', summaryContent);
    onSummaryReceived?.(summaryContent);
  }, [onSummaryReceived]);

  const handleStreamError = useCallback((error: string) => {
    console.error('Summary stream error:', error);
    setNodeStatus({
      currentNode: 'Error generating summary',
      currentNodeIndex: 0,
      totalNodes: 0,
      workflowRunId: null,
      status: 'finished'
    });
    setProgressMessage(null);
  }, []);
  
  const handleProgressUpdate = useCallback((progress: { type: string; message?: string; [key: string]: any }) => {
    console.log('Progress update:', progress);
    
    // Update progress message based on the type of progress
    switch (progress.type) {
      case 'stream_started':
        setProgressMessage('Starting summary generation...');
        break;
      case 'streaming_progress':
        if (progress.message) {
          setProgressMessage(progress.message);
        }
        break;
      case 'message_accumulated':
        setProgressMessage(`Processed ${progress.messageCount} messages (${progress.totalLength} characters)`);
        break;
      case 'conversation_id_received':
        setProgressMessage('Connected to summary service...');
        break;
      case 'stream_completed':
        setProgressMessage('Summary generation completed!');
        // Clear progress message after a delay
        setTimeout(() => setProgressMessage(null), 3000);
        break;
      default:
        if (progress.message) {
          setProgressMessage(progress.message);
        }
    }
  }, []);

  const renderMainContent = () => {
    if (conversationId) {
      const partialSummary = conversationProgress?.partialSummary;
      const currentStep = conversationProgress?.currentStep;
      
      if (isConversationPending && showProgress) {
        // If we're in the summarizing phase and have partial content, show it
        if (currentStep === 'summarizing' && partialSummary) {
          return (
            <div className={summaryClasses.summaryContent}>
              <ReactMarkdown>{partialSummary}</ReactMarkdown>
              <div style={{ marginTop: '1rem', display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'center' }}>
                <Text size="sm" color="dimmed">
                  Generating summary...
                </Text>
              </div>
            </div>
          );
        }
        
        return (
          <div className={summaryClasses.progressContainer}>
            <ProgressBar
              phase={progressPhase}
              transcriptLength={transcriptLength}
              nodeStatus={nodeStatus}
              onComplete={handleProgressComplete}
            />
            {progressMessage && (
              <Text size="sm" color="dimmed" mt="xs" ta="center">
                {progressMessage}
              </Text>
            )}
          </div>
        );
      }
      
      if (summaryContent) {
        return (
          <div className={summaryClasses.summaryContent}>
            <ReactMarkdown>{summaryContent}</ReactMarkdown>
          </div>
        );
      }
    }

    return null;
  };

  return (
    <>
      {conversationId && (
        <SummaryStreamProcessor
          conversationId={conversationId}
          youtubeUrl={currentVideoUrl || null}
          isActive={streamingActive && !isConversationPending}
          onStreamStart={handleStreamStart}
          onStreamEnd={handleStreamEnd}
          onNodeStatusChange={handleNodeStatusChange}
          onSummaryReceived={handleSummaryReceived}
          onError={handleStreamError}
          onProgressUpdate={handleProgressUpdate}
        />
      )}
      <Stack 
        h="100%" 
        style={{ 
          flex: 1,
          overflow: 'hidden',
          backgroundColor: '#f9fafb',
          position: 'relative'
        }}
        align="center"
      >
        <Box className={summaryClasses.summary}>
          {renderMainContent()}
          {showNewChatInput && (
            <div className={summaryClasses.overlay}>
              <NewChatInput
                onConversationCreated={onConversationCreated!}
                onError={onNewChatError}
                onCancel={onNewChatCancel}
                onCreateBackground={onCreateBackground}
              />
            </div>
          )}
        </Box>
      </Stack>
    </>
  );
};

export default SummaryView; 
'use client';

import React, { useState, useEffect } from 'react';
import { Box, Progress, Text } from '@mantine/core';
import { NodeStatus } from '../dify/NodeStatusDisplay';

interface ProgressBarProps {
  phase: 'fetching' | 'processing' | 'completed';
  transcriptLength?: number;
  nodeStatus?: NodeStatus;
  onComplete?: () => void;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ 
  phase, 
  transcriptLength = 0,
  nodeStatus,
  onComplete 
}) => {
  const [progress, setProgress] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState(0);

  // Estimate processing time based on transcript length
  useEffect(() => {
    if (phase === 'processing' && transcriptLength > 0) {
      // Base time: 5 seconds + 0.5 seconds per 1000 characters
      const baseTime = 0;
      const perCharTime = 1.2;
      const estimated = baseTime + (transcriptLength * perCharTime);
      setEstimatedTime(estimated);
      setProgress(0);
      setTimeElapsed(0);
    }
  }, [phase, transcriptLength]);

  // Handle progress animation
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (phase === 'processing' && estimatedTime > 0) {
      interval = setInterval(() => {
        setTimeElapsed(prev => {
          const newTimeElapsed = prev + 100;
          const newProgress = Math.min((newTimeElapsed / estimatedTime) * 100, 100);
          setProgress(newProgress);
          return newTimeElapsed;
        });
      }, 100);
    } else if (phase === 'completed') {
      setProgress(100);
      setTimeout(() => {
        onComplete?.();
      }, 500);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [phase, estimatedTime, onComplete]);

  const getStatusText = () => {
    // If we have node status information, prioritize that
    if (nodeStatus && nodeStatus.status !== 'none' && nodeStatus.currentNode) {
      return nodeStatus.currentNode;
    }

    switch (phase) {
      case 'fetching':
        return 'Fetching transcript from YouTube...';
      case 'processing':
        if (progress < 30) {
          return 'Generating summary...';
        } else if (progress < 70) {
          return 'Analyzing content...';
        } else if (progress < 100) {
          return 'Finalizing summary...';
        } else {
          return 'Completing processing...';
        }
      case 'completed':
        return 'Summary ready!';
      default:
        return 'Processing...';
    }
  };

  const getProgressValue = () => {
    if (phase === 'fetching') {
      return 100; // Infinite animation
    }
    
    // Always use time-based progress for the actual progress bar
    return progress;
  };

  if (phase === 'completed') {
    return null; // Don't render anything when completed
  }

  return (
    <Box 
      style={{ 
        width: '400px',
        margin: '0 auto',
        padding: '2rem 1rem'
      }}
    >
      <Progress
        value={getProgressValue()}
        animated={phase === 'fetching'}
        size="sm"
        color="blue"
        style={{ marginBottom: '0.5rem', width: '100%' }}
      />
      <Text size="sm" ta="center" c="dimmed">
        {getStatusText()}
      </Text>
      {/* Show node progress information and time estimation */}
      {phase === 'processing' && estimatedTime > 0 && (
        <Text size="xs" ta="center" c="dimmed" style={{ marginTop: '0.25rem' }}>
          {nodeStatus && nodeStatus.status === 'running' && nodeStatus.totalNodes > 0 ? (
            <>
              Step {nodeStatus.currentNodeIndex} of {nodeStatus.totalNodes} • {' '}
              {progress < 100 
                ? `${Math.ceil((estimatedTime - timeElapsed) / 1000)}s remaining`
                : 'Finishing up...'
              }
            </>
          ) : (
            progress < 100 
              ? `Estimated time: ${Math.ceil((estimatedTime - timeElapsed) / 1000)}s remaining`
              : 'Finishing up...'
          )}
        </Text>
      )}
    </Box>
  );
};

export default ProgressBar; 
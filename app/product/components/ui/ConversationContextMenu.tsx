import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Text, Group, Title } from '@mantine/core';
import { IconPencil, IconTrash } from '@tabler/icons-react';
import { useContextMenu } from 'mantine-contextmenu';

interface ConversationContextMenuProps {
  children: React.ReactNode;
  conversationId: string;
  currentName: string;
  onDelete: (conversationId: string) => Promise<void>;
  onStartRename: (conversationId: string) => void;
}

export function ConversationContextMenu({
  children,
  conversationId,
  currentName,
  onDelete,
  onStartRename,
}: ConversationContextMenuProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const { showContextMenu } = useContextMenu();

  const handleDeleteConfirm = async () => {
    setIsDeleting(true);
    setDeleteError(null);
    try {
      await onDelete(conversationId);
      setIsDeleteDialogOpen(false);
    } catch (error) {
        console.error("Delete failed:", error);
        setDeleteError(error instanceof Error ? error.message : 'Failed to delete conversation.');
    } finally {
      setIsDeleting(false);
    }
  };

  const openDeleteDialog = () => {
    setDeleteError(null);
    setIsDeleteDialogOpen(true);
  };

  return (
    <>
      <div 
        onContextMenu={showContextMenu([
          {
            key: 'rename',
            icon: <IconPencil size={14} />,
            title: 'Rename',
            onClick: () => onStartRename(conversationId),
          },
          {
            key: 'delete',
            icon: <IconTrash size={14} />,
            title: 'Delete',
            color: 'red',
            onClick: openDeleteDialog,
          },
        ])}
        style={{ width: '100%' }}
      >
        {children}
      </div>

      <Modal
        opened={isDeleteDialogOpen}
        onClose={() => !isDeleting && setIsDeleteDialogOpen(false)}
        centered
        closeOnClickOutside={!isDeleting}
        closeOnEscape={!isDeleting}
        withCloseButton={false}
      >
        <Modal.Body>
        <Title order={4}>Delete Conversation</Title>
        <Text size="sm" mt="md">
          This action cannot be undone. This will permanently delete the
          conversation &quot;{currentName}&quot;.
        </Text>

        {deleteError && <Text color="red" size="sm" mt="sm">{deleteError}</Text>}

        <Group justify="space-between" mt="lg">
          <Button variant="default" onClick={() => setIsDeleteDialogOpen(false)} disabled={isDeleting}>
            Cancel
          </Button>
          <Button
            color="red"
            onClick={handleDeleteConfirm}
            loading={isDeleting}
          >
            Delete
          </Button>
        </Group>
        </Modal.Body>
      </Modal>
    </>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { Group, Box, Text, Progress } from '@mantine/core';
import { NodeStatus } from '../dify/NodeStatusDisplay';
import progressClasses from '../../../theme/Progress.module.css';
import { useConversationProgress } from '@/app/hooks/useConversationProgress';

interface Step {
  key: string;
  label: string;
  status: 'pending' | 'active' | 'completed';
  progress: number;
}

export interface ConversationProgress {
  currentStep: 'fetching' | 'optimizing' | 'summarizing' | 'completed';
  transcriptProgress: number;
  optimizationProgress: number;
  summaryProgress: number;
  transcriptLength: number;
  currentStepText: string;
  partialSummary?: string;
}

interface ThreeStepProgressProps {
  conversationProgress?: ConversationProgress;
  nodeStatus?: NodeStatus; // Keep for backward compatibility
  transcriptLength?: number; // Keep for backward compatibility
  conversationId?: string; // If provided, will automatically subscribe to progress updates
  onProgressUpdate?: (progress: ConversationProgress) => void; // Callback for progress updates
  onCompleted?: (conversationId: string) => void; // Callback when processing completes
  onFailed?: (conversationId: string, error: string) => void; // Callback when processing fails
}

const ThreeStepProgress: React.FC<ThreeStepProgressProps> = ({
  conversationProgress,
  nodeStatus,
  transcriptLength = 0,
  conversationId,
  onProgressUpdate,
  onCompleted,
  onFailed
}) => {
  const [steps, setSteps] = useState<Step[]>([
    { key: 'transcript', label: 'Fetching transcript', status: 'pending', progress: 0 },
    { key: 'optimize', label: 'Optimizing transcript', status: 'pending', progress: 0 },
    { key: 'summary', label: 'Creating summary', status: 'pending', progress: 0 }
  ]);

  const [timeElapsed, setTimeElapsed] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState(0);

  // Use the progress hook if conversationId is provided
  const { progress: hookProgress, subscribeToProgress } = useConversationProgress();

  // Use hook progress if available, otherwise use prop
  const activeProgress = conversationProgress || hookProgress;

  // Subscribe to progress updates if conversationId is provided
  useEffect(() => {
    if (conversationId && subscribeToProgress) {
      const cleanup = subscribeToProgress(conversationId, {
        onProgressUpdate: (progress) => {
          onProgressUpdate?.(progress);
        },
        onCompleted: (convId) => {
          onCompleted?.(convId);
        },
        onFailed: (convId, error) => {
          onFailed?.(convId, error);
        }
      });

      return cleanup;
    }
  }, [conversationId, subscribeToProgress, onProgressUpdate, onCompleted, onFailed]);

  // Update steps based on conversation progress (preferred) or fallback to node status
  useEffect(() => {
    if (activeProgress) {
      setSteps(prevSteps => {
        const newSteps = [...prevSteps];
        
        switch (activeProgress.currentStep) {
          case 'fetching':
            newSteps[0].status = 'active';
            newSteps[0].progress = activeProgress.transcriptProgress;
            newSteps[1].status = 'pending';
            newSteps[1].progress = 0;
            newSteps[2].status = 'pending';
            newSteps[2].progress = 0;
            break;
          case 'optimizing':
            newSteps[0].status = 'completed';
            newSteps[0].progress = 100;
            newSteps[1].status = 'active';
            newSteps[1].progress = activeProgress.optimizationProgress;
            newSteps[2].status = 'pending';
            newSteps[2].progress = 0;
            break;
          case 'summarizing':
            newSteps[0].status = 'completed';
            newSteps[0].progress = 100;
            newSteps[1].status = 'completed';
            newSteps[1].progress = 100;
            newSteps[2].status = 'active';
            newSteps[2].progress = activeProgress.summaryProgress;
            break;
          case 'completed':
            newSteps[0].status = 'completed';
            newSteps[0].progress = 100;
            newSteps[1].status = 'completed';
            newSteps[1].progress = 100;
            newSteps[2].status = 'completed';
            newSteps[2].progress = 100;
            break;
        }
        
        return newSteps;
      });
    } else if (nodeStatus && nodeStatus.status !== 'none') {
      // Fallback to old nodeStatus logic for backward compatibility
      const currentNode = nodeStatus.currentNode.toLowerCase();
      
      setSteps(prevSteps => {
        const newSteps = [...prevSteps];
        
        if (currentNode.includes('transcript')) {
          newSteps[0].status = 'active';
          newSteps[1].status = 'pending';
          newSteps[2].status = 'pending';
        } else if (currentNode.includes('optimiz')) {
          newSteps[0].status = 'completed';
          newSteps[0].progress = 100;
          newSteps[1].status = 'active';
          newSteps[2].status = 'pending';
        } else if (currentNode.includes('summary') || currentNode.includes('generat')) {
          newSteps[0].status = 'completed';
          newSteps[0].progress = 100;
          newSteps[1].status = 'completed';
          newSteps[1].progress = 100;
          newSteps[2].status = 'active';
        } else if (nodeStatus.status === 'finished') {
          newSteps[0].status = 'completed';
          newSteps[0].progress = 100;
          newSteps[1].status = 'completed';
          newSteps[1].progress = 100;
          newSteps[2].status = 'completed';
          newSteps[2].progress = 100;
        }
        
        return newSteps;
      });
    }
  }, [activeProgress, nodeStatus]);

  // Calculate estimated time based on transcript length
  useEffect(() => {
    const length = activeProgress?.transcriptLength || transcriptLength;
    if (length > 0) {
      // Base time: 10 seconds + 1 second per 1000 characters
      const baseTime = 10000;
      const perCharTime = 1;
      const estimated = baseTime + (length * perCharTime);
      setEstimatedTime(estimated);
      setTimeElapsed(0);
    }
  }, [activeProgress?.transcriptLength, transcriptLength]);

  // Handle progress animation for active step
  useEffect(() => {
    let interval: NodeJS.Timeout;

    // Always show some progress animation for active steps
    const activeStep = steps.find(step => step.status === 'active');
    if (activeStep && !conversationProgress) {
      // Time-based animation when no explicit progress is provided
      if (estimatedTime > 0) {
        interval = setInterval(() => {
          setTimeElapsed(prev => {
            const newTimeElapsed = prev + 100;
            
            // Update active step progress
            setSteps(prevSteps => {
              const newSteps = [...prevSteps];
              const activeIndex = newSteps.findIndex(step => step.status === 'active');
              if (activeIndex !== -1) {
                // Calculate step-specific progress (each step gets ~33% of total time)
                const stepProgress = Math.min(((newTimeElapsed % (estimatedTime / 3)) / (estimatedTime / 3)) * 100, 95);
                newSteps[activeIndex].progress = stepProgress;
              }
              return newSteps;
            });
            
            return newTimeElapsed;
          });
        }, 100);
      }
    } else if (activeStep && conversationProgress) {
      // Show a gentle pulsing animation even when we have explicit progress
      interval = setInterval(() => {
        setSteps(prevSteps => {
          const newSteps = [...prevSteps];
          const activeIndex = newSteps.findIndex(step => step.status === 'active');
          if (activeIndex !== -1 && newSteps[activeIndex].progress < 100) {
            // Add a small increment to show activity
            const currentProgress = newSteps[activeIndex].progress;
            newSteps[activeIndex].progress = Math.min(currentProgress + 0.5, 95);
          }
          return newSteps;
        });
      }, 200);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [steps, estimatedTime, conversationProgress]);



  // Get label for progress bar section
  const getStepLabel = () => {
    // No labels inside the progress bar
    return '';
  };

  // Calculate width for each step based on status
  const getStepWidth = (stepIndex: number) => {
    // Check if all steps are completed
    const allCompleted = steps.every(step => step.status === 'completed');
    if (allCompleted) {
      return 100 / steps.length; // Equal width for all when complete (33.33% each)
    }
    
    const step = steps[stepIndex];
    if (step.status === 'active') {
      return 50; // Active step gets 50%
    } else {
      return 25; // Other steps get 25%
    }
  };

  // Calculate progress value for each step
  const getStepProgressValue = (stepIndex: number) => {
    const step = steps[stepIndex];
    const stepWidth = getStepWidth(stepIndex);
    
    if (step.status === 'completed') {
      return stepWidth;
    } else if (step.status === 'active') {
      return Math.max((step.progress / 100) * stepWidth, 5);
    } else {
      return 0;
    }
  };

  // Get the current status message to display to the left of the progress bar
  const getCurrentStatusMessage = () => {
    const activeStep = steps.find(step => step.status === 'active');
    
    if (activeStep) {
      // Show current step text or node status for active step
      if (activeProgress?.currentStepText) {
        return activeProgress.currentStepText;
      } else if (nodeStatus?.currentNode) {
        return nodeStatus.currentNode;
      }
      
      // Fallback to step-specific messages
      switch (activeStep.key) {
        case 'transcript':
          return 'Fetching Transcript';
        case 'optimize':
          return 'Optimizing Transcript';
        case 'summary':
          return 'Creating Summary';
        default:
          return activeStep.label;
      }
    }
    
    const completedSteps = steps.filter(step => step.status === 'completed');
    if (completedSteps.length === steps.length) {
      return 'Complete';
    }
    
  };

  const activeStep = steps.find(step => step.status === 'active');
  const remainingTime = activeStep && estimatedTime > 0 ? Math.max(0, Math.ceil((estimatedTime - timeElapsed) / 1000)) : 0;
  const currentStatusMessage = getCurrentStatusMessage();
  const allCompleted = steps.every(step => step.status === 'completed');

  return (
    <Group 
      gap="md" 
      wrap="nowrap" 
      style={{ 
        alignItems: 'center',
        marginTop: allCompleted ? '100px' : '0',
        transition: 'margin-top 0.5s ease'
      }}
    >
      {/* Status message takes remaining space */}
      <Box style={{ flex: 1, textAlign: 'right', minWidth: 0 }}>
        <Text 
          size="sm" 
          truncate
          className={progressClasses.label}
          data-completed={steps[0].status && steps[1].status && steps[2].status === 'completed'}
        >
          {currentStatusMessage}
          {remainingTime > 0 && (
            <Text size="xs" c="dimmed" component="span" ml="xs">
              (~{remainingTime}s)
            </Text>
          )}
        </Text>
      </Box>
      
      {/* Progress bar with fixed width */}
      <Box w="240px" style={{ flexShrink: 0 }}>
        <Progress.Root size="xl" style={{ width: '100%' }}>
          <Progress.Section 
            value={getStepProgressValue(0)} 
            data-completed={steps[0].status === 'completed'}
          >
            <Progress.Label>{getStepLabel()}</Progress.Label>
          </Progress.Section>
          <Progress.Section 
            value={getStepProgressValue(1)} 
            data-completed={steps[1].status === 'completed'}
          >
            <Progress.Label>{getStepLabel()}</Progress.Label>
          </Progress.Section>
          <Progress.Section 
            value={getStepProgressValue(2)} 
            data-completed={steps[2].status === 'completed'}
          >
            <Progress.Label>{getStepLabel()}</Progress.Label>
          </Progress.Section>
        </Progress.Root>
      </Box>
    </Group>
  );
};

export default ThreeStepProgress;
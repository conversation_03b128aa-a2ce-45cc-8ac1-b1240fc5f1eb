import React from 'react';
import { Box, Text, Group, Anchor, ActionIcon, Button, Badge } from '@mantine/core';
import { IconEye, IconThumbUp, IconClock, IconCalendar, IconExternalLink, IconPinned, IconPinnedOff } from '@tabler/icons-react';
import YouTube from 'react-youtube';
import Balancer from 'react-wrap-balancer';
import { VideoMetadata } from '../../lib/videoTypes';
import classes from '../../theme/VideoMetadataCard.module.css';

interface YouTubePlayer {
  playVideo: () => void;
  pauseVideo: () => void;
  seekTo: (seconds: number, allowSeekAhead: boolean) => void;
}

interface YouTubeEvent {
  target: YouTubePlayer;
}

interface VideoMetadataCardProps {
  metadata: VideoMetadata;
  videoUrl?: string;
  chatMinimized?: boolean;
  onVideoHover?: (isHovering: boolean) => void;
  isPinned?: boolean;
  onPinClick?: () => void;
  onPlayerReady?: (seekFunction: (seconds: number) => void) => void;
}

const VideoMetadataCard: React.FC<VideoMetadataCardProps> = ({ 
  metadata, 
  videoUrl,
  chatMinimized,
  onVideoHover,
  isPinned = false,
  onPinClick,
  onPlayerReady
}) => {
  const [isDescriptionExpanded, setIsDescriptionExpanded] = React.useState(false);

  // Handle YouTube player ready event
  const handlePlayerReady = (event: YouTubeEvent) => {
    
    // Create seek function and pass it to parent
    const seekFunction = (seconds: number) => {
      if (event.target && typeof event.target.seekTo === 'function') {
        event.target.seekTo(seconds, true);
      }
    };
    
    onPlayerReady?.(seekFunction);
  };

  // Helper function to format numbers
  const formatNumber = (num: number | null | undefined): string => {
    if (!num) return '0';
    
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Helper function to format duration
  const formatDuration = (seconds: number | null | undefined): string => {
    if (!seconds) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Helper function to format upload date
  const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return 'Unknown';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Box 
      className={classes.videoMetadataRoot} 
      data-chat-expanded={!chatMinimized}
      onMouseEnter={() => onVideoHover?.(true)}
      onMouseLeave={() => onVideoHover?.(false)}
    >
      {/* Video Player */}
      <Box className={classes.videoPlayer} style={{ position: 'relative' }}>
        <YouTube
          videoId={metadata.video_id}
          opts={{
            width: '100%',
            height: '100%',
            playerVars: {
              modestbranding: 1,
              rel: 0
            }
          }}
          onReady={handlePlayerReady}
        />
        <ActionIcon
          color={isPinned ? 'gray' : 'gray'}
          size="lg"
          className={classes.pinButton}
          onClick={(e) => {
            e.stopPropagation();
            onPinClick?.();
          }}
        >
          {isPinned ? <IconPinned size={20} /> : <IconPinnedOff size={20} />}
        </ActionIcon>
      </Box>
      
      {/* Metadata */}
      <Box className={classes.metadata}>
          {/* Title and Channel */}
            <Anchor
              href={videoUrl || `https://www.youtube.com/watch?v=${metadata.video_id}`}
              target="_blank"
              rel="noopener noreferrer"
              className={classes.title}
            >
              <Balancer>{metadata.title}</Balancer>
            </Anchor>
          {/* Stats */}
          <Group className={classes.stats}>
            {metadata.view_count && (
              <Group className={classes.statsGroup}>
                <IconEye className={classes.statsIcon} />
                <Text>{formatNumber(metadata.view_count)} views</Text>
              </Group>
            )}
            
            {metadata.like_count && (
              <Group className={classes.statsGroup}>
                <IconThumbUp className={classes.statsIcon} />
                <Text>{formatNumber(metadata.like_count)}</Text>
              </Group>
            )}
            
            {metadata.duration && (
              <Group className={classes.statsGroup}>
                <IconClock className={classes.statsIcon} />
                <Text>{formatDuration(metadata.duration)}</Text>
              </Group>
            )}
            
            {metadata.upload_date && (
              <Group className={`${classes.statsGroup} ${classes.uploadDate}`}>
                <IconCalendar className={classes.statsIcon} />
                <Text>{formatDate(metadata.upload_date)}</Text>
              </Group>
            )}

            {/* External Link */}
            <Anchor
              href={videoUrl || `https://www.youtube.com/watch?v=${metadata.video_id}`}
              target="_blank"
              rel="noopener noreferrer"
              className={`${classes.statsGroup} ${classes.externalLink}`}
            >
              <IconExternalLink className={classes.statsIcon} />
              <Text>Open on YouTube</Text>
            </Anchor>
          </Group>

          {/* Channel Name */}
          {metadata.channel_name && (
            <Group className={classes.channelInfo}>
              <Text className={classes.channelName}>{metadata.channel_name}</Text>
            </Group>
          )}

          {/* Description - Collapsible */}
          {metadata.description && (
            <Box className={classes.descriptionContainer}>
              <Text 
                className={classes.description}
                lineClamp={isDescriptionExpanded ? undefined : 3}
              >
                {metadata.description}
              </Text>
              {metadata.description.length > 150 && (
                <Button
                  variant="subtle"
                  size="xs"
                  onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                  className={classes.showMoreButton}
                >
                  {isDescriptionExpanded ? 'Show less' : 'Show more'}
                </Button>
              )}
            </Box>
          )}

          {/* Tags */}
          {metadata.tags && metadata.tags.length > 0 && (
            <Group className={classes.tagsContainer}>
              {metadata.tags.slice(0, 5).map((tag, index) => (
                <Badge key={index} variant="light" size="sm" className={classes.tag}>
                  {tag}
                </Badge>
              ))}
              {metadata.tags.length > 5 && (
                <Text size="sm" c="dimmed">+{metadata.tags.length - 5} more</Text>
              )}
            </Group>
          )}

          {/* Transcript Languages */}
          {metadata.transcript_languages && metadata.transcript_languages.length > 0 && (
            <Group className={classes.languagesContainer}>
              <Text size="sm" fw={500} c="dimmed">Available in:</Text>
              <Group gap="xs">
                {metadata.transcript_languages.slice(0, 3).map((lang, index) => (
                  <Badge key={index} variant="outline" size="xs" className={classes.languageBadge}>
                    {lang.toUpperCase()}
                  </Badge>
                ))}
                {metadata.transcript_languages.length > 3 && (
                  <Text size="xs" c="dimmed">+{metadata.transcript_languages.length - 3}</Text>
                )}
              </Group>
            </Group>
          )}
      </Box>
    </Box>
  );
};

export default VideoMetadataCard; 
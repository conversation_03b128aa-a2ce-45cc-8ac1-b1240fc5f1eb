'use client';

import { useState, useEffect } from 'react';

// Add CSS keyframes for pulse animation
const styles = `
  @keyframes animatedTitlePulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
  }
`;

interface AnimatedTitleProps {
  oldTitle: string;
  newTitle: string;
  onAnimationComplete?: () => void;
}

export function AnimatedTitle({ oldTitle, newTitle, onAnimationComplete }: AnimatedTitleProps) {
  const [displayText, setDisplayText] = useState(oldTitle);
  const [isErasing, setIsErasing] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(oldTitle.length);

  useEffect(() => {
    if (isErasing) {
      // Erasing animation
      if (currentIndex > 0) {
        const timer = setTimeout(() => {
          setDisplayText(oldTitle.slice(0, currentIndex - 1));
          setCurrentIndex(currentIndex - 1);
        }, 50); // Adjust speed as needed
        return () => clearTimeout(timer);
      } else {
        setIsErasing(false);
        setCurrentIndex(0);
      }
    } else {
      // Typing animation
      if (currentIndex < newTitle.length) {
        const timer = setTimeout(() => {
          setDisplayText(newTitle.slice(0, currentIndex + 1));
          setCurrentIndex(currentIndex + 1);
        }, 50); // Adjust speed as needed
        return () => clearTimeout(timer);
      } else if (onAnimationComplete) {
        onAnimationComplete();
      }
    }
  }, [currentIndex, isErasing, oldTitle, newTitle, onAnimationComplete]);

  // Start erasing when new title is received
  useEffect(() => {
    setIsErasing(true);
    setCurrentIndex(oldTitle.length);
    setDisplayText(oldTitle);
  }, [oldTitle, newTitle]);

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <span style={{ 
        fontWeight: 500, 
        fontSize: '14px', 
        color: 'var(--mantine-color-dark-text)',
        display: 'block',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis'
      }}>
        {displayText}
        <span style={{ animation: 'animatedTitlePulse 1s infinite' }}>|</span>
      </span>
    </>
  );
} 
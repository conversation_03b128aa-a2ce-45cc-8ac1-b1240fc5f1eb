.sidebar {
  background-color: #f9fafb;
  border-right: 1px solid #e5e7eb;
  height: 100vh;
  padding: 50px 5px 0 5px;
  flex-shrink: 0;
  transition: width .25s ease;
  width: 250px;
  position: relative;
  z-index: 30;
  display: flex;
  flex-direction: column;
}

.sidebar[data-collapsed="true"] {
  width: 60px;
}

.conversationItem {
  border-radius: var(--mantine-radius-xs);
  padding: .5rem !important;
  margin-left: -0.25em;
  margin-right: -0.25em;
}

.conversationItem:hover {
  background-color: var(--mantine-color-gray-1);
  outline: 1px solid var(--mantine-color-gray-3);
  outline-offset: -1px;
}

.conversationItem:active {
  background-color: var(--mantine-color-gray-7);
}

.collapsedConversationItem {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.25s ease;
}

.collapsedConversationItem:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.sidebarToggleCollapsed {
  background-color: var(--mantine-color-gray-1);
  justify-content: center !important;
  cursor: pointer;
  width: 40px;
  aspect-ratio: 1/1;
  border-radius: 50%;
  padding: 0 !important;
  transition: all .25s ease, left .25s ease, top .25s ease;
  position: absolute;
  left: 10px;
  top: 10px;
}

.sidebarToggle {
  background-color: var(--mantine-color-gray-1);
  justify-content: center !important;
  cursor: pointer;
  width: 36px;
  aspect-ratio: 1/1;
  border-radius: 50%;
  padding: 0 !important;
  transition: all .25s ease, left .25s ease, top .25s ease;
  position: absolute;
  top: 10px;
  left: 205px;
}

.sidebarContent {
  opacity: 1;
  visibility: visible;
  width: 240px;
  transition: opacity 0.25s ease, visibility 0.25s ease;
  pointer-events: auto;
  flex-grow: 1;
  overflow-y: auto;
  min-height: 0;
}

.sidebarContent[data-collapsed="true"] {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.sidebarLogo {
  position: absolute;
  width: 90px;
  left: 20px;
  top: 13px;
  transition: left .25s ease;
}

.sidebarLogo a img {
  height: 20px;
  padding: 0;
}

.sidebarLogo[data-collapsed="true"] {
  position: absolute;
  left: 70px;
}

.sidebarButton {
  transition: all .3s ease, width .3s ease, padding .3s ease;
  width: 100%;
  padding: 8px;
  justify-content: left;
  aspect-ratio: auto;
  overflow: visible;
  white-space: normal;
}

.sidebarButton[data-collapsed="true"] {
  width: 40px;
  padding: 8px 11px;
  aspect-ratio: 1/1;
  overflow: hidden;
  white-space: nowrap;
}

.sidebarButtonText {
  opacity: 1;
  transition: opacity .5s ease;
  margin-left: 4px;
}

.sidebarButtonText[data-collapsed="true"] {
  opacity: 0;
}

.sidebarLoader {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%); 
}
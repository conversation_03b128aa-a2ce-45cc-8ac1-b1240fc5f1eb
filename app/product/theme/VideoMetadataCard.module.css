.videoMetadataRoot {
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Allow shrinking */
  border-radius: var(--mantine-radius-xxs);
}

.videoMetadataRoot[data-chat-expanded="true"] {
  border-radius: 0;
}

.videoPlayer {
  width: 100%;
  aspect-ratio: 16/9;
  position: relative;
  background-color: black;
}

.pinButton {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.videoPlayer:hover .pinButton,
.pinButton[data-active="true"] {
  opacity: 1;
}

.videoPlayer :global(iframe) {
  position: absolute;
  top: 0;
  left: 0;
}

.metadata {
  padding: var(--mantine-spacing-sm);
  flex-shrink: 0; /* Don't shrink the metadata section */
  font-size: var(--mantine-font-size-xxs);
}



.title {
  display: inline;
  hyphens: auto;
  color: var(--mantine-color-black);
  font-weight: 600;
}

.stats {
  gap: var(--mantine-spacing-sm);
}

.statsLeft {
  gap: var(--mantine-spacing-sm);
}

.statsGroup {
  gap: var(--mantine-spacing-xs);
}

/* Show metadata when panel is expanded (hover or pinned) */
:global([data-panel-expanded="true"]) .metadata {
  display: block;
}

:global([data-panel-expanded="false"]) .metadata {
  display: none;
}



.statsIcon {
  width: 16px;
  height: 16px;
}

.description {
  cursor: pointer;
  transition: all 0.2s ease;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: var(--mantine-spacing-xs);
  display: none;

}

.description[data-expanded="true"] {
  -webkit-line-clamp: unset;
}

.showMoreText {
  font-size: var(--mantine-font-size-xs);
  cursor: pointer;
  margin-top: 4px;
  display: none;
}


.transcriptLanguages {
  gap: var(--mantine-spacing-xs);
  font-size: var(--mantine-font-size-sm);
}

.externalLink {
  text-decoration: none;
  color: var(--mantine-color-primary);
  flex-shrink: 0;
}

.externalLink:hover {
  color: var(--mantine-color-white);
}

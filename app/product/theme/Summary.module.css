.summary {
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    flex: 1;
    flex-grow: 1;
    padding: 6em 5em;
    margin-right: 1em;
    width: 100%;
    max-width: 100%;
    h1, h2, h3, h4, h5, h6 {
        font-weight: 600;
        margin-top: .5em;
        margin-bottom: .25em;
    }
    h1:first-child, h2:first-child, h3:first-child, h4:first-child, h5:first-child, h6:first-child {
        margin-top: 0;
    }
    h1 {
        font-size: var(--mantine-font-size-xxxl);
        line-height: 1;
        font-weight: 600;
    }
    h2 {
        font-size: var(--mantine-font-size-xxl);
        line-height: 1;
        font-weight: 600;
    }
    h3 {
        font-size: var(--mantine-font-size-xl);
        line-height: 1;
        font-weight: 600;
    }
    h4 {    
        font-size: var(--mantine-font-size-lg);
        line-height: 1;
        font-weight: 600;
    }
    h5 {
        font-size: var(--mantine-font-size-lg);
        line-height: 1;
        font-weight: 600;
    }
    p {
        font-size: var(--mantine-font-size-md);
        line-height: 1.5;
        font-weight: 400;
    }
    ul {
        list-style-type: square;
        list-style-position: inside;
        list-style-image: none;
        padding-left: 1em;
        font-size: var(--mantine-font-size-md);
        line-height: 1.5;
        font-weight: 400;
    }
    ol {
        list-style-type: decimal;
        list-style-position: inside;
        list-style-image: none;
        padding-left: 1em;
        font-size: var(--mantine-font-size-md);
        line-height: 1.5;
        font-weight: 400;
    }
}

.progressContainer {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.25s ease;
}

.overlay[data-collapsed="false"] {
    left: 250px;
}

.transcriptSegment {
    display: flex;
    gap: 12px;
    align-items: flex-start;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    margin-bottom: 4px;
}

.transcriptTimestamp {
    font-family: monospace;
    font-size: 11px;
    line-height: 1.4;
    color: #6c757d;
    transition: color 0.2s ease, background-color 0.2s ease;
    border-radius: 4px;
    padding: 2px 4px;
}

.transcriptTimestamp[style*="cursor: pointer"] {
    color: var(--mantine-color-primary);
}

.transcriptTimestamp[style*="cursor: pointer"]:hover {
    color: var(--mantine-color-white);
    background-color: var(--mantine-color-primary-0);
}

.transcriptText {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
}

.transcriptContainer {
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: 100%;
}


.toggleContainer {
    font-size: var(--mantine-font-size-xs);
    gap: 0;
    margin-bottom: var(--mantine-spacing-xl);
    position: fixed;
    z-index: 1;
    top: 0;
    left: 60px;
    align-items: flex-start;
    transition: all .25s ease;
    height: 100px;
    width: calc(100% - 60px);
    padding-top: 15px;
    padding-right: 15px;
    background-color: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(16px);
    mask-image: linear-gradient(
      to bottom,
      black 0% 50%,
      transparent 50% 100%
    );
}

.toggleContainer[data-collapsed="true"] {
    padding-left: 120px;
}

.toggleContainer[data-collapsed="false"] {
    padding-left: 210px;
}

.toggleContainer:after {
    content: "";
    position: absolute;
    bottom: 50px;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--mantine-color-gray-3);
    height: 1px;
}


.toggleSwitchRoot {
    justify-content: center;
}

.toggleSwitchTrack[data-checked="true"]  {
        width: auto;
        justify-content: center;
        padding: 0 .5em 0 1em;
}

.toggleSwitchTrack[data-checked="false"]  {
    width: auto;
    justify-content: center;
    padding: 0 1em 0 .5em;
}

.toggleSwitchLabel {
    font-size: .7rem;
    text-transform: uppercase;
    font-weight: 600;
} 

.toggleLabel {
    font-size: .7rem;
    text-transform: uppercase !important;
    font-weight: 600;
    margin: 0 0 -.15em 0em;
} 

.toggleLabelSummary {
    color: var(--mantine-color-green-5) !important;
} 

.toggleLabelTranscript {
    color: var(--mantine-color-gray-7) !important;
} 

.transcriptTitle {
  padding: var(--mantine-spacing-xs);
  border-bottom: 1px solid var(--mantine-color-gray-2);
  margin-bottom: var(--mantine-spacing-xs);
  color: var(--mantine-color-gray-7);
} 




.chatWindowRoot {
    display: flex;
    flex: none; /* Size to content */
    width: 100%;
    transition: width 0.25s ease;
    height: auto;
}

:global([data-chat-expanded="true"]) .chatWindowRoot {
    flex: 1;
}

.chatWindowExpandButton {
    display: flex;
    align-items: center;
    align-self: flex-end;
    gap: 8px;
    padding: 8px 16px;
    pointer-events: all;
    width: 100%;
    flex-shrink: 0;
    margin-bottom: -100px;
    white-space: nowrap;
    margin-top: var(--mantine-spacing-md);
    transition: margin-bottom 1s ease;
}

.chatWindowExpandButton[data-ready="true"] {
    margin-bottom: 0;
}

.chatWindow {
    display: flex;
    flex-direction: column;
    flex: 1; /* Take remaining space */
    overflow: hidden;
    min-height: 0; /* Allow shrinking */
    width: 100%; /* Ensure full width */
    gap: 0;
}

.chatWindow[data-minimized="false"] {
    transition: opacity 0.3s ease, height 0.3s ease;
    transition-delay: 0.3s;
    opacity: 1;
    height: auto;
    flex: none;
    width: 100%; /* Ensure full width */
}

.chatWindow[data-minimized="true"] {
    transition: opacity 0.3s ease, height 0.3s ease;
    opacity: 0;
    height: 0;
    overflow: hidden;
    flex: none;
}

.chatMessageInput {
    padding: var(--mantine-spacing-md);
    background-color: var(--mantine-color-gray-0);
    border-top: 1px solid var(--mantine-color-gray-3);
    width: 100%; /* Ensure full width */
    margin-top: 0 !important;
    font-weight: 400;
}

/* Ensure Mantine wrapper components take full width */
.chatMessageInputRoot {
    width: 100%;
}

.chatMessageTextareaInput {
    flex: 1;
    border: none !important;
    outline: none !important;
    padding: 0 !important;
    font-weight: 500;
    background-color: transparent;
    resize: none;
    width: 100%;
}

.messageContainer {
    display: flex;
    width: 100%;
    flex: 1;
    flex-grow: 1;
}

.userMessageContainer {
    justify-content: flex-start;
}

.aiMessageContainer {
    justify-content: flex-end;
}

.messageBubble {
    border-radius: var(--mantine-radius-sm);
    max-width: 90%;
    background-color: white;
    padding: 16px;
    border: 1px solid #e5e7eb;
}

.userMessageBubble {
    border-bottom-left-radius: 0;
    box-shadow: var(--mantine-shadow-sm);
}

.aiMessageBubble {
    border-bottom-right-radius: 0;
    box-shadow: var(--mantine-shadow-sm);
}

.chatMessageSendButton {
    position: absolute;
    right: var(--mantine-spacing-md);
    bottom: var(--mantine-spacing-md);
}

.minimizeButton {
    position: absolute;
    top: 8px;
    right: 8px;
    border-radius: var(--mantine-radius-xs);
    width: 32px;
    height: 32px;
    padding: 0;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
}

.messagesContainer {
    overflow-y: auto;
    font-size: 14px;
    padding: var(--mantine-spacing-md);
    flex: 1;
}


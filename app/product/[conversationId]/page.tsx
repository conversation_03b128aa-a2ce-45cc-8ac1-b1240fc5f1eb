'use client';

import { useParams } from 'next/navigation';
import ChatInterface from '../ChatInterface';
import { useUserData } from '@/contexts/UserDataContext';
import SuccessAlert from '../../components/ui/SuccessAlert';
import LoginPrompt from '../../components/ui/LoginPrompt';
import { Loader } from '@mantine/core';

export default function ConversationPage() {
	const { user, isLoading } = useUserData();
	const params = useParams();
	const conversationId = params.conversationId as string;

	if (isLoading) {
		return <div className="fixed top-0 left-0 w-full h-full flex justify-center items-center">
			<Loader />
		</div>;
	}

	return (
		<>
			<SuccessAlert />
			{!user && <LoginPrompt />}
			<ChatInterface
				initialConversationId={conversationId}
				user={user}
			/>
		</>
	);
}
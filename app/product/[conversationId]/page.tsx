'use client';

import { useParams } from 'next/navigation';
import ChatInterface from '../ChatInterface';
import { useUserData } from '@/contexts/UserDataContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import SuccessAlert from '../../components/ui/SuccessAlert';
import { Loader } from '@mantine/core';

export default function ConversationPage() {
	const { user, isLoading } = useUserData();
	const router = useRouter();
	const params = useParams();
	const conversationId = params.conversationId as string;

	useEffect(() => {
		if (!isLoading && !user) {
			router.push('/auth');
		}
	}, [user, isLoading, router]);

	if (isLoading || !user) {
		return <div className="fixed top-0 left-0 w-full h-full flex justify-center items-center">
			<Loader />
		</div>;
	}

	return (
		<>
			<SuccessAlert />
			<ChatInterface initialConversationId={conversationId} />
		</>
	);
}
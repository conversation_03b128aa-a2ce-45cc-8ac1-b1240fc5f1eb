'use server';

import { createAdminClient, createClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import { createClient as createSupabaseServerClient } from '@/lib/supabase/server';
import Stripe from 'stripe';

// Initialize Stripe client (ensure your STRIPE_SECRET_KEY is in environment variables)
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-05-28.basil', // Use your desired API version or match the one in your API route
  typescript: true,
});

export async function updateUserAdminStatus(formData: FormData): Promise<void> {
  const userId = formData.get('userId') as string;
  const newAdminStatus = formData.get('newAdminStatus') === 'true';

  if (!userId) {
    console.error('User ID is missing for admin status update.');
    return;
  }

  const supabaseAdmin = await createAdminClient();

  const { data: { user: targetUser }, error: getUserError } = await supabaseAdmin.auth.admin.getUserById(userId);

  if (getUserError) {
    console.error('Error fetching user for admin status update:', getUserError);
    return;
  }

  if (!targetUser) {
    console.error('User not found for admin status update:', userId);
    return;
  }

  const currentAppMetadata = targetUser.app_metadata || {};
  const newAppMetadata = {
    ...currentAppMetadata,
    is_admin: newAdminStatus,
  };

  const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
    userId,
    { app_metadata: newAppMetadata }
  );

  if (updateError) {
    console.error('Error updating user admin status:', updateError);
  }

  revalidatePath('/dashboard');
}

export async function updateUserPassword(formData: FormData): Promise<{ success: boolean; message: string }> {
  const newPassword = formData.get('newPassword') as string;
  const confirmPassword = formData.get('confirmPassword') as string;

  if (!newPassword || !confirmPassword) {
    return { success: false, message: 'Both password fields are required.' };
  }

  if (newPassword !== confirmPassword) {
    return { success: false, message: 'Passwords do not match.' };
  }

  if (newPassword.length < 6) { // Example: Enforce a minimum password length
    return { success: false, message: 'Password must be at least 6 characters long.' };
  }

  const supabase = await createClient(); 

  const { error } = await supabase.auth.updateUser({ password: newPassword });

  if (error) {
    console.error('Error updating user password:', error);
    return { success: false, message: `Failed to update password: ${error.message}` };
  }

  return { success: true, message: 'Password updated successfully.' };
}

export async function deleteCurrentUserAccount(): Promise<{ success: boolean; message: string; error?: unknown }> {
  const supabase = await createClient();
  const { data: { user }, error: getUserError } = await supabase.auth.getUser();

  if (getUserError || !user) {
    console.error('Error fetching user for deletion:', getUserError);
    return { success: false, message: 'Could not identify user to delete.', error: getUserError };
  }

  const supabaseAdmin = await createAdminClient();
  const { error: deleteError } = await supabaseAdmin.auth.admin.deleteUser(user.id);

  if (deleteError) {
    console.error('Error deleting user account:', deleteError);
    return { success: false, message: `Failed to delete account: ${deleteError.message}`, error: deleteError };
  }
  
  revalidatePath('/dashboard');
  return { success: true, message: 'Account deleted successfully.' };
}

export async function cancelSubscriptionAction(formData: FormData): Promise<void> {
  const subscriptionId = formData.get('subscriptionId') as string;

  if (!subscriptionId) {
    console.error('Subscription ID is missing for cancellation.');
    // Consider how to notify the user. Revalidating will refresh, but an explicit message might be better.
    revalidatePath('/dashboard');
    return;
  }

  try {
    // 1. Authenticate the user making the request
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('User not authenticated for subscription cancellation.');
      // Notify user or handle error appropriately
      revalidatePath('/dashboard'); // Revalidate to potentially show a login prompt or error state
      return;
    }

    // Optional: Verify ownership if your 'subscriptions' table has user_id
    // This is a good security practice, uncomment and adjust if needed.
    // const { data: supabaseSub, error: subFetchError } = await supabase
    //   .from('subscriptions')
    //   .select('user_id') // Only select what's needed for verification
    //   .eq('stripe_subscription_id', subscriptionId)
    //   .eq('user_id', user.id)
    //   .maybeSingle(); // Use maybeSingle to handle case where sub might not exist

    // if (subFetchError) {
    //   console.error('Error fetching subscription for ownership verification:', subFetchError);
    //   // Decide if this is a critical error that should halt the process
    //   revalidatePath('/dashboard');
    //   return;
    // }
    // if (!supabaseSub) {
    //   console.error('Subscription not found or user does not own this subscription.');
    //   revalidatePath('/dashboard');
    //   return;
    // }

    // 2. Cancel the subscription in Stripe (set to cancel at period end)
    const canceledSubscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });

    console.log(`Subscription ${subscriptionId} scheduled for cancellation at period end by action for user ${user.id}.`);

    // 3. Optimistically update the local database
    if (canceledSubscription.cancel_at_period_end) {
      const { error: updateDbError } = await supabase
        .from('subscriptions')
        .update({ cancel_at_period_end: true })
        .eq('stripe_subscription_id', subscriptionId)
        .eq('user_id', user.id); // Ensure we only update for the logged-in user

      if (updateDbError) {
        console.error(`Error updating subscription ${subscriptionId} in local DB after Stripe cancellation:`, JSON.stringify(updateDbError));
        // Even if this DB update fails, the Stripe call was successful.
        // The webhook should eventually correct this.
        // Consider logging this for monitoring.
      } else {
        console.log(`Subscription ${subscriptionId} in local DB optimistically updated with cancel_at_period_end=true.`);
      }
    }
    // Stripe webhook 'customer.subscription.updated' will handle further DB updates,
    // like changing status to 'canceled' when the period actually ends.

  } catch (error: unknown) {
    console.error('Error in cancelSubscriptionAction:', error instanceof Error ? error.message : 'Unknown error');
    // Differentiate Stripe errors if needed
    if (error && typeof error === 'object' && 'type' in error && typeof error.type === 'string' && error.type.startsWith('Stripe')) {
      console.error('Stripe error details:', error);
    }
    // No direct return of error message to client in Promise<void> Server Action.
    // Client will see updated state after revalidation or rely on logs/monitoring.
  } finally {
    // Always revalidate the path to ensure the UI reflects any changes or errors.
    revalidatePath('/dashboard');
  }
} 
'use client';

import { useState, useEffect, useRef } from 'react';
import AlertMessage from '../../components/ui/AlertMessage';

// Interface for product data displayed and managed by the UI
interface ProductUIData {
  id: string; // Stripe Product ID (from stripe.products.id)
  name: string; // from stripe.products.name or attrs.name
  description?: string | null; // from stripe.products.description or attrs.description
  active: boolean; // from stripe.products.active
  default_price_id?: string | null; // from stripe.products.default_price
  is_visible_on_subscribe_page: boolean; // Parsed from attrs.metadata.is_visible
  exclusivity_group: string | null;      // Parsed from attrs.metadata.group
  display_order: number;                 // Parsed from attrs.metadata.display_order
  one_time_purchase: boolean;            // Parsed from attrs.metadata.one_time_purchase
  tier: number | null;                   // Parsed from attrs.metadata.tier
  created: string; // from stripe.products.created
  updated: string; // from stripe.products.updated
  // raw_attrs might be useful if we need to pass the whole attrs back for updates
  // to preserve other metadata, but the API will handle merging.
}

// Type for the data being edited in the form
interface EditableProductData {
  is_visible_on_subscribe_page: boolean;
  exclusivity_group: string; // UI input uses empty string for null
  display_order: number;
  one_time_purchase: boolean;
  tier: number | null; // Allow null for unsetting tier
}

export default function AdminStripeProductsTablePage() {
  const [products, setProducts] = useState<ProductUIData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [alertInfo, setAlertInfo] = useState<{ message: string | null; type: 'success' | 'error' } | null>(null);

  const successFadeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const successRemoveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    return () => {
      if (successFadeTimeoutRef.current) clearTimeout(successFadeTimeoutRef.current);
      if (successRemoveTimeoutRef.current) clearTimeout(successRemoveTimeoutRef.current);
    };
  }, []);

  // const [isSyncing, setIsSyncing] = useState(false); // Sync button removed for direct stripe.products usage

  const [editingProductId, setEditingProductId] = useState<string | null>(null);
  const [editableProductData, setEditableProductData] = useState<Partial<EditableProductData>>({});

  const fetchProducts = async () => {
    setIsLoading(true);
    setAlertInfo(null);
    if (successFadeTimeoutRef.current) clearTimeout(successFadeTimeoutRef.current);
    if (successRemoveTimeoutRef.current) clearTimeout(successRemoveTimeoutRef.current);
    try {
      const response = await fetch('/api/admin/stripe-products');
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})) ;
        throw new Error(errorData.message || 'Failed to fetch Stripe products.');
      }
      const data: ProductUIData[] = await response.json();
      setProducts(data);
    } catch (err: unknown) {
      setAlertInfo({ message: err instanceof Error ? err.message : 'An unknown error occurred', type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    fetchProducts();
  }, []);

  const handleEdit = (product: ProductUIData) => {
    setEditingProductId(product.id);
    setEditableProductData({
      is_visible_on_subscribe_page: product.is_visible_on_subscribe_page,
      exclusivity_group: product.exclusivity_group || '', // Use empty string for input if null
      display_order: product.display_order,
      one_time_purchase: product.one_time_purchase,
      tier: product.tier, // Will be null if product.tier is null
    });
    setAlertInfo(null);
  };

  const handleCancelEdit = () => {
    setEditingProductId(null);
    setEditableProductData({});
    setAlertInfo(null);
  };

  const handleInputChange = (field: keyof EditableProductData, value: string | number | boolean | null) => {
    setEditableProductData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSaveEdit = async (productId: string) => {
    if (!editableProductData || 
        editableProductData.is_visible_on_subscribe_page === undefined ||
        editableProductData.exclusivity_group === undefined ||
        editableProductData.display_order === undefined ||
        editableProductData.one_time_purchase === undefined ||
        editableProductData.tier === undefined
    ) {
        setAlertInfo({ message: 'Editable data is incomplete. Please fill all fields.', type: 'error' });
        if (successFadeTimeoutRef.current) clearTimeout(successFadeTimeoutRef.current);
        if (successRemoveTimeoutRef.current) clearTimeout(successRemoveTimeoutRef.current);
        return;
    }

    // Ensure all fields are present for the PUT request body
    const dataToSave: EditableProductData = {
        is_visible_on_subscribe_page: editableProductData.is_visible_on_subscribe_page,
        exclusivity_group: editableProductData.exclusivity_group, // API will handle "null" string if needed
        display_order: Number(editableProductData.display_order),
        one_time_purchase: editableProductData.one_time_purchase,
        tier: editableProductData.tier, // Can be number or null
    };

    try {
        const response = await fetch(`/api/admin/stripe-products`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                productId: productId,
                updates: dataToSave 
            }),
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || 'Failed to update product metadata.');
        }

        // Optimistically update local state or re-fetch
        setProducts(prevProducts => 
            prevProducts.map(p => 
                p.id === productId ? { 
                    ...p, 
                    is_visible_on_subscribe_page: dataToSave.is_visible_on_subscribe_page,
                    exclusivity_group: dataToSave.exclusivity_group === '' ? null : dataToSave.exclusivity_group,
                    display_order: dataToSave.display_order,
                    one_time_purchase: dataToSave.one_time_purchase,
                    tier: dataToSave.tier // tier can now be null
                } : p
            )
        );
        handleCancelEdit(); // Exit editing mode
        
        if (successFadeTimeoutRef.current) clearTimeout(successFadeTimeoutRef.current);
        if (successRemoveTimeoutRef.current) clearTimeout(successRemoveTimeoutRef.current);

        setAlertInfo({ message: 'Product metadata updated successfully!', type: 'success' });

    } catch (err: unknown) {
        setAlertInfo({ message: err instanceof Error ? err.message : 'An unknown error occurred', type: 'error' });
        if (successFadeTimeoutRef.current) clearTimeout(successFadeTimeoutRef.current);
        if (successRemoveTimeoutRef.current) clearTimeout(successRemoveTimeoutRef.current);
    }
  };

  return (
    <div>
      <h2 className="text-2xl  mb-4">Product Management</h2>

      {alertInfo && alertInfo.message && (
        <AlertMessage message={alertInfo.message} type={alertInfo.type} duration={2500} fadeDuration={500} />
      )}

      {isLoading ? (
        <p>Loading products from Stripe...</p>
      ) : products.length === 0 ? (
        <p>No products found in stripe.products. Ensure the foreign table is populated.</p>
      ) : (
        <div className="max-w-full overflow-scroll">
          <table className="table-auto w-full max-w-full">
            <thead>
              <tr>
                <th className="p-4">Name</th>
                <th className="p-4">ID</th>
                <th className="p-4">Active</th>
                <th className="p-4">Visible</th>
                <th className="p-4">Group</th>
                <th className="p-4">Order</th>
                <th className="p-4">One time</th>
                <th className="p-4">Tier</th>
                <th className="p-4">Actions</th>
              </tr>
            </thead>
            <tbody className="table-zebra">
              {products.map((product) => (
                <tr key={product.id} className={editingProductId === product.id ? 'bg-yellow-100' : ''}>
                  <td className="p-4">{product.name}</td>
                  <td className="p-4">{product.id}</td>
                  <td className="p-4">{product.active ? 'Yes' : 'No'}</td>
                  <td className="p-4">
                    {editingProductId === product.id ? (
                      <input 
                        type="checkbox" 
                        checked={editableProductData?.is_visible_on_subscribe_page || false}
                        onChange={(e) => handleInputChange('is_visible_on_subscribe_page', e.target.checked)}
                        className="checkbox checkbox-primary"
                      />
                    ) : (
                      product.is_visible_on_subscribe_page ? 'Yes' : 'No'
                    )}
                  </td>
                  <td className="p-4">
                    {editingProductId === product.id ? (
                      <input 
                        type="text" 
                        value={editableProductData?.exclusivity_group || ''}
                        onChange={(e) => handleInputChange('exclusivity_group', e.target.value)}
                        className="input input-bordered w-full"
                        placeholder="e.g. tier, addon"
                      />
                    ) : (
                      product.exclusivity_group || '-'
                    )}
                  </td>
                  <td className="p-4">
                    {editingProductId === product.id ? (
                      <input 
                        type="number"
                        value={editableProductData?.display_order ?? 0}
                        onChange={(e) => handleInputChange('display_order', parseInt(e.target.value, 10) || 0)}
                        className="input input-bordered w-20"
                      />
                    ) : (
                      product.display_order
                    )}
                  </td>
                  <td className="p-4">
                    {editingProductId === product.id ? (
                      <input 
                        type="checkbox" 
                        checked={editableProductData?.one_time_purchase || false}
                        onChange={(e) => handleInputChange('one_time_purchase', e.target.checked)}
                        className="checkbox checkbox-primary"
                      />
                    ) : (
                      product.one_time_purchase ? 'Yes' : 'No'
                    )}
                  </td>
                  <td className="p-4">
                    {editingProductId === product.id ? (
                      <input 
                        type="number"
                        value={editableProductData?.tier === null ? '' : String(editableProductData?.tier ?? '')}
                        onChange={(e) => {
                          const val = e.target.value;
                          if (val === '') {
                            handleInputChange('tier', null);
                          } else {
                            const num = parseInt(val, 10);
                            handleInputChange('tier', isNaN(num) ? null : num);
                          }
                        }}
                        className="input input-bordered w-20"
                        placeholder="Tier (empty to unset)"
                      />
                    ) : (
                      product.tier === null ? '-' : product.tier
                    )}
                  </td>
                  <td className="p-4">
                    {editingProductId === product.id ? (
                      <div className="flex flex-col space-y-2">
                        <button 
                          onClick={() => handleSaveEdit(product.id)} 
                          className="btn btn-xs w-16"
                        >
                          Save
                        </button>
                        <button 
                          onClick={handleCancelEdit} 
                          className="btn btn-xs w-16"
                        >
                          Cancel
                        </button>
                      </div>
                    ) : (
                      <button 
                        onClick={() => handleEdit(product)} 
                        className="btn btn-xs w-16"
                        disabled={editingProductId !== null && editingProductId !== product.id}
                      >
                        Edit
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}

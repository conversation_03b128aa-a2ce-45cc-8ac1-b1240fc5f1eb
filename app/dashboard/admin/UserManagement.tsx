'use client';

import React from 'react';
import { useState, useEffect, useRef } from 'react';
import AlertMessage from '../../components/ui/AlertMessage';

// --- Data Structures from/for API (matching /api/admin/stripe-users/route.ts) ---
interface OneTimePurchaseDisplayDetail {
  productId: string;
  productName?: string | null;
}

interface UserManagementData {
  userId: string;
  userEmail: string | undefined;
  userCreatedAt: string; // ISO String
  isAdmin: boolean;
  stripeCustomerId?: string | null;
  activeSubscription?: {
    subscriptionId: string;
    status: string;
    productName?: string | null;
    productId?: string | null;
    billingInterval?: string | null;
    currentPeriodEnd?: string | null; // ISO String
    cancelAtPeriodEnd?: boolean | null;
  } | null;
  oneTimePurchases: OneTimePurchaseDisplayDetail[];
}

// --- Formatted Data Structures for Frontend Display ---
interface FormattedSubscriptionDetail {
  subscriptionId: string;
  status: string;
  productName?: string | null;
  productId?: string | null;
  billingInterval?: string | null;
  currentPeriodEndFmt?: string | null;
  cancelAtPeriodEnd?: boolean | null;
}

interface FormattedOneTimePurchaseDetail {
  productId: string;
  productName?: string | null;
  // Removed amount, currency, createdFmt as they are not in the new API response for OTPs
}

interface FormattedUserData {
  userId: string;
  userEmail: string | undefined;
  userCreatedAtFmt: string;
  isAdmin: boolean;
  stripeCustomerId?: string | null;
  activeSubscription?: FormattedSubscriptionDetail | null;
  oneTimePurchases: FormattedOneTimePurchaseDetail[];
}

export default function UserManagementPage() {
  const [usersData, setUsersData] = useState<FormattedUserData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [alertInfo, setAlertInfo] = useState<{ message: string | null; type: 'success' | 'error' } | null>(null);
  const [updatingAdminStatus, setUpdatingAdminStatus] = useState<Record<string, boolean>>({});
  const [syncingProfileId, setSyncingProfileId] = useState<string | null>(null);

  const alertTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    return () => {
      if (alertTimeoutRef.current) clearTimeout(alertTimeoutRef.current);
    };
  }, []);

  const fetchUsersSubscriptions = async () => {
    setIsLoading(true);
    setAlertInfo(null);
    if (alertTimeoutRef.current) clearTimeout(alertTimeoutRef.current);

    try {
      const response = await fetch('/api/admin/stripe-users');
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to fetch user and subscription data.');
      }
      const rawData: UserManagementData[] = await response.json();

      const formattedData: FormattedUserData[] = rawData.map(user => {
        let formattedSub: FormattedSubscriptionDetail | null = null;
        if (user.activeSubscription) {
          formattedSub = {
            ...user.activeSubscription,
            currentPeriodEndFmt: user.activeSubscription.currentPeriodEnd
              ? new Date(user.activeSubscription.currentPeriodEnd).toLocaleDateString()
              : 'N/A',
          };
        }

        const formattedOtps: FormattedOneTimePurchaseDetail[] = user.oneTimePurchases.map(otp => ({
          productId: otp.productId,
          productName: otp.productName,
        }));

        return {
          userId: user.userId,
          userEmail: user.userEmail,
          userCreatedAtFmt: user.userCreatedAt ? new Date(user.userCreatedAt).toLocaleDateString() : 'N/A',
          isAdmin: user.isAdmin,
          stripeCustomerId: user.stripeCustomerId,
          activeSubscription: formattedSub,
          oneTimePurchases: formattedOtps,
        };
      });
      setUsersData(formattedData);

    } catch (err: unknown) {
      setAlertInfo({ message: err instanceof Error ? err.message : 'An unknown error occurred', type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUsersSubscriptions();
  }, []);

  const handleSetAdminStatus = async (userId: string, newIsAdminStatus: boolean) => {
    setUpdatingAdminStatus(prev => ({ ...prev, [userId]: true }));
    setAlertInfo(null);
    if (alertTimeoutRef.current) clearTimeout(alertTimeoutRef.current);
    try {
      const response = await fetch('/api/admin/stripe-users', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, isAdmin: newIsAdminStatus }),
      });
      const responseData = await response.json();
      if (!response.ok) throw new Error(responseData.message || responseData.error || 'Failed to update admin status.');
      setUsersData(prevUsers => prevUsers.map(user => user.userId === userId ? { ...user, isAdmin: newIsAdminStatus } : user));
      setAlertInfo({ message: 'Admin status updated!', type: 'success' });
    } catch (err: unknown) {
      setAlertInfo({ message: err instanceof Error ? err.message : 'An unknown error occurred', type: 'error' });
    } finally {
      setUpdatingAdminStatus(prev => ({ ...prev, [userId]: false }));
    }
  };

  const handleSyncProfile = async (userId: string) => {
    setSyncingProfileId(userId);
    setAlertInfo(null);
    if (alertTimeoutRef.current) clearTimeout(alertTimeoutRef.current);

    try {
      const response = await fetch('/api/admin/sync-user-profile', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId }),
      });
      const responseData = await response.json();
      if (!response.ok) {
        throw new Error(responseData.message || responseData.error || 'Failed to sync user profile.');
      }
      setAlertInfo({ message: responseData.message || 'Profile synced successfully!', type: 'success' });
      
      // Update only the synced user in the local state
      const syncedProfile = responseData.profile; // This is the raw profile data
      if (syncedProfile) {
        setUsersData(prevUsers => 
          prevUsers.map(user => {
            if (user.userId === userId) {
              // Create a new FormattedSubscriptionDetail if subscription data exists
              let updatedFormattedSub: FormattedSubscriptionDetail | null = null;
              if (syncedProfile.stripe_subscription_id && syncedProfile.subscription_status) {
                updatedFormattedSub = {
                  subscriptionId: syncedProfile.stripe_subscription_id,
                  status: syncedProfile.subscription_status,
                  productId: syncedProfile.subscribed_product_id || null,
                  // ProductName will be missing here, will update on next full fetch
                  productName: user.activeSubscription?.productName || 'Processing...', // Preserve old or show placeholder 
                  billingInterval: syncedProfile.subscription_interval || null,
                  currentPeriodEndFmt: syncedProfile.subscription_current_period_end
                    ? new Date(syncedProfile.subscription_current_period_end).toLocaleDateString()
                    : 'N/A',
                  // cancelAtPeriodEnd is not directly in profiles table, might be null/stale until full refresh
                  cancelAtPeriodEnd: user.activeSubscription?.cancelAtPeriodEnd === undefined ? null : user.activeSubscription.cancelAtPeriodEnd,
                };
              }

              // Create new FormattedOneTimePurchaseDetail array
              const updatedOtps: FormattedOneTimePurchaseDetail[] = (syncedProfile.purchases || []).map((pid: string) => {
                // Try to find existing OTP to preserve product name temporarily
                const existingOtp = user.oneTimePurchases.find(otp => otp.productId === pid);
                return {
                  productId: pid,
                  productName: existingOtp?.productName || 'Processing...', // Preserve old or show placeholder
                };
              });

              return {
                ...user, // Keep existing isAdmin, userCreatedAtFmt, userEmail
                stripeCustomerId: syncedProfile.stripe_customer_id || null,
                activeSubscription: updatedFormattedSub,
                oneTimePurchases: updatedOtps,
              };
            }
            return user;
          })
        );
      } else {
        // If for some reason profile is not in response, fall back to full refresh
        fetchUsersSubscriptions(); 
      }

    } catch (err: unknown) {
      setAlertInfo({ message: err instanceof Error ? err.message : 'An unknown error occurred', type: 'error' });
    } finally {
      setSyncingProfileId(null);
    }
  };

  return (
    <div>
      <h2 className="text-2xl  mb-4">User Management</h2>
      {alertInfo && <AlertMessage message={alertInfo.message} type={alertInfo.type} duration={3000} />}
      {isLoading ? <p>Loading data...</p> : usersData.length === 0 ? <p>No users found.</p> : (
        <div className="w-full"> {/* Container for user blocks */}
          {usersData.map((user) => (
            <div key={user.userId + '-block'} className="border-b border-gray-300 bg-base-200 mt-4 p-4">
              {/* User Info Table */}
              <div>
                <table className="table rounded-none w-full ">
                  <tbody>
                    <tr className="hover:bg-base-300">
                      <td className="align-top  w-[33%]">{user.userEmail || 'N/A'}</td>
                      <td className="p-2 align-top text-right">
                        registered {user.userCreatedAtFmt}</td>
                        <td className="p-2 text-right align-top w-[100px]"><span className="-mt-1 ml-2 text-xs rounded-full badge bg-primary-600 text-white p-0 pl-2 text-gray-500 uppercase  text-[10px] space-x-0">Admin<input type="checkbox" className="-ml-1 toggle toggle-sm toggle-primary h-full outline-none" checked={user.isAdmin}
                               onChange={(e) => handleSetAdminStatus(user.userId, e.target.checked)} 
                               disabled={updatingAdminStatus[user.userId]} /></span></td>
                      <td className="p-2 text-right align-top w-auto">
                        <button 
                          className={`btn btn-xs btn-outline ml-2 ${syncingProfileId === user.userId ? 'loading' : ''}`}
                          onClick={() => handleSyncProfile(user.userId)}
                          disabled={isLoading || syncingProfileId === user.userId || !!updatingAdminStatus[user.userId]}
                        >
                          {syncingProfileId === user.userId ? 'Syncing...' : 'Sync Profile'}
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              {/* Transactions Table */}
              {(user.activeSubscription || user.oneTimePurchases.length > 0) ? (
                <div>
                  <div className="overflow-x-auto">
                    <table className="table table-compact w-full table-fixed ">
                      <thead className="text-[10px] uppercase">
                        <tr>
                          <th className="p-2 text-left">Type</th>
                          <th className="p-2 text-left">Product (ID)</th>
                          <th className="p-2 text-left">Status</th>
                          <th className="p-2 text-left">Interval</th>
                          {/* <th className="p-2 text-left">Start/Date</th> OTPs don't have start date from profile */}
                          <th className="p-2 text-left">End/Purchase Date</th>
                          <th className="p-2 text-left">Cancel at Period End?</th> 
                          {/* <th className="p-2 text-left">Details</th> OTPs have less details now */}
                        </tr>
                      </thead>
                      <tbody>
                        {user.activeSubscription && (
                          <tr key={user.activeSubscription.subscriptionId} className="hover:bg-base-300">
                            <td className="p-2 align-top"><span className="badge badge-sm badge-info rounded-full">Subscription</span></td>
                            <td className="p-2 align-top">
                              <span className="badge badge-xs badge-outline uppercase p-2 whitespace-nowrap">{user.activeSubscription.productName || 'N/A'}</span>
                              {user.activeSubscription.productId && <span className="text-xs text-gray-400 ml-1">({user.activeSubscription.productId})</span>}
                            </td>
                            <td className="p-2 align-top"><span className="badge badge-xs badge-outline uppercase p-2 whitespace-nowrap">{user.activeSubscription.status || 'N/A'}</span></td>
                            <td className="p-2 align-top"><span className="badge badge-xs badge-outline uppercase p-2 whitespace-nowrap">{user.activeSubscription.billingInterval || 'N/A'}</span></td>
                            <td className="p-2 align-top">{user.activeSubscription.currentPeriodEndFmt}</td>
                            <td className="p-2 align-top whitespace-nowrap">
                              {user.activeSubscription.cancelAtPeriodEnd === null ? 'N/A' : user.activeSubscription.cancelAtPeriodEnd ? <span className="text-warning">Yes</span> : 'No'}
                            </td>
                          </tr>
                        )}
                        {user.oneTimePurchases.map(otp => (
                          <tr key={otp.productId} className="hover:bg-base-300">
                            <td className="p-2 align-top"><span className="badge badge-sm badge-success rounded-full">One-Time</span></td>
                            <td className="p-2 align-top">
                              <span className="badge badge-xs badge-outline uppercase p-2 whitespace-nowrap mr-2">{otp.productName || 'N/A'}</span>
                              <span className="text-xs text-gray-400 ml-1">({otp.productId})</span>
                            </td>
                            <td className="p-2 align-top"><span className="badge badge-xs badge-outline uppercase p-2 whitespace-nowrap">Completed</span></td>
                            <td className="p-2 align-top uppercase">N/A</td>
                            <td className="p-2 align-top">N/A</td>
                            <td className="p-2 align-top whitespace-nowrap">N/A</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <p className="p-4  text-gray-500">No active subscriptions or one-time purchases found for this user based on their profile.</p>
              )}
              
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 
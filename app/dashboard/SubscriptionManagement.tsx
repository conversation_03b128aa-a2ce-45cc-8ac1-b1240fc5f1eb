'use client';

import React from 'react';
import ProductList from '../components/products/ProductList';
import { Product } from '@/app/utils/pricingUtils';
import SuccessDialog from '../components/ui/SuccessDialog';
import { useSuccessDialog } from '../hooks/useSuccessDialog';
import { Title, Flex } from '@mantine/core';
import classes from '@/app/theme/DashboardModal.module.css';

interface Subscription {
  stripe_subscription_id: string;
  status: string;
  stripe_product_id: string | null;
  product_name?: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  user_id?: string;
}

interface OneTimePurchase {
  stripe_product_id: string;
  product_name?: string;
}

interface SubscriptionManagementProps {
  subscriptions: Subscription[] | null;
  products: Product[];
  purchases?: OneTimePurchase[] | null;
}

export default function SubscriptionManagement({ subscriptions, products, purchases }: SubscriptionManagementProps) {
  const { 
    isSuccessDialogOpen, 
    successSessionId, 
    closeSuccessDialog, 
    goToHomepage, 
    goToDashboard 
  } = useSuccessDialog();

  return (
    <Flex direction="column">
      <Title order={2} mb="md" className={classes.tabTitle}>Subscription Management</Title>
      <Title order={2} mb="md" className={classes.title}>Current Subscription</Title>

      <Flex direction="column" mb="xl" className="mt-0 sm:-mt-10">
        <ProductList 
          listType="subscriptions"
          displayMode="upgrade"
          showCancelButton={true}
          subscriptions={subscriptions}
          products={products}
          gridColumns={3}
        />
      </Flex>

      <Flex direction="column" mt="xl">
        <Title order={2} mb="md" className={classes.title}>Add-ons</Title>
        <ProductList 
          listType="addons"
          displayMode="current"
          purchases={purchases}
          products={products}
          gridColumns={3}
        />
      </Flex>



      {/* Success Dialog */}
      <SuccessDialog
        opened={isSuccessDialogOpen}
        onClose={closeSuccessDialog}
        sessionId={successSessionId}
        onGoToHomepage={goToHomepage}
        onGoToDashboard={goToDashboard}
      />
    </Flex>
  );
} 

'use client';

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { Button, Alert, Title, Text, Modal, Group, Stack } from '@mantine/core';
import { IconTrash, IconX } from '@tabler/icons-react';
import { deleteCurrentUserAccount } from './actions';
import { createClient } from '@/lib/supabase/client';

export default function DeleteAccountButton() {
  const router = useRouter();
  const supabase = createClient();
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null);
  const [isPending, startTransition] = useTransition();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const confirmDelete = () => {
    setIsModalOpen(false);
    setMessage(null);

    startTransition(async () => {
      const result = await deleteCurrentUserAccount();
      if (result.success) {
        setMessage({ text: result.message, type: 'success' });
        await supabase.auth.signOut();
        router.push('/?message=Account deleted successfully&status=success');
        router.refresh();
      } else {
        setMessage({ text: result.message || 'An unexpected error occurred.', type: 'error' });
      }
    });
  };

  const openConfirmModal = () => {
    setMessage(null);
    setIsModalOpen(true);
  };

  const cancelDelete = () => {
    setIsModalOpen(false);
  };

  return (
    <div>
      <Title order={2} mb="md">Delete Account</Title>
      <Text c="dimmed" mb="md">
        Permanently delete your account and all associated data. This action is irreversible.
      </Text>
      
      <Stack gap="md">
        <Button
          onClick={openConfirmModal}
          loading={isPending}
          color="red"
          fullWidth
          leftSection={<IconTrash size="1rem" />}
        >
          Delete My Account
        </Button>

        {message && (
          <Alert
            icon={<IconX size="1rem" />}
            color={message.type === 'success' ? 'green' : 'red'}
            variant="light"
          >
            {message.text}
          </Alert>
        )}
      </Stack>

      <Modal 
        opened={isModalOpen}
        onClose={cancelDelete}
        title="Confirm Account Deletion"
        centered
      >
        <Stack gap="md">
          <Text>
            Are you sure you want to delete your account? This action cannot be undone. 
            All your data associated with this account will be permanently removed.
          </Text>
          
          <Group justify="flex-end" gap="sm">
            <Button 
              onClick={cancelDelete} 
              variant="subtle"
            >
              Cancel
            </Button>
            <Button 
              onClick={confirmDelete} 
              color="red"
              loading={isPending}
            >
              Confirm Delete
            </Button>
          </Group>
        </Stack>
      </Modal>
    </div>
  );
} 

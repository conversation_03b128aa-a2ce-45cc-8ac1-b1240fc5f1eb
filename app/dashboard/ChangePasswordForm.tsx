'use client';

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { PasswordInput, Button, Alert, Title, Text, Modal, Group, Stack, Divider } from '@mantine/core';
import { IconCheck, IconX, IconTrash } from '@tabler/icons-react';
import { updateUserPassword, deleteCurrentUserAccount } from './actions';
import { createClient } from '@/lib/supabase/client';
import classes from '@/app/theme/DashboardModal.module.css';

export default function AccountSettings() {
  const router = useRouter();
  const supabase = createClient();
  
  // Password change state
  const [passwordMessage, setPasswordMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null);
  const [isPasswordPending, startPasswordTransition] = useTransition();
  
  // Delete account state
  const [deleteMessage, setDeleteMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null);
  const [isDeletePending, startDeleteTransition] = useTransition();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deleteSuccess, setDeleteSuccess] = useState(false);

  const handlePasswordSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setPasswordMessage(null);
    const formData = new FormData(event.currentTarget);

    startPasswordTransition(async () => {
      const result = await updateUserPassword(formData);
      if (result.success) {
        setPasswordMessage({ text: result.message, type: 'success' });
        (event.target as HTMLFormElement).reset(); // Reset form on success
      } else {
        setPasswordMessage({ text: result.message, type: 'error' });
      }
    });
  };

  const confirmDelete = () => {
    setDeleteMessage(null);

    startDeleteTransition(async () => {
      const result = await deleteCurrentUserAccount();
      if (result.success) {
        setDeleteSuccess(true);
        setDeleteMessage({ text: result.message, type: 'success' });
        
        // Auto-close modal and redirect after 3 seconds
        setTimeout(async () => {
          await supabase.auth.signOut();
          router.push('/?message=Account deleted successfully&status=success');
          router.refresh();
        }, 3000);
      } else {
        setDeleteMessage({ text: result.message || 'An unexpected error occurred.', type: 'error' });
      }
    });
  };

  const openConfirmModal = () => {
    setDeleteMessage(null);
    setDeleteSuccess(false);
    setIsModalOpen(true);
  };

  const cancelDelete = () => {
    setIsModalOpen(false);
    setDeleteSuccess(false);
    setDeleteMessage(null);
  };

  const DeleteModalComponent = () => {
    if (!isModalOpen) return null;

    const title = deleteSuccess ? "Account Deleted" : "Confirm Account Deletion";

    return (
      <Modal 
        opened={isModalOpen}
        onClose={cancelDelete}
        centered
        withCloseButton={false}
      >
        <Modal.Body>
          <Modal.Title>{title}</Modal.Title>
          {deleteSuccess ? (
            <>
              <Text mt="md">
                Your account has been successfully deleted. You will be redirected to the home page in a few seconds.
              </Text>
              {deleteMessage && (
                <Alert
                  icon={<IconCheck size="1rem" />}
                  color="green"
                  variant="light"
                  mt="md"
                >
                  {deleteMessage.text}
                </Alert>
              )}
              <Group justify="center" mt="lg">
                <Button
                  onClick={cancelDelete}
                  variant="outline"
                  disabled={isDeletePending}
                >
                  Close
                </Button>
              </Group>
            </>
          ) : (
            <>
              <Text mt="md">
                Are you sure you want to delete your account? This action cannot be undone. 
                All your data associated with this account will be permanently removed.
              </Text>
              
              {deleteMessage && (
                <Alert
                  icon={<IconX size="1rem" />}
                  color="red"
                  variant="light"
                  mt="md"
                >
                  {deleteMessage.text}
                </Alert>
              )}
              
              <Group justify="space-between" gap="sm" mt="lg">
                <Button 
                  onClick={cancelDelete} 
                  variant="outline"
                  disabled={isDeletePending}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={confirmDelete} 
                  color="red"
                  loading={isDeletePending}
                  leftSection={<IconTrash size="1rem" />}
                >
                  {isDeletePending ? 'Deleting...' : 'Delete Account'}
                </Button>
              </Group>
            </>
          )}
        </Modal.Body>
      </Modal>
    );
  };

  return (
    <div>
      <Title order={2} mb="md" className={classes.tabTitle}>Account Settings</Title>
      
      {/* Change Password Section */}
      <Title order={3} mb="md" className={classes.title}>Change Password</Title>
      <form onSubmit={handlePasswordSubmit}>
        <Stack gap="md">
          <PasswordInput
            name="newPassword"
            placeholder="Enter new password"
            aria-label="New Password"
            required
            size="md"
          />
          
          <PasswordInput
            name="confirmPassword"
            placeholder="Confirm new password"
            aria-label="Confirm New Password"
            required
            size="md"
          />
          
          <Button
            type="submit"
            loading={isPasswordPending}
            fullWidth
            size="md"
          >
            Change Password
          </Button>

          {passwordMessage && (
            <Alert
              icon={passwordMessage.type === 'success' ? <IconCheck size="1rem" /> : <IconX size="1rem" />}
              color={passwordMessage.type === 'success' ? 'green' : 'red'}
              variant="light"
            >
              {passwordMessage.text}
            </Alert>
          )}
        </Stack>
      </form>

      <Divider my="xl" />

      {/* Delete Account Section */}
      <Title order={3} mb="md" className={classes.title}>Delete Account</Title>
      <Text c="dimmed" mb="md">
        Permanently delete your account and all associated data. This action is irreversible.
      </Text>
      
      <Stack gap="md">
        <Button
          onClick={openConfirmModal}
          loading={isDeletePending}
          color="red"
          fullWidth
          leftSection={<IconTrash size="1rem" />}
        >
          Delete My Account
        </Button>
      </Stack>

      <DeleteModalComponent />
    </div>
  );
} 
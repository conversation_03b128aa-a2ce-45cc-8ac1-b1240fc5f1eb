'use client';

import React, { useState, useEffect, Suspense, useRef, useCallback } from 'react';
import { FloatingIndicator, Tabs, Container, Loader } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import SubscriptionManagement from './SubscriptionManagement';
import ChangePasswordForm from './ChangePasswordForm';
import ProductManagement from './admin/ProductManagement';
import UserManagement from './admin/UserManagement';
import { Product } from '@/app/utils/pricingUtils';
import classes from '@/app/theme/Tabs.module.css';

// Prop types are now more complex due to nested subscriptions
// It might be better to define these in a shared types file

// Define the structure for a single subscription (used in both props)
interface Subscription {
  stripe_subscription_id: string;
  status: string;
  stripe_product_id: string | null;
  product_name?: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  user_id?: string; // user_id might not always be present depending on context
}

// Structure for subscriptions WITHIN the User object for the table
// UserSubscription interface was here and is now removed.

// User structure for the table
// User interface was here and is now removed.

// One-time purchase structure
interface OneTimePurchase {
  stripe_product_id: string;
  product_name?: string;
}

interface DashboardTabsProps {
  isAdmin: boolean; // Changed from currentUserIsAdmin
  subscriptions: Subscription[] | null; // NEW: Array of subscriptions for the logged-in user
  products: Product[]; // NEW: Array of relevant products
  purchases?: OneTimePurchase[] | null; // Add one-time purchases
}

// Add "Product Management" to TabName
type TabName = "User Management" | "Subscription" | "Product Management" | "Account Settings";

// Helper to get available tabs based on admin status
const getAvailableTabs = (isAdmin: boolean): TabName[] => {
  // Define all possible tabs
  const commonTabs: TabName[] = ["Subscription", "Account Settings"];
  const adminTabs: TabName[] = ["User Management", "Product Management"];

  if (isAdmin) {
    // Common tabs first, then admin tabs
    return [...commonTabs, ...adminTabs];
  }
  // Non-admins only see common tabs
  return commonTabs;
};

export default function DashboardTabs({
  isAdmin,
  subscriptions,
  products,
  purchases = null,
}: DashboardTabsProps) {
  const initialAvailableTabs = getAvailableTabs(isAdmin);
  const [activeTab, setActiveTab] = useState<TabName>("Subscription");
  const [tabs, setTabs] = useState<TabName[]>(initialAvailableTabs);
  const [refsReady, setRefsReady] = useState(false);
  const isSmallScreen = useMediaQuery('(max-width: 768px)');

  const rootRef = useRef<HTMLDivElement | null>(null);
  const controlsRefs = useRef<Record<string, HTMLButtonElement | null>>({});
  
  const setControlRef = useCallback((val: string) => (node: HTMLButtonElement | null) => {
    controlsRefs.current[val] = node;
  }, []);

  useEffect(() => {
    setRefsReady(true);
  }, []);

  useEffect(() => {
    const availableTabs = getAvailableTabs(isAdmin);
    setTabs(availableTabs);
    if (!availableTabs.includes(activeTab)) {
      setActiveTab(availableTabs[0]);
    }
  }, [isAdmin, activeTab]);

  return (
    <Container size="xl" className={classes.container}>
      <Tabs 
        value={activeTab} 
        onChange={(value) => setActiveTab(value as TabName || "Subscription")} 
        variant="pills"
        orientation={isSmallScreen ? 'horizontal' : 'vertical'}
        className={classes.tabs}
      >
        <Tabs.List 
          ref={rootRef} 
        >
          {tabs.map((tab) => (
            <Tabs.Tab 
              key={tab}
              value={tab} 
              ref={setControlRef(tab)}
              className={classes.tab}
            >
              {tab}
            </Tabs.Tab>
          ))}

          <FloatingIndicator
            target={refsReady && activeTab ? controlsRefs.current[activeTab] : null}
            parent={rootRef.current}
            className={classes.indicator}
          />
        </Tabs.List>

        {tabs.map((tab) => (
          <Tabs.Panel key={tab} value={tab} className={classes.panel}>
            <Suspense fallback={
              <Loader>
                Loading...
              </Loader>
            }>
              {tab === "Subscription" && (
                <SubscriptionManagement 
                  subscriptions={subscriptions} 
                  products={products} 
                  purchases={purchases} 
                />
              )}
              {tab === "Product Management" && isAdmin && <ProductManagement />}
              {tab === "User Management" && isAdmin && <UserManagement />}
              {tab === "Account Settings" && <ChangePasswordForm />}
            </Suspense>
          </Tabs.Panel>
        ))}
      </Tabs>
    </Container>
  );
} 
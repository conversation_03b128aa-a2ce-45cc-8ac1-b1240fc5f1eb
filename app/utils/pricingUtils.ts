'use client';

// app/utils/pricingUtils.ts

export interface Price {
  id: string;
  unit_amount: number | null; // Price in cents
  currency: string;
  type: 'one_time' | 'recurring';
  recurring?: {
    interval: string | null;
    interval_count: number | null;
  } | null;
  // nickname?: string | null; // Optional: if you use nicknames for prices
}

export interface Product {
  id: string; // Product ID
  name: string;
  description?: string | null;
  images: string[];
  active: boolean;
  marketing_features?: string[];
  metadata: {
    display_order?: string; // Still useful for an initial sort, but not for upgrade logic itself
    product_group?: string;
    [key: string]: string | undefined;
  };
  prices: Price[];
}

export interface ActiveSubscriptionDetails {
  productId: string | null;
  priceId: string | null;
  status: string | null;
  stripeSubscriptionId?: string | null;
  cancelAtPeriodEnd?: boolean | null;
  // Consider adding currentSubscriptionId (the actual Stripe Subscription ID) here if available
  // currentSubscriptionId?: string | null;
}

/**
 * Calculates a normalized monthly price for a given price object.
 * This helps in comparing prices with different billing intervals (day, week, month, year).
 * @param price The Price object
 * @returns Normalized monthly price in cents, or Infinity if not applicable.
 */
export const getNormalizedMonthlyPrice = (price: Price): number => {
  if (price.unit_amount === null) {
    return Infinity; // Only return Infinity for null prices, not 0 prices
  }
  if (price.type === 'one_time') {
    // For one-time purchases, normalization to monthly might not be directly applicable
    // or could be considered as a one-off cost. For comparison with subscriptions,
    // this might be treated as Infinity or a very high number if trying to fit it into a recurring model.
    // Or, if comparing one-time products against each other, their raw unit_amount is fine.
    // For upgrade logic relating to subscriptions, we primarily care about recurring prices.
    return Infinity; 
  }
  if (!price.recurring) {
      return Infinity; // Should not happen for recurring type if data is clean
  }

  const amount = price.unit_amount;
  const interval = price.recurring.interval;
  const intervalCount = price.recurring.interval_count || 1;

  switch (interval) {
    case 'day': return (amount / intervalCount) * 30; // Approximation
    case 'week': return (amount / intervalCount) * (365.25 / 12 / 7); // More precise weekly to monthly
    case 'month': return amount / intervalCount;
    case 'year': return amount / (intervalCount * 12);
    default: return Infinity; // Unknown interval
  }
}; 
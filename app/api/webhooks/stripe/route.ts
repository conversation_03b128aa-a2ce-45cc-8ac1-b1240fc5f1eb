import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { stripe } from '@/lib/stripe/admin';
import { supabaseAdmin } from '@/lib/supabase/admin';

// Set of relevant event types to handle
const relevantEvents = new Set([
  'checkout.session.completed',
  'invoice.payment_succeeded', // Good for ongoing subscription payments
  'invoice.payment_failed',
  'customer.subscription.updated',
  'customer.subscription.deleted',
  'checkout.session.async_payment_succeeded', // Added for one-time payment confirmation
  'customer.subscription.created', // Added to ensure subscription creation is captured directly
]);

interface ProfileUpdateData {
  stripe_customer_id?: string;
  stripe_subscription_id?: string | null; // Allow null for one-time purchases or if subscription is removed
  subscription_status?: string | null;
  subscribed_product_id?: string | null;
  subscription_current_period_end?: string | null;
  subscription_price_id?: string | null;
  subscription_interval?: string | null;
  cancel_at_period_end?: boolean | null;
  purchases?: string[]; // Added for one-time purchases
}

type ExtendedCheckoutSession = Stripe.Checkout.Session & {
  customer_email?: string | null;
  subscription?: string;
  customer: string;
}

type ExtendedInvoice = Stripe.Invoice & {
  subscription: string;
  customer: string;
}

type SubscriptionItem = Stripe.SubscriptionItem & {
  current_period_end: number;
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  console.log(`[WEBHOOK] Stripe event: checkout.session.completed. Session ID: ${session.id}. Metadata:`, session.metadata);

  const userEmail = (session as ExtendedCheckoutSession).customer_email || session.metadata?.userEmail;
  const productIdFromMetadata = session.metadata?.productId; // Renamed to avoid conflict
  const type = session.metadata?.type;
  const existingSubscriptionIdToCancel = session.metadata?.existingSubscriptionIdToCancel;
  const stripeCustomerId = (session as ExtendedCheckoutSession).customer;

  if (typeof stripeCustomerId !== 'string') {
    console.error('[WEBHOOK_ERROR] checkout.session.completed: Customer ID is not a string or is missing.', { sessionId: session.id, customer: stripeCustomerId });
    return;
  }
  if (!userEmail) { // Check userEmail here as it's needed for profile operations
    console.error('[WEBHOOK_ERROR] checkout.session.completed: User email is missing.', { sessionId: session.id });
    return;
  }

  let profileUpdatePayload: Partial<ProfileUpdateData> = {
    stripe_customer_id: stripeCustomerId,
  };

  if (type === 'subscription') {
    if (!productIdFromMetadata) { // productId is essential for subscriptions here
        console.error('[WEBHOOK_ERROR] checkout.session.completed (subscription): Missing productId in session metadata.', { sessionId: session.id });
        return;
    }
    const sessionSubscriptionId = (session as ExtendedCheckoutSession).subscription;
    if (typeof sessionSubscriptionId !== 'string') {
      console.error('Subscription ID is not a string or is missing for a subscription type.', session.id);
      return;
    }
    
    try {
      const subscriptionObject: Stripe.Subscription = await stripe.subscriptions.retrieve(sessionSubscriptionId);
      let actualNewProductId: string | null = null;
      let subPriceId: string | null = null;
      let subInterval: string | null = null;
      let currentPeriodEnd: Date | undefined = undefined;

      if (subscriptionObject.items?.data[0]?.price) {
        const firstItemPrice = subscriptionObject.items.data[0].price;
        subPriceId = firstItemPrice.id;
        if (firstItemPrice.recurring) {
            subInterval = firstItemPrice.recurring.interval;
        }
        if (typeof firstItemPrice.product === 'string') {
          actualNewProductId = firstItemPrice.product;
        } else if (typeof firstItemPrice.product === 'object') {
          actualNewProductId = (firstItemPrice.product as Stripe.Product).id;
        }
      }
      if (subscriptionObject.items?.data[0]?.current_period_end) {
          currentPeriodEnd = new Date(subscriptionObject.items.data[0].current_period_end * 1000);
      }
      
      console.log(`[WEBHOOK] checkout.session.completed (subscription): Extracted Price ID: ${subPriceId}, Interval: ${subInterval}, Actual Product ID: ${actualNewProductId}`);

      profileUpdatePayload = {
        ...profileUpdatePayload,
        subscribed_product_id: actualNewProductId || productIdFromMetadata, // Fallback to metadata if needed, but actual is better
        stripe_subscription_id: sessionSubscriptionId,
        subscription_status: subscriptionObject.status,
        subscription_current_period_end: currentPeriodEnd ? currentPeriodEnd.toISOString() : null,
        subscription_price_id: subPriceId,
        subscription_interval: subInterval,
      };
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error(`[WEBHOOK_ERROR] checkout.session.completed (subscription): Error fetching subscription ${sessionSubscriptionId}:`, error);
      }
      return; 
    }
  } else if (type === 'once') {
    console.log(`[WEBHOOK] checkout.session.completed (one-time): Session ID: ${session.id}, Payment Status: ${session.payment_status}. Minimal update here. Purchase details fully handled by async_payment_succeeded if payment was not 'paid' immediately.`);
    // For type 'once', profileUpdatePayload remains minimal (only stripe_customer_id).
    // If payment_status is 'paid' here, async_payment_succeeded will still run if Stripe sends it,
    // or if not, then the purchase might be missed. This highlights the importance of async_payment_succeeded for reliability.
    // We could also set a temporary status here if needed, e.g., profileUpdatePayload.subscription_status = session.payment_status;
    // but it risks overwriting an active subscription's status. So, better to leave it minimal.
  } else {
    console.warn(`[WEBHOOK_WARN] checkout.session.completed: Unknown session type '${type}'. Skipping specific field updates.`);
    // Only stripe_customer_id will be updated from the initial payload.
  }

  console.log('[WEBHOOK] checkout.session.completed: Attempting to update/insert profile for email:', userEmail, 'with data:', JSON.stringify(profileUpdatePayload, null, 2));

  const { data: updatedProfile, error: profileError } = await supabaseAdmin
    .from('profiles')
    .update(profileUpdatePayload)
    .eq('email', userEmail)
    .select()
    .single();

  if (profileError) {
    if (profileError.code === 'PGRST116') { // Row not found, attempt insert
        console.warn(`[WEBHOOK_WARN] checkout.session.completed: No profile found for email ${userEmail} to update. Attempting to insert.`);
        const insertData = { email: userEmail, ...profileUpdatePayload }; 
        const { error: insertError } = await supabaseAdmin.from('profiles').insert(insertData);
        if (insertError) {
            console.error(`[WEBHOOK_ERROR] checkout.session.completed: Failed to insert missing profile for ${userEmail}. Error:`, insertError.message);
            throw insertError;
        }
        console.log(`[WEBHOOK] checkout.session.completed: Profile for email ${userEmail} inserted successfully.`);
    } else {
        console.error(`[WEBHOOK_ERROR] checkout.session.completed: Supabase error updating profile for email ${userEmail}. Error:`, profileError.message);
        throw profileError;
    }
  } else {
    console.log(`[WEBHOOK] checkout.session.completed: Profile for email ${userEmail} updated successfully. Data:`, JSON.stringify(updatedProfile, null, 2));
  }

  if (type === 'subscription' && existingSubscriptionIdToCancel && existingSubscriptionIdToCancel !== profileUpdatePayload.stripe_subscription_id) {
    try {
      console.log(`[WEBHOOK] checkout.session.completed: Attempting to cancel old subscription ${existingSubscriptionIdToCancel} due to upgrade to ${profileUpdatePayload.stripe_subscription_id}.`);
      await stripe.subscriptions.cancel(existingSubscriptionIdToCancel);
      console.log(`[WEBHOOK] checkout.session.completed: Old subscription ${existingSubscriptionIdToCancel} cancelled successfully.`);
    } catch (cancelError: unknown) {
      console.error(`[WEBHOOK_ERROR] checkout.session.completed: Error cancelling old subscription ${existingSubscriptionIdToCancel}:`, 
        cancelError instanceof Error ? cancelError.message : 'Unknown error');
    }
  }
}

async function handleCheckoutSessionAsyncPaymentSucceeded(session: Stripe.Checkout.Session) {
  console.log(`[WEBHOOK] Stripe event: checkout.session.async_payment_succeeded. Session ID: ${session.id}`);

  if (session.payment_status !== 'paid') {
    console.warn(`[WEBHOOK_WARN] checkout.session.async_payment_succeeded: Received for session ${session.id} but payment_status is ${session.payment_status}. Skipping.`);
    return;
  }

  if (session.metadata?.type !== 'once') {
    console.log(`[WEBHOOK] checkout.session.async_payment_succeeded: Session type is '${session.metadata?.type}', not 'once'. Skipping update to purchases array for session ${session.id}.`);
    return;
  }

  const userEmail = session.customer_details?.email || session.metadata?.userEmail;
  const productId = session.metadata?.productId;
  const stripeCustomerId = typeof session.customer === 'string' ? session.customer : null;

  if (!userEmail || !productId || !stripeCustomerId) {
    console.error('[WEBHOOK_ERROR] checkout.session.async_payment_succeeded: Missing essential data.', { sessionId: session.id, userEmail, productId, stripeCustomerId });
    return;
  }

  let updatedPurchases: string[] = [];
  try {
    const { data: profile, error: fetchError } = await supabaseAdmin
      .from('profiles')
      .select('purchases')
      .eq('stripe_customer_id', stripeCustomerId)
      .single();

    let currentPurchases: string[] = [];
    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error(`[WEBHOOK_ERROR] checkout.session.async_payment_succeeded: Error fetching profile for customer ${stripeCustomerId}:`, fetchError.message);
      return;
    } else if (profile?.purchases && Array.isArray(profile.purchases)) {
      currentPurchases = profile.purchases;
    } else if (!profile && fetchError?.code === 'PGRST116') {
      console.warn(`[WEBHOOK_WARN] checkout.session.async_payment_succeeded: Profile not found for customer ${stripeCustomerId}. checkout.session.completed should have created/linked it.`);
      return; // Cannot update purchases if profile doesn't exist.
    }

    if (!currentPurchases.includes(productId)) {
      currentPurchases.push(productId);
    }
    updatedPurchases = currentPurchases;
  } catch (e) {
     console.error(`[WEBHOOK_ERROR] checkout.session.async_payment_succeeded: Exception processing purchases for customer ${stripeCustomerId}:`, e);
     return;
  }

  const profileUpdateForPurchase: Partial<ProfileUpdateData> = {
    purchases: updatedPurchases,
    // DO NOT update subscription_status or other general subscription fields here.
    // This handler is ONLY for appending to the purchases array.
  };

  console.log(`[WEBHOOK] checkout.session.async_payment_succeeded: Attempting to update profile for customer ${stripeCustomerId} with data:`, JSON.stringify(profileUpdateForPurchase, null, 2));

  const { data: finalUpdatedProfile, error: updateError } = await supabaseAdmin
    .from('profiles')
    .update(profileUpdateForPurchase)
    .eq('stripe_customer_id', stripeCustomerId)
    .select('id, email, purchases, stripe_subscription_id, subscription_status')
    .single();

  if (updateError) {
    console.error(`[WEBHOOK_ERROR] checkout.session.async_payment_succeeded: Supabase error updating profile for customer ${stripeCustomerId}. Error:`, updateError.message);
    if (updateError.code === 'PGRST116') {
        console.error(`[WEBHOOK_FATAL] checkout.session.async_payment_succeeded: Profile not found for update (customer: ${stripeCustomerId}). This is unexpected.`);
    }
    throw updateError;
  }
  console.log(`[WEBHOOK] checkout.session.async_payment_succeeded: Profile for customer ${stripeCustomerId} updated. Current state:`, JSON.stringify(finalUpdatedProfile, null, 2));
}

// TODO: Implement handlers for other relevant events like invoice.payment_succeeded, customer.subscription.updated, etc.
async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log(`[WEBHOOK] Stripe event: invoice.payment_succeeded. Invoice ID: ${invoice.id}, Subscription ID (from invoice): ${(invoice as ExtendedInvoice).subscription}, Customer ID (from invoice): ${(invoice as ExtendedInvoice).customer}`);
  
  const stripeSubscriptionIdFromInvoice = (invoice as ExtendedInvoice).subscription;
  const stripeSubscriptionId = typeof stripeSubscriptionIdFromInvoice === 'string' ? stripeSubscriptionIdFromInvoice : null;

  if (!stripeSubscriptionId) {
    console.log(`[WEBHOOK] invoice.payment_succeeded: Invoice ${invoice.id} does not have a direct string subscription ID. This might be for a one-time payment, setup fee, or not a subscription managed by this profile system. Skipping profile update for subscription renewal.`);
    return;
  }
  console.log(`[WEBHOOK] invoice.payment_succeeded: Processing for subscription ID: ${stripeSubscriptionId}`);

  let subscriptionObject: Stripe.Subscription;
  try {
    subscriptionObject = await stripe.subscriptions.retrieve(stripeSubscriptionId);
    console.log(`[WEBHOOK] invoice.payment_succeeded: Retrieved subscription ${stripeSubscriptionId}. Status: ${subscriptionObject.status}`);
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error(`[WEBHOOK_ERROR] invoice.payment_succeeded: Error retrieving subscription ${stripeSubscriptionId} for invoice ${invoice.id}:`, error);
    }
    return;
  }

  const stripeCustomerIdFromInvoice = (invoice as ExtendedInvoice).customer;
  const stripeCustomerId = typeof stripeCustomerIdFromInvoice === 'string' ? stripeCustomerIdFromInvoice : null;

  if (!stripeCustomerId) {
    console.error('[WEBHOOK_ERROR] invoice.payment_succeeded: Customer ID is not a string or is missing on invoice.', { invoiceId: invoice.id });
    return;
  }
  console.log(`[WEBHOOK] invoice.payment_succeeded: StripeCustomerID from invoice: ${stripeCustomerId}`);

  let userEmail: string | null = null;
  try {
    const customer = await stripe.customers.retrieve(stripeCustomerId);
    if (customer.deleted) {
        console.warn(`[WEBHOOK_WARN] invoice.payment_succeeded: Customer ${stripeCustomerId} is deleted. Cannot update profile.`);
        return;
    }
    userEmail = customer.email;
    if (!userEmail) {
      console.error(`[WEBHOOK_ERROR] invoice.payment_succeeded: Email is missing for Stripe customer ${stripeCustomerId}. Cannot find profile to update.`);
      return;
    }
    console.log(`[WEBHOOK] invoice.payment_succeeded: Retrieved email ${userEmail} for customer ${stripeCustomerId}`);
  } catch (error) {
    console.error(`[WEBHOOK_ERROR] invoice.payment_succeeded: Error fetching customer ${stripeCustomerId} from Stripe:`, error);
    return;
  }

  const price = subscriptionObject.items.data[0]?.price;
  if (!price || !price.product) {
    console.error('[WEBHOOK_ERROR] invoice.payment_succeeded: Price or product information missing on subscription item for invoice:', { subscriptionId: subscriptionObject.id, items: subscriptionObject.items.data });
    return;
  }
  const subscribedProductIdRaw = price.product;
  const subscribedProductId: string | null = typeof subscribedProductIdRaw === 'string' ? subscribedProductIdRaw : (subscribedProductIdRaw as Stripe.Product).id || null;
  console.log(`[WEBHOOK] invoice.payment_succeeded: Subscribed Product ID: ${subscribedProductId}`);

  const profileUpdate: ProfileUpdateData = {
    stripe_customer_id: stripeCustomerId,
    stripe_subscription_id: subscriptionObject.id,
    subscription_status: subscriptionObject.status,
    subscribed_product_id: subscribedProductId,
    subscription_price_id: null,
    subscription_interval: null,
    cancel_at_period_end: subscriptionObject.cancel_at_period_end,
  };

  // Get current_period_end, price_id, and interval from the subscription item
  let currentPeriodEnd: Date | undefined;
  let subPriceId: string | null = null;
  let subInterval: string | null = null;

  if (subscriptionObject.items && 
      subscriptionObject.items.data && 
      subscriptionObject.items.data.length > 0) {
    const firstItem = subscriptionObject.items.data[0] as SubscriptionItem;
    const periodEndTimestamp = firstItem.current_period_end;
    
    if (typeof periodEndTimestamp === 'number' && !isNaN(periodEndTimestamp)) {
      currentPeriodEnd = new Date(periodEndTimestamp * 1000);
      profileUpdate.subscription_current_period_end = currentPeriodEnd.toISOString();
      console.log(`[WEBHOOK] invoice.payment_succeeded: Using period end date: ${currentPeriodEnd.toISOString()}`);
    } else {
      console.warn(`[WEBHOOK_WARN] invoice.payment_succeeded: Invalid/missing current_period_end in subscription items.`);
    }

    // Extract priceId and interval
    if (firstItem.price) {
        subPriceId = firstItem.price.id;
        if (firstItem.price.recurring) {
            subInterval = firstItem.price.recurring.interval;
        }
        console.log(`[WEBHOOK] invoice.payment_succeeded: Extracted Price ID: ${subPriceId}, Interval: ${subInterval}`);
    } else {
        console.warn(`[WEBHOOK_WARN] invoice.payment_succeeded: Subscription item for ${stripeSubscriptionId} missing price object.`);
    }
  } else {
    console.warn(`[WEBHOOK_WARN] invoice.payment_succeeded: No subscription items found in subscription ${stripeSubscriptionId}.`);
  }
  
  profileUpdate.subscription_price_id = subPriceId;
  profileUpdate.subscription_interval = subInterval;
  
  console.log(`[WEBHOOK] invoice.payment_succeeded: Attempting to update profile for email: ${userEmail} with data:`, JSON.stringify(profileUpdate, null, 2));

  const { data: updatedProfile, error: profileError } = await supabaseAdmin
    .from('profiles')
    .update(profileUpdate)
    .eq('email', userEmail)
    .select()
    .single();

  if (profileError) {
    console.error(`[WEBHOOK_ERROR] invoice.payment_succeeded: Supabase error updating profile for email ${userEmail}. Error:`, profileError.message, 'Data:', JSON.stringify(profileUpdate, null, 2));
    throw profileError; 
  }

  if (!updatedProfile) {
    console.warn(`[WEBHOOK_WARN] invoice.payment_succeeded: No profile found for email ${userEmail} (customer ${stripeCustomerId}) to update. Attempting to insert.`);
    const insertData = { email: userEmail, ...profileUpdate };
    console.log('[WEBHOOK] invoice.payment_succeeded: Attempting to insert profile with data:', JSON.stringify(insertData, null, 2));
    const { error: insertError } = await supabaseAdmin
        .from('profiles')
        .insert(insertData);
    if (insertError) {
        console.error(`[WEBHOOK_ERROR] invoice.payment_succeeded: Failed to insert missing profile for ${userEmail}. Error:`, insertError.message, 'Data:', JSON.stringify(insertData, null, 2));
        throw insertError;
    }
    console.log(`[WEBHOOK] invoice.payment_succeeded: Profile for email ${userEmail} inserted after invoice ${invoice.id} for subscription ${subscriptionObject.id}`);
  } else {
    console.log(`[WEBHOOK] invoice.payment_succeeded: Profile for email ${userEmail} updated by invoice ${invoice.id}. Updated profile:`, JSON.stringify(updatedProfile, null, 2));
  }
}

async function handleCustomerSubscriptionUpdated(subscription: Stripe.Subscription) {
  console.log(`[WEBHOOK] Stripe event: customer.subscription.updated. Subscription ID: ${subscription.id}, Status: ${subscription.status}, Customer ID: ${subscription.customer}`);

  const stripeSubscriptionId = subscription.id;
  const stripeCustomerId = typeof subscription.customer === 'string' ? subscription.customer : (subscription.customer as Stripe.Customer)?.id;
  const subscriptionStatus = subscription.status;
  
  let itemCurrentPeriodEndTimestamp: number | undefined;
  if (subscription.items?.data[0]?.current_period_end) { // Safely access current_period_end
    itemCurrentPeriodEndTimestamp = subscription.items.data[0].current_period_end;
  }

  let currentPeriodEnd: Date | undefined; // For the main update data
  let subPriceId: string | null = null; // Restored declaration
  let subInterval: string | null = null; // Restored declaration
  let localSubscribedProductId: string | null = null;
  
  if (!stripeCustomerId) {
    console.error('[WEBHOOK_ERROR] customer.subscription.updated: Stripe Customer ID missing on subscription object.', { subscriptionId: stripeSubscriptionId });
    return;
  }

  // Fetch existing profile to check current subscription ID
  let existingProfileStripeSubId: string | null = null;
  let profileForCheck;
  try {
    const { data: fetchedProfile, error: profileFetchError } = await supabaseAdmin
        .from('profiles')
        .select('stripe_subscription_id, email') // Fetch email too for later use
        .eq('stripe_customer_id', stripeCustomerId)
        .single();
    if (profileFetchError && profileFetchError.code !== 'PGRST116') {
        console.warn(`[WEBHOOK_WARN] customer.subscription.updated: Error fetching profile by stripe_customer_id ${stripeCustomerId}: ${profileFetchError.message}`);
    }
    if (fetchedProfile) {
        existingProfileStripeSubId = fetchedProfile.stripe_subscription_id;
        profileForCheck = fetchedProfile;
    }
  } catch (e) {
    console.error(`[WEBHOOK_ERROR] customer.subscription.updated: Exception fetching profile for ${stripeCustomerId}`, e);
  }
  
  const isTerminalStatus = subscriptionStatus === 'canceled' || subscriptionStatus === 'unpaid';
  const isDefinitelyEnded = subscription.cancel_at_period_end && itemCurrentPeriodEndTimestamp && (itemCurrentPeriodEndTimestamp * 1000 < Date.now());

  if (existingProfileStripeSubId && existingProfileStripeSubId !== stripeSubscriptionId && (isTerminalStatus || isDefinitelyEnded)) {
    console.log(`[WEBHOOK] customer.subscription.updated: Profile for customer ${stripeCustomerId} (email: ${profileForCheck?.email}) already has subscription ${existingProfileStripeSubId}. Current event for ${stripeSubscriptionId} (status: ${subscriptionStatus}) is for an older/superseded subscription. Skipping update.`);
    return;
  }

  // Now, proceed to extract details from subscription.items.data[0] for the main update
  if (subscription.items?.data[0]) {
    const firstItem = subscription.items.data[0] as SubscriptionItem;
    // itemCurrentPeriodEndTimestamp is already fetched and can be used here
    if (typeof itemCurrentPeriodEndTimestamp === 'number' && !isNaN(itemCurrentPeriodEndTimestamp)) {
      currentPeriodEnd = new Date(itemCurrentPeriodEndTimestamp * 1000);
      console.log(`[WEBHOOK] customer.subscription.updated: Found period end timestamp ${itemCurrentPeriodEndTimestamp}, date: ${currentPeriodEnd.toISOString()}`);
    } else {
      // Log if still undefined after initial attempt, or if it became undefined
      console.warn(`[WEBHOOK_WARN] customer.subscription.updated: Invalid or missing current_period_end in subscription items (itemCurrentPeriodEndTimestamp value: ${itemCurrentPeriodEndTimestamp}).`);
    }

    // Extract priceId, interval, and productId
    if (firstItem.price) {
        subPriceId = firstItem.price.id;
        if (firstItem.price.recurring) {
            subInterval = firstItem.price.recurring.interval;
        }
        if (firstItem.price.product && typeof firstItem.price.product === 'string') {
            localSubscribedProductId = firstItem.price.product;
        } else if (firstItem.price.product && typeof firstItem.price.product === 'object' && (firstItem.price.product as Stripe.Product).id) {
            localSubscribedProductId = (firstItem.price.product as Stripe.Product).id;
        }
        console.log(`[WEBHOOK] customer.subscription.updated: Extracted Price ID: ${subPriceId}, Interval: ${subInterval}, Product ID: ${localSubscribedProductId}`);
    } else {
        console.warn(`[WEBHOOK_WARN] customer.subscription.updated: Subscription item for ${stripeSubscriptionId} missing price object.`);
    }
  } else {
    console.warn(`[WEBHOOK_WARN] customer.subscription.updated: Subscription ${stripeSubscriptionId} has no items data.`);
  }
  
  console.log(`[WEBHOOK] customer.subscription.updated: Final Subscribed Product ID: ${localSubscribedProductId}`);

  let userEmail: string | null = null;
  let customerIsDeleted = false;
  if (profileForCheck?.email) { // Use email from already fetched profile if available
      userEmail = profileForCheck.email;
  } else { // Fallback to fetching customer from Stripe if profile wasn't found or didn't have email
      try {
        const customer = await stripe.customers.retrieve(stripeCustomerId);
        if (customer.deleted) {
          customerIsDeleted = true;
          console.warn(`[WEBHOOK_WARN] customer.subscription.updated: Customer ${stripeCustomerId} is deleted.`);
        } else {
          userEmail = customer.email;
        }
        
        if (userEmail) {
          console.log(`[WEBHOOK] customer.subscription.updated: Retrieved email ${userEmail} for customer ${stripeCustomerId} from Stripe.`);
        } else if (!customerIsDeleted) {
          console.warn(`[WEBHOOK_WARN] customer.subscription.updated: Email is missing for active Stripe customer ${stripeCustomerId}. Will attempt update by stripe_customer_id only.`);
        }
    
      } catch (error: unknown) {
        console.error(`[WEBHOOK_ERROR] customer.subscription.updated: Error fetching customer ${stripeCustomerId} from Stripe: ${
          error instanceof Error ? error.message : 'Unknown error'
        }. Will attempt update by stripe_customer_id only if profile exists.`);
      }
  }

  const profileDataToUpdate: ProfileUpdateData = {
    stripe_subscription_id: stripeSubscriptionId,
    subscription_status: subscriptionStatus,
    subscribed_product_id: localSubscribedProductId,
    subscription_price_id: subPriceId,
    subscription_interval: subInterval,
    cancel_at_period_end: subscription.cancel_at_period_end,
  };
  
  if (currentPeriodEnd) {
    profileDataToUpdate.subscription_current_period_end = currentPeriodEnd.toISOString();
  }

  console.log('[WEBHOOK] customer.subscription.updated: Profile data to update:', JSON.stringify(profileDataToUpdate, null, 2));

  if (userEmail) {
    console.log(`[WEBHOOK] customer.subscription.updated: Attempting to update profile by email: ${userEmail}`);
    const { data: updatedProfile, error: emailUpdateError } = await supabaseAdmin
      .from('profiles')
      .update(profileDataToUpdate)
      .eq('email', userEmail)
      .select()
      .single();

    if (emailUpdateError) {
      console.error(`[WEBHOOK_ERROR] customer.subscription.updated: Supabase error updating profile by email ${userEmail}. Error:`, emailUpdateError.message, 'Data:', JSON.stringify(profileDataToUpdate, null, 2));
      // If email update fails, and we have stripeCustomerId, maybe try updating by stripe_customer_id as a fallback?
      // For now, let's throw to retry unless it's a "not found" error, then try stripe_customer_id.
      if (emailUpdateError.code === 'PGRST116' && stripeCustomerId) { // PGRST116: "Row to update not found" (0 rows)
          console.warn(`[WEBHOOK_WARN] customer.subscription.updated: Profile not found by email ${userEmail}. Will attempt update by stripe_customer_id ${stripeCustomerId}.`);
      } else {
        throw emailUpdateError;
      }
    } else if (updatedProfile) {
      console.log(`[WEBHOOK] customer.subscription.updated: Profile for email ${userEmail} updated successfully. Data:`, JSON.stringify(updatedProfile, null, 2));
      return; // Success
    } else {
       // Profile not found by email, and no error thrown for it (e.g. RLS made it seem like not found)
       // This case is less likely if PGRST116 is handled above.
       console.warn(`[WEBHOOK_WARN] customer.subscription.updated: Profile not found by email ${userEmail} (no error, or RLS). Proceeding to try stripe_customer_id ${stripeCustomerId} if available.`);
    }
  }

  // Fallback or primary update path if no email or if email update failed to find a row
  if (stripeCustomerId) {
    console.log(`[WEBHOOK] customer.subscription.updated: Attempting to update profile by stripe_customer_id: ${stripeCustomerId}`);
    // Ensure stripe_customer_id is part of the update data if we're solely relying on it for identification in a missing profile scenario
    // However, for an update, it's used in `eq`. The `profileDataToUpdate` should not necessarily include it unless we're certain.
    // Let's assume profile exists and `stripe_customer_id` is already correctly set.
    // If the profile is missing, the trigger should have created it with the email, so this path is more for updates.
    const updatePayloadForCustomerIdMatch = { ...profileDataToUpdate };
    // If we did not have an email, we might not have stripe_customer_id in profileDataToUpdate yet.
    // But it's fine, stripe_customer_id is used for `eq`. We set it on profile at creation.

    const { data: updatedProfileByCustomerId, error: customerIdUpdateError } = await supabaseAdmin
      .from('profiles')
      .update(updatePayloadForCustomerIdMatch) // Use the same payload
      .eq('stripe_customer_id', stripeCustomerId)
      .select()
      .single(); // Assuming one profile per stripe_customer_id

    if (customerIdUpdateError) {
      console.error(`[WEBHOOK_ERROR] customer.subscription.updated: Supabase error updating profile by stripe_customer_id ${stripeCustomerId}. Error:`, customerIdUpdateError.message, 'Data:', JSON.stringify(updatePayloadForCustomerIdMatch, null, 2));
      // If profile still not found by stripe_customer_id, it's a more serious issue.
      throw customerIdUpdateError;
    }

    if (updatedProfileByCustomerId) {
      console.log(`[WEBHOOK] customer.subscription.updated: Profile for stripe_customer_id ${stripeCustomerId} updated successfully. Data:`, JSON.stringify(updatedProfileByCustomerId, null, 2));
    } else {
      console.warn(`[WEBHOOK_WARN] customer.subscription.updated: No profile found for stripe_customer_id ${stripeCustomerId} to update. This is unexpected if customer is not deleted.`);
      // Potentially insert if absolutely necessary and we have an email, but this indicates a problem.
      // For customer.subscription.updated, an existing profile is expected.
    }
  } else {
    console.error('[WEBHOOK_ERROR] customer.subscription.updated: Cannot update profile - Stripe Customer ID is missing and email lookup failed or email was not available.');
  }
}

async function handleCustomerSubscriptionCreated(subscription: Stripe.Subscription) {
  console.log(`[WEBHOOK] Stripe event: customer.subscription.created. Subscription ID: ${subscription.id}, Status: ${subscription.status}, Customer ID: ${subscription.customer}`);

  const stripeSubscriptionId = subscription.id;
  const stripeCustomerId = typeof subscription.customer === 'string' ? subscription.customer : (subscription.customer as Stripe.Customer)?.id;
  const subscriptionStatus = subscription.status;
  
  let itemCurrentPeriodEndTimestamp: number | undefined;
  if (subscription.items?.data[0]?.current_period_end) { // Safely access current_period_end
    itemCurrentPeriodEndTimestamp = subscription.items.data[0].current_period_end;
  }

  let currentPeriodEnd: Date | undefined; // For the main update data
  let subPriceId: string | null = null;
  let subInterval: string | null = null;
  let localSubscribedProductId: string | null = null;
  
  if (!stripeCustomerId) {
    console.error('[WEBHOOK_ERROR] customer.subscription.created: Stripe Customer ID missing on subscription object.', { subscriptionId: stripeSubscriptionId });
    return;
  }

  // Now, proceed to extract details from subscription.items.data[0] for the main update
  if (subscription.items?.data[0]) {
    const firstItem = subscription.items.data[0] as SubscriptionItem;
    if (typeof itemCurrentPeriodEndTimestamp === 'number' && !isNaN(itemCurrentPeriodEndTimestamp)) {
      currentPeriodEnd = new Date(itemCurrentPeriodEndTimestamp * 1000);
      console.log(`[WEBHOOK] customer.subscription.created: Found period end timestamp ${itemCurrentPeriodEndTimestamp}, date: ${currentPeriodEnd.toISOString()}`);
    } else {
      console.warn(`[WEBHOOK_WARN] customer.subscription.created: Invalid or missing current_period_end in subscription items.`);
    }

    // Extract priceId, interval, and productId
    if (firstItem.price) {
        subPriceId = firstItem.price.id;
        if (firstItem.price.recurring) {
            subInterval = firstItem.price.recurring.interval;
        }
        if (firstItem.price.product && typeof firstItem.price.product === 'string') {
            localSubscribedProductId = firstItem.price.product;
        } else if (firstItem.price.product && typeof firstItem.price.product === 'object' && (firstItem.price.product as Stripe.Product).id) {
            localSubscribedProductId = (firstItem.price.product as Stripe.Product).id;
        }
        console.log(`[WEBHOOK] customer.subscription.created: Extracted Price ID: ${subPriceId}, Interval: ${subInterval}, Product ID: ${localSubscribedProductId}`);
    } else {
        console.warn(`[WEBHOOK_WARN] customer.subscription.created: Subscription item for ${stripeSubscriptionId} missing price object.`);
    }
  } else {
    console.warn(`[WEBHOOK_WARN] customer.subscription.created: Subscription ${stripeSubscriptionId} has no items data.`);
  }
  
  console.log(`[WEBHOOK] customer.subscription.created: Final Subscribed Product ID: ${localSubscribedProductId}`);

  // Get customer email
  let userEmail: string | null = null;
  try {
    const customer = await stripe.customers.retrieve(stripeCustomerId);
    if (customer.deleted) {
      console.warn(`[WEBHOOK_WARN] customer.subscription.created: Customer ${stripeCustomerId} is deleted.`);
      return;
    }
    userEmail = customer.email;
    
    if (userEmail) {
      console.log(`[WEBHOOK] customer.subscription.created: Retrieved email ${userEmail} for customer ${stripeCustomerId} from Stripe.`);
    } else {
      console.warn(`[WEBHOOK_WARN] customer.subscription.created: Email is missing for Stripe customer ${stripeCustomerId}. Cannot update profile.`);
      return;
    }
  } catch (error: unknown) {
    console.error(`[WEBHOOK_ERROR] customer.subscription.created: Error fetching customer ${stripeCustomerId} from Stripe: ${
      error instanceof Error ? error.message : 'Unknown error'
    }. Cannot update profile.`);
    return;
  }

  const profileDataToUpdate: ProfileUpdateData = {
    stripe_customer_id: stripeCustomerId,
    stripe_subscription_id: stripeSubscriptionId,
    subscription_status: subscriptionStatus,
    subscribed_product_id: localSubscribedProductId,
    subscription_price_id: subPriceId,
    subscription_interval: subInterval,
    cancel_at_period_end: subscription.cancel_at_period_end,
  };
  
  if (currentPeriodEnd) {
    profileDataToUpdate.subscription_current_period_end = currentPeriodEnd.toISOString();
  }

  console.log('[WEBHOOK] customer.subscription.created: Profile data to update:', JSON.stringify(profileDataToUpdate, null, 2));

  // Update profile by email
  const { data: updatedProfile, error: emailUpdateError } = await supabaseAdmin
    .from('profiles')
    .update(profileDataToUpdate)
    .eq('email', userEmail)
    .select()
    .single();

  if (emailUpdateError) {
    console.error(`[WEBHOOK_ERROR] customer.subscription.created: Supabase error updating profile by email ${userEmail}. Error:`, emailUpdateError.message, 'Data:', JSON.stringify(profileDataToUpdate, null, 2));
    
    if (emailUpdateError.code === 'PGRST116') { // Row not found
      console.log(`[WEBHOOK] customer.subscription.created: No profile found for email ${userEmail}. Attempting to insert.`);
      
      const insertData = { 
        email: userEmail, 
        ...profileDataToUpdate 
      };
      
      const { error: insertError } = await supabaseAdmin
        .from('profiles')
        .insert(insertData);
        
      if (insertError) {
        console.error(`[WEBHOOK_ERROR] customer.subscription.created: Failed to insert profile for email ${userEmail}. Error:`, insertError.message);
        throw insertError;
      }
      
      console.log(`[WEBHOOK] customer.subscription.created: Profile for email ${userEmail} inserted successfully.`);
    } else {
      throw emailUpdateError;
    }
  } else {
    console.log(`[WEBHOOK] customer.subscription.created: Profile for email ${userEmail} updated successfully. Data:`, JSON.stringify(updatedProfile, null, 2));
  }
}

async function handleCustomerSubscriptionDeleted(subscription: Stripe.Subscription) {
  console.log(`[WEBHOOK] Stripe event: customer.subscription.deleted. Subscription ID: ${subscription.id}, Customer ID: ${subscription.customer}`);

  const stripeSubscriptionId = subscription.id;
  const stripeCustomerId = typeof subscription.customer === 'string' ? subscription.customer : (subscription.customer as Stripe.Customer)?.id;

  if (!stripeCustomerId) {
    console.error('[WEBHOOK_ERROR] customer.subscription.deleted: Stripe Customer ID missing on subscription object.', { subscriptionId: stripeSubscriptionId });
    return;
  }

  // Fetch existing profile to check current subscription ID
  let existingProfileStripeSubId: string | null = null;
  let profileEmailForLog: string | null = null;

  try {
      const { data: profile, error: profileFetchError } = await supabaseAdmin
          .from('profiles')
          .select('stripe_subscription_id, email')
          .eq('stripe_customer_id', stripeCustomerId)
          .single();

      if (profileFetchError && profileFetchError.code !== 'PGRST116') {
          console.warn(`[WEBHOOK_WARN] customer.subscription.deleted: Error fetching profile by stripe_customer_id ${stripeCustomerId}: ${profileFetchError.message}`);
          // Allow to proceed, update logic will handle missing profile if necessary
      }
      if (profile) {
          existingProfileStripeSubId = profile.stripe_subscription_id;
          profileEmailForLog = profile.email;
      }
  } catch (e) {
      console.error(`[WEBHOOK_ERROR] customer.subscription.deleted: Exception fetching profile for ${stripeCustomerId}`, e);
  }
  
  // If profile has a DIFFERENT subscription ID, this event is for an older, superseded subscription.
  if (existingProfileStripeSubId && existingProfileStripeSubId !== stripeSubscriptionId) {
    console.log(`[WEBHOOK] customer.subscription.deleted: Profile for customer ${stripeCustomerId} (email: ${profileEmailForLog}) has newer subscription ${existingProfileStripeSubId}. Event for deleted subscription ${stripeSubscriptionId} is for an older, superseded subscription. Skipping update.`);
    return;
  }

  const profileUpdateData: ProfileUpdateData = {
    stripe_subscription_id: null,
    subscription_status: subscription.status, 
    subscribed_product_id: null,
    subscription_current_period_end: null, 
    subscription_price_id: null,
    subscription_interval: null,
    cancel_at_period_end: null,
  };
   if (subscription.ended_at) {
    profileUpdateData.subscription_current_period_end = new Date(subscription.ended_at * 1000).toISOString();
  }


  console.log('[WEBHOOK] customer.subscription.deleted: Profile data to update:', JSON.stringify(profileUpdateData, null, 2));

  let userEmail: string | null = null;
  let customerIsDeletedInStripe = false;

  try {
    const customer = await stripe.customers.retrieve(stripeCustomerId);
    if (customer.deleted) {
        customerIsDeletedInStripe = true;
        console.warn(`[WEBHOOK_WARN] customer.subscription.deleted: Customer ${stripeCustomerId} is deleted in Stripe.`);
    } else {
        userEmail = customer.email;
    }
    if (userEmail) {
        console.log(`[WEBHOOK] customer.subscription.deleted: Retrieved email ${userEmail} for customer ${stripeCustomerId}`);
    } else if (!customerIsDeletedInStripe) {
        console.warn(`[WEBHOOK_WARN] customer.subscription.deleted: Email not found for active customer ${stripeCustomerId}. Will update by stripe_customer_id.`);
    } else {
        console.warn(`[WEBHOOK_WARN] customer.subscription.deleted: Customer ${stripeCustomerId} deleted and email not available. Will update by stripe_customer_id.`);
    }
  } catch (error: unknown) {
    console.error(`[WEBHOOK_ERROR] customer.subscription.deleted: Error fetching customer ${stripeCustomerId} from Stripe: ${
      error instanceof Error ? error.message : 'Unknown error'
    }. Will proceed to update by stripe_customer_id.`);
    // Proceed with stripe_customer_id if available
  }

  let updated = false;
  if (userEmail) {
    console.log(`[WEBHOOK] customer.subscription.deleted: Attempting to update profile by email: ${userEmail}`);
    const { error: emailUpdateError, count } = await supabaseAdmin
      .from('profiles')
      .update(profileUpdateData)
      .eq('email', userEmail);
      // .select().single(); // Not selecting here to avoid error if RLS prevents read but allows update

    if (emailUpdateError) {
      console.error(`[WEBHOOK_ERROR] customer.subscription.deleted: Supabase error updating profile by email ${userEmail}. Error:`, emailUpdateError.message, 'Data:', JSON.stringify(profileUpdateData, null, 2));
      if (emailUpdateError.code !== 'PGRST116') { // Don't throw if it's just "not found", will try by customer_id
          throw emailUpdateError;
      }
    } else if (count && count > 0) {
      console.log(`[WEBHOOK] customer.subscription.deleted: Profile for email ${userEmail} updated successfully (${count} row(s)).`);
      updated = true;
    } else {
      console.warn(`[WEBHOOK_WARN] customer.subscription.deleted: No profile found or updated for email ${userEmail}. Will attempt by stripe_customer_id.`);
    }
  }

  if (!updated && stripeCustomerId) {
    console.log(`[WEBHOOK] customer.subscription.deleted: Attempting to update profile by stripe_customer_id: ${stripeCustomerId}`);
    const { error: customerIdUpdateError, count } = await supabaseAdmin
      .from('profiles')
      .update(profileUpdateData)
      .eq('stripe_customer_id', stripeCustomerId);

    if (customerIdUpdateError) {
      console.error(`[WEBHOOK_ERROR] customer.subscription.deleted: Supabase error updating profile by stripe_customer_id ${stripeCustomerId}. Error:`, customerIdUpdateError.message, 'Data:', JSON.stringify(profileUpdateData, null, 2));
      throw customerIdUpdateError;
    }
    if (count && count > 0) {
      console.log(`[WEBHOOK] customer.subscription.deleted: Profile for stripe_customer_id ${stripeCustomerId} updated successfully (${count} row(s)).`);
    } else {
      console.warn(`[WEBHOOK_WARN] customer.subscription.deleted: No profile found or updated for stripe_customer_id ${stripeCustomerId}.`);
    }
  } else if (!updated && !stripeCustomerId) {
      console.error('[WEBHOOK_ERROR] customer.subscription.deleted: Could not update profile, stripe_customer_id missing and email update failed or email not found.');
  }
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  console.log(`[WEBHOOK] Stripe event: invoice.payment_failed. Invoice ID: ${invoice.id}, Subscription ID (from invoice): ${(invoice as ExtendedInvoice).subscription}, Customer ID: ${(invoice as ExtendedInvoice).customer}`);

  const stripeSubscriptionIdFromInvoice = (invoice as ExtendedInvoice).subscription;
  const stripeSubscriptionId = typeof stripeSubscriptionIdFromInvoice === 'string' ? stripeSubscriptionIdFromInvoice : null;
  
  if (!stripeSubscriptionId) {
    console.log(`[WEBHOOK] invoice.payment_failed: Invoice ${invoice.id} does not have a direct subscription ID. May not be a subscription payment or is for a one-time purchase. Skipping profile update.`);
    return;
  }
  console.log(`[WEBHOOK] invoice.payment_failed: Processing for subscription ID: ${stripeSubscriptionId}`);

  let failedSubscription: Stripe.Subscription;
  try {
    failedSubscription = await stripe.subscriptions.retrieve(stripeSubscriptionId);
    console.log(`[WEBHOOK] invoice.payment_failed: Retrieved subscription ${stripeSubscriptionId}. Status: ${failedSubscription.status}`);
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error(`[WEBHOOK_ERROR] invoice.payment_failed: Error retrieving subscription ${stripeSubscriptionId} for invoice ${invoice.id}:`, error);
    }
    return;
  }

  const stripeCustomerIdFromInvoice = (invoice as ExtendedInvoice).customer;
  const stripeCustomerId = typeof stripeCustomerIdFromInvoice === 'string' ? stripeCustomerIdFromInvoice : null;

  if (!stripeCustomerId) {
    console.error('[WEBHOOK_ERROR] invoice.payment_failed: Customer ID is not a string or is missing on invoice.', { invoiceId: invoice.id });
    return;
  }
  console.log(`[WEBHOOK] invoice.payment_failed: StripeCustomerID from invoice: ${stripeCustomerId}`);

  let userEmail: string | null = null;
  let customerIsDeleted = false;
  try {
    const customer = await stripe.customers.retrieve(stripeCustomerId);
    if (customer.deleted) {
      customerIsDeleted = true;
      console.warn(`[WEBHOOK_WARN] invoice.payment_failed: Customer ${stripeCustomerId} is deleted.`);
    } else {
      userEmail = customer.email;
    }

    if (userEmail) {
      console.log(`[WEBHOOK] invoice.payment_failed: Retrieved email ${userEmail} for customer ${stripeCustomerId}`);
    } else if (!customerIsDeleted) {
      console.warn(`[WEBHOOK_WARN] invoice.payment_failed: Email is missing for active Stripe customer ${stripeCustomerId}. Will attempt update by stripe_customer_id only.`);
    } else {
      console.warn(`[WEBHOOK_WARN] invoice.payment_failed: Customer ${stripeCustomerId} deleted and email not available. Will attempt update by stripe_customer_id.`);
    }
  } catch (error: unknown) {
    console.error(`[WEBHOOK_ERROR] invoice.payment_failed: Error fetching customer ${stripeCustomerId} from Stripe: ${
      error instanceof Error ? error.message : 'Unknown error'
    }. Will attempt update by stripe_customer_id only.`);
  }

  // Fetch the current subscription status from Stripe, as the invoice failure might lead to a specific status.
  let currentSubscriptionStatusFromStripe: string | null = null;
  let currentSubscribedProductId: string | null = null;
  let currentPeriodEndFromStripe: string | null = null;
  let subPriceId: string | null = null;
  let subInterval: string | null = null;

  let subscriptionObject;
  try {
    subscriptionObject = await stripe.subscriptions.retrieve(stripeSubscriptionId);
    currentSubscriptionStatusFromStripe = subscriptionObject.status;
    
    if (subscriptionObject.items && 
        subscriptionObject.items.data && 
        subscriptionObject.items.data.length > 0) {
      const firstItem = subscriptionObject.items.data[0] as SubscriptionItem;
      const periodEndTimestamp = firstItem.current_period_end;
      if (typeof periodEndTimestamp === 'number' && !isNaN(periodEndTimestamp)) {
        const currentPeriodEndDate = new Date(periodEndTimestamp * 1000);
        currentPeriodEndFromStripe = currentPeriodEndDate.toISOString();
        console.log(`[WEBHOOK] invoice.payment_failed: Found period end timestamp ${periodEndTimestamp}, date: ${currentPeriodEndDate.toISOString()}`);
      } else {
        console.warn(`[WEBHOOK_WARN] invoice.payment_failed: Invalid or missing current_period_end in subscription items.`);
      }

      if (firstItem.price) {
          const price = firstItem.price;
          if (price.product && typeof price.product === 'string') {
            currentSubscribedProductId = price.product;
          } else if (price.product && typeof price.product === 'object' && (price.product as Stripe.Product).id) {
            currentSubscribedProductId = (price.product as Stripe.Product).id;
          }
          
          subPriceId = price.id;
          if (price.recurring) {
            subInterval = price.recurring.interval;
          }
          console.log(`[WEBHOOK] invoice.payment_failed: Extracted Price ID: ${subPriceId}, Interval: ${subInterval}, Product ID: ${currentSubscribedProductId}`);
      } else {
          console.warn(`[WEBHOOK_WARN] invoice.payment_failed: Subscription item for ${stripeSubscriptionId} missing price object.`);
      }
    } else {
      console.warn(`[WEBHOOK_WARN] invoice.payment_failed: Subscription ${stripeSubscriptionId} has no items data for price/interval.`);
    }
    
    console.log(`[WEBHOOK] invoice.payment_failed: Fetched subscription ${stripeSubscriptionId} from Stripe. Status: ${currentSubscriptionStatusFromStripe}, Final Product ID: ${currentSubscribedProductId}`);
  } catch (subError: unknown) {
    console.error(`[WEBHOOK_ERROR] invoice.payment_failed: Failed to retrieve subscription ${stripeSubscriptionId} from Stripe: ${
      subError instanceof Error ? subError.message : 'Unknown error'
    }. Cannot update profile with latest status.`);
    return; 
  }

  const profileUpdate: ProfileUpdateData = {
    subscription_status: currentSubscriptionStatusFromStripe || 'payment_failed', 
    subscribed_product_id: currentSubscribedProductId, 
    subscription_current_period_end: currentPeriodEndFromStripe,
    subscription_price_id: subPriceId,
    subscription_interval: subInterval,
    cancel_at_period_end: subscriptionObject.cancel_at_period_end,
  };
  console.log('[WEBHOOK] invoice.payment_failed: Profile data to update:', JSON.stringify(profileUpdate, null, 2));

  let updated = false;
  if (userEmail) {
    console.log(`[WEBHOOK] invoice.payment_failed: Attempting to update profile by email: ${userEmail}`);
    const { error: emailUpdateError, count } = await supabaseAdmin
      .from('profiles')
      .update(profileUpdate)
      .eq('email', userEmail);

    if (emailUpdateError) {
      console.error(`[WEBHOOK_ERROR] invoice.payment_failed: Supabase error updating profile by email ${userEmail}. Error:`, emailUpdateError.message, 'Data:', JSON.stringify(profileUpdate, null, 2));
      if (emailUpdateError.code !== 'PGRST116') {
        throw emailUpdateError;
      }
    } else if (count && count > 0) {
      console.log(`[WEBHOOK] invoice.payment_failed: Profile for email ${userEmail} updated successfully (${count} row(s)).`);
      updated = true;
    } else {
      console.warn(`[WEBHOOK_WARN] invoice.payment_failed: No profile found or updated for email ${userEmail}. Will attempt by stripe_customer_id.`);
    }
  }

  if (!updated && stripeCustomerId) {
    console.log(`[WEBHOOK] invoice.payment_failed: Attempting to update profile by stripe_customer_id: ${stripeCustomerId}`);
    const { error: customerIdUpdateError, count } = await supabaseAdmin
      .from('profiles')
      .update(profileUpdate)
      .eq('stripe_customer_id', stripeCustomerId);

    if (customerIdUpdateError) {
      console.error(`[WEBHOOK_ERROR] invoice.payment_failed: Supabase error updating profile by stripe_customer_id ${stripeCustomerId}. Error:`, customerIdUpdateError.message, 'Data:', JSON.stringify(profileUpdate, null, 2));
      throw customerIdUpdateError;
    }
    if (count && count > 0) {
      console.log(`[WEBHOOK] invoice.payment_failed: Profile for stripe_customer_id ${stripeCustomerId} updated successfully (${count} row(s)).`);
    } else {
      console.warn(`[WEBHOOK_WARN] invoice.payment_failed: No profile found or updated for stripe_customer_id ${stripeCustomerId}.`);
    }
  } else if (!updated && !stripeCustomerId) {
     console.error('[WEBHOOK_ERROR] invoice.payment_failed: Could not update profile, stripe_customer_id missing and email update failed or email not found.');
  }
}


export async function POST(req: NextRequest) {
  const signature = req.headers.get('stripe-signature');
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET_LIVE || process.env.STRIPE_WEBHOOK_SECRET;

  if (!signature) {
    console.error('[WEBHOOK_ERROR] POST: Missing Stripe signature.');
    return NextResponse.json({ error: 'Missing stripe-signature header' }, { status: 400 });
  }
  if (!webhookSecret) {
    console.error('[WEBHOOK_ERROR] POST: Stripe webhook secret is not configured.');
    return NextResponse.json({ error: 'Webhook secret not configured' }, { status: 500 });
  }

  let event: Stripe.Event;
  try {
    const body = await req.text();
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    console.log(`[WEBHOOK] POST: Received Stripe event: ${event.type}, ID: ${event.id}`);
  } catch (err: unknown) {
    if (err instanceof Error) {
      console.error(`[WEBHOOK_ERROR] POST: Error verifying webhook signature:`, err.message);
      return NextResponse.json({ error: `Webhook Error: ${err.message}` }, { status: 400 });
    }
    return NextResponse.json({ error: 'Unknown error occurred' }, { status: 400 });
  }

  try {
    if (relevantEvents.has(event.type)) {
      console.log(`[WEBHOOK] POST: Processing relevant event: ${event.type}`);
      switch (event.type) {
        case 'checkout.session.completed':
          await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session);
          break;
        case 'invoice.payment_succeeded':
          await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;
        case 'customer.subscription.created':
          await handleCustomerSubscriptionCreated(event.data.object as Stripe.Subscription);
          break;
        case 'customer.subscription.updated':
          await handleCustomerSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;
        case 'customer.subscription.deleted':
          await handleCustomerSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;
        case 'invoice.payment_failed':
          await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
          break;
        case 'checkout.session.async_payment_succeeded':
          await handleCheckoutSessionAsyncPaymentSucceeded(event.data.object as Stripe.Checkout.Session);
          break;
        default:
          console.warn(`[WEBHOOK_WARN] POST: Unhandled relevant event type: ${event.type}`);
          // Optional: throw new Error to indicate an issue if all relevantEvents should be handled explicitly
      }
      console.log(`[WEBHOOK] POST: Successfully processed event ${event.type}, ID: ${event.id}`);
    } else {
      console.log(`[WEBHOOK] POST: Ignoring irrelevant event type: ${event.type}`);
    }
    return NextResponse.json({ received: true });
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error(`[WEBHOOK_ERROR] POST: Error processing event ${event.type} (ID: ${event.id}):`, error.message, error.stack);
      // If Supabase errors were thrown, Stripe will retry.
      // For other errors, decide if a 500 is appropriate to trigger retries.
      return NextResponse.json({ error: 'Webhook handler failed. Error: ' + error.message }, { status: 500 });
    }
    return NextResponse.json({ error: 'Unknown error occurred' }, { status: 500 });
  }
} 
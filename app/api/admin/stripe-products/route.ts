import { NextResponse } from 'next/server';
// import { supabaseAdmin } from '@/lib/supabase/admin'; // No longer needed in this file
import <PERSON><PERSON> from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-06-30.basil' // Ensure this is your desired API version
});

// Interface for product data returned by the API to the UI
// This might need adjustment based on what the admin UI actually consumes.
interface ProductAPIData {
  id: string;
  name: string;
  description?: string | null;
  active: boolean;
  default_price_id?: string | null; // This will be a string ID from Stripe product
  is_visible_on_subscribe_page: boolean;
  exclusivity_group: string | null;
  display_order: number;
  one_time_purchase: boolean;
  tier: number | null;
  created: string; // ISO string from Stripe's Unix timestamp
  updated: string; // ISO string from Stripe's Unix timestamp
  // Add other fields if your admin UI needs them, e.g., marketing_features, images
}

// Helper to transform Stripe product to API product
// This now takes a Stripe.Product object directly.
function transformStripeProductToAPIData(stripeProduct: Stripe.Product): ProductAPIData {
  const metadata = stripeProduct.metadata || {};
  
  const name = stripeProduct.name || 'Unnamed Product';
  const description = stripeProduct.description || null;

  const isVisible = metadata.is_visible?.toLowerCase() === 'true';
  const group = (metadata.group?.toLowerCase() !== 'null' && metadata.group) ? metadata.group : null;
  const displayOrder = parseInt(metadata.display_order || '0', 10) || 0;
  const oneTimePurchase = metadata.one_time_purchase?.toLowerCase() === 'true';
  
  let tier: number | null = null;
  if (metadata.tier) {
    if (metadata.tier.toLowerCase() === 'null') {
      tier = null;
    } else {
      const parsedTier = parseInt(metadata.tier, 10);
      tier = isNaN(parsedTier) ? null : parsedTier;
    }
  }
  
  // Get default_price_id (Stripe stores it as a string or an expanded object)
  let defaultPriceId: string | null = null;
  if (typeof stripeProduct.default_price === 'string') {
    defaultPriceId = stripeProduct.default_price;
  } else if (stripeProduct.default_price && typeof stripeProduct.default_price === 'object') {
    defaultPriceId = stripeProduct.default_price.id;
  }

  return {
    id: stripeProduct.id,
    name: name,
    active: stripeProduct.active,
    default_price_id: defaultPriceId,
    description: description,
    is_visible_on_subscribe_page: isVisible,
    exclusivity_group: group,
    display_order: displayOrder,
    one_time_purchase: oneTimePurchase,
    tier: tier,
    created: new Date(stripeProduct.created * 1000).toISOString(),
    updated: new Date(stripeProduct.updated * 1000).toISOString(),
  };
}

export async function GET() {
  try {
    console.log('[ADMIN_PRODUCTS_API GET] Fetching products from Stripe...');
    const products = await stripe.products.list({
      active: true, // Fetch only active products, or remove/change if you need inactive ones too
      limit: 100,   // Adjust limit as needed, Stripe defaults to 10, max 100 per call
      expand: ['data.default_price'], // Expand default_price to get its ID or details if needed
    });

    if (!products || !products.data) {
      console.log('[ADMIN_PRODUCTS_API GET] No products data found in Stripe response.');
      return NextResponse.json({ products: [] }); // Return empty array with success if no products
    }

    console.log(`[ADMIN_PRODUCTS_API GET] Fetched ${products.data.length} products.`);
    
    const transformedProducts = products.data.map(product => 
      transformStripeProductToAPIData(product as Stripe.Product)
    );
    
    // Optional: Sort transformed products if needed, e.g., by display_order or name
    transformedProducts.sort((a, b) => {
        if (a.display_order !== b.display_order) {
            return a.display_order - b.display_order;
        }
        return a.name.localeCompare(b.name);
    });

    return NextResponse.json(transformedProducts, { status: 200 });

  } catch (e: unknown) {
    console.error('/api/admin/stripe-products GET Error:', e);
    const error = e as Error | Stripe.errors.StripeError;
    return NextResponse.json({ 
      message: 'An unexpected error occurred fetching products.', 
      details: error.message 
    }, { status: error instanceof Stripe.errors.StripeError ? error.statusCode : 500 });
  }
}

interface UpdatePayload {
  productId: string;
  updates: {
    is_visible_on_subscribe_page: boolean;
    exclusivity_group: string; // Empty string means null
    display_order: number;
    one_time_purchase: boolean;
    tier: number | null; // Updated to allow null
  };
}

export async function PUT(request: Request) {
  try {
    const body = await request.json() as UpdatePayload;
    const { productId, updates } = body;
    
    console.log('PUT Request received:', { productId, updates });

    if (!productId || !updates) {
      return NextResponse.json({ message: 'Product ID and updates are required.' }, { status: 400 });
    }

    // Fetch the current product's metadata directly from Stripe
    let currentProductFromStripe: Stripe.Product;
    try {
      console.log(`[ADMIN_PRODUCTS_API PUT] Fetching product ${productId} from Stripe...`);
      currentProductFromStripe = await stripe.products.retrieve(productId);
      if (!currentProductFromStripe) { // Should not happen if retrieve succeeds without error
        return NextResponse.json({ message: `Product with ID ${productId} not found in Stripe.` }, { status: 404 });
      }
      console.log('[ADMIN_PRODUCTS_API PUT] Fetched product from Stripe:', { id: currentProductFromStripe.id, metadata: currentProductFromStripe.metadata });
    } catch (fetchErr: unknown) {
      const error = fetchErr as Error | Stripe.errors.StripeError;
      console.error(`[ADMIN_PRODUCTS_API PUT] Error fetching product ${productId} from Stripe:`, error.message);
      return NextResponse.json({ 
        message: `Error fetching product ${productId} from Stripe for update.`, 
        details: error.message 
      }, { status: error instanceof Stripe.errors.StripeError ? error.statusCode : 500 });
    }

    const currentMetadata = currentProductFromStripe.metadata || {};

    console.log('[ADMIN_PRODUCTS_API PUT] Current metadata state from Stripe:', currentMetadata);

    // Prepare new metadata values for Stripe
    const newMetadata = {
      ...currentMetadata, // Preserve existing metadata
      is_visible: updates.is_visible_on_subscribe_page ? "true" : "false",
      group: updates.exclusivity_group === '' || updates.exclusivity_group === null ? "null" : updates.exclusivity_group,
      display_order: String(updates.display_order),
      one_time_purchase: updates.one_time_purchase ? "true" : "false",
      tier: updates.tier === null ? null : String(updates.tier), // Pass null to Stripe SDK if tier is null
    };
    
    console.log('New metadata to be applied to Stripe:', newMetadata);

    try {
      // Update the product directly in Stripe
      const updatedProduct = await stripe.products.update(productId, {
        metadata: newMetadata
      });

      console.log('Stripe update response:', {
        id: updatedProduct.id,
        metadata: updatedProduct.metadata
      });

      // Verify the update by fetching the product from Stripe
      const verifiedProduct = await stripe.products.retrieve(productId);
      
      console.log('Verification from Stripe:', {
        id: verifiedProduct.id,
        metadata: verifiedProduct.metadata
      });
      
      return NextResponse.json({ 
        message: 'Product metadata updated successfully in Stripe.',
        product: {
          id: verifiedProduct.id,
          metadata: verifiedProduct.metadata
        }
      }, { status: 200 });
      
    } catch (stripeErr: unknown) {
      const error = stripeErr as Error | Stripe.errors.StripeError;
      console.error(`Error updating Stripe product:`, error);
      return NextResponse.json({ 
        message: 'Failed to update product metadata in Stripe.',
        details: error.message 
      }, { status: error instanceof Stripe.errors.StripeError ? error.statusCode : 500 });
    }

  } catch (e: unknown) {
    console.error('/api/admin/stripe-products PUT Error:', e);
    const error = e as Error;
    if (error instanceof SyntaxError) {
      return NextResponse.json({ message: 'Invalid JSON payload.' }, { status: 400 });
    }
    return NextResponse.json({ message: 'An unexpected error occurred.', details: error.message }, { status: 500 });
  }
}
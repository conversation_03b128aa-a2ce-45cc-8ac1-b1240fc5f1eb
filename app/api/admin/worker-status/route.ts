import { NextResponse } from 'next/server';
import { getWorkerPoolInstance } from '@/lib/background-processing/worker';

export async function GET() {
  try {
    const pool = getWorkerPoolInstance();
    const detailedStatus = pool.getDetailedStatus();
    
    // Determine overall health
    const healthStatus = determineHealthStatus(detailedStatus);
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      status: healthStatus.status,
      health: healthStatus.health,
      ...detailedStatus,
      recommendations: healthStatus.recommendations
    });
  } catch (error) {
    console.error(`[${new Date().toISOString()}] [WorkerStatus] Error getting worker status:`, error);
    return NextResponse.json({ 
      timestamp: new Date().toISOString(),
      error: 'Failed to get worker status',
      status: 'error',
      health: 'unhealthy'
    }, { status: 500 });
  }
}

function determineHealthStatus(status: any) {
  const recommendations: string[] = [];
  let health = 'healthy';
  let overallStatus = 'running';

  if (!status.isRunning) {
    health = 'unhealthy';
    overallStatus = 'stopped';
    recommendations.push('Worker pool is not running');
    return { status: overallStatus, health, recommendations };
  }

  // Check for stuck workers
  const stuckWorkers = status.workersStatus.filter((w: any) => 
    w.currentConversationId && w.currentConversationDuration > 600 // 10 minutes
  );
  
  if (stuckWorkers.length > 0) {
    health = 'degraded';
    recommendations.push(`${stuckWorkers.length} workers appear stuck (processing > 10min)`);
  }

  // Check error rate
  const totalProcessed = status.totalConversationsProcessed + status.totalConversationsFailed;
  if (totalProcessed > 0) {
    const errorRate = status.totalConversationsFailed / totalProcessed;
    if (errorRate > 0.2) { // More than 20% failure rate
      health = 'degraded';
      recommendations.push(`High error rate: ${Math.round(errorRate * 100)}%`);
    }
  }

  // Check for inactive workers
  const inactiveWorkers = status.workersStatus.filter((w: any) => 
    w.timeSinceLastActivity > 300 // 5 minutes
  );
  
  if (inactiveWorkers.length === status.totalWorkers) {
    health = 'degraded';
    recommendations.push('All workers have been inactive for > 5 minutes');
  }

  // Check recent errors
  if (status.totalErrors > status.totalWorkers * 10) { // More than 10 errors per worker
    health = 'degraded';
    recommendations.push('High number of recent errors detected');
  }

  if (recommendations.length === 0) {
    recommendations.push('All systems operating normally');
  }

  return { status: overallStatus, health, recommendations };
}
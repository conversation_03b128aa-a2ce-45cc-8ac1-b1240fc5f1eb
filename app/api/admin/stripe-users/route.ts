import { NextResponse } from 'next/server';
import { supabaseAdmin } from '../../../../lib/supabase/admin';
import Stripe from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-06-30.basil', // Ensure this is your desired API version
});

// Interface for one-time purchase details (simplified from profile.purchases)
interface OneTimePurchaseDisplayDetail {
  productId: string;
  productName?: string | null;
  // We no longer fetch payment intents directly for this admin view,
  // so amount, currency, created are not available from profile.purchases
}

// Updated interface for the API response structure
interface UserManagementData {
  userId: string;
  userEmail: string | undefined;
  userCreatedAt: string;
  isAdmin: boolean;
  stripeCustomerId?: string | null; // From profiles table
  activeSubscription?: { // Simplified: assumes one active subscription for display
    subscriptionId: string;
    status: string; // From profiles table or Stripe
    productName?: string | null; // From Stripe Product
    productId?: string | null; // From profiles table or Stripe
    billingInterval?: string | null; // From profiles table or Stripe
    currentPeriodEnd?: string | null; // From profiles table (ISO string)
    cancelAtPeriodEnd?: boolean | null; // From Stripe Subscription
  } | null;
  oneTimePurchases: OneTimePurchaseDisplayDetail[];
}

export async function GET() {
  console.log("API route /api/admin/stripe-users GET called - Refactored Logic (Profiles + Stripe API)");

  try {
    const { data: { users: authUsers }, error: usersError } = await supabaseAdmin.auth.admin.listUsers();
    if (usersError) throw usersError;
    if (!authUsers) return NextResponse.json({ error: "No auth users found" }, { status: 404 });

    const resultData: UserManagementData[] = [];

    for (const authUser of authUsers) {
      // Fetch profile for this user
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('id', authUser.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') { // PGRST116: 0 rows
        console.warn(`Error fetching profile for user ${authUser.id}: ${profileError.message}`);
        // Optionally decide if you want to include users without profiles or skip them
      }
      
      const userData: UserManagementData = {
        userId: authUser.id,
        userEmail: authUser.email,
        userCreatedAt: authUser.created_at,
        isAdmin: !!authUser.app_metadata?.is_admin,
        stripeCustomerId: profile?.stripe_customer_id || null,
        activeSubscription: null,
        oneTimePurchases: [],
      };

      // Populate active subscription details
      if (profile?.stripe_subscription_id && profile?.subscription_status === 'active') {
        try {
          const stripeSubscription = await stripe.subscriptions.retrieve(profile.stripe_subscription_id, {
            expand: ['items.data.price.product'],
          });

          if (stripeSubscription && stripeSubscription.status === 'active') {
            const firstItem = stripeSubscription.items.data[0];
            const price = firstItem?.price;
            const product = price?.product as Stripe.Product; // Product is expanded

            userData.activeSubscription = {
              subscriptionId: stripeSubscription.id,
              status: stripeSubscription.status, // Could also use profile.subscription_status
              productId: product?.id || profile.subscribed_product_id,
              productName: product?.name || 'Unknown Product',
              billingInterval: price?.recurring?.interval || profile.subscription_interval || null,
                          currentPeriodEnd: profile.subscription_current_period_end 
                                ? new Date(profile.subscription_current_period_end).toISOString() 
                                : stripeSubscription.items.data[0]?.current_period_end 
                                  ? new Date(stripeSubscription.items.data[0].current_period_end * 1000).toISOString()
                                  : null,
            cancelAtPeriodEnd: Boolean(stripeSubscription.cancel_at_period_end),
            };
          }
        } catch (subError: unknown) {
          const error = subError as Error;
          console.warn(`Error fetching Stripe subscription ${profile.stripe_subscription_id} for user ${authUser.id}: ${error.message}`);
          // Fallback to profile data if Stripe call fails but profile has info
          if (profile.subscribed_product_id) {
             userData.activeSubscription = {
                subscriptionId: profile.stripe_subscription_id,
                status: profile.subscription_status,
                productId: profile.subscribed_product_id,
                productName: 'Product name from profile (Stripe fetch failed)', // Placeholder
                billingInterval: profile.subscription_interval,
                currentPeriodEnd: profile.subscription_current_period_end ? new Date(profile.subscription_current_period_end).toISOString() : null,
                cancelAtPeriodEnd: null, // Not available in profile
             };
             // Attempt to get product name if only subscription fetch failed
             try {
                const product = await stripe.products.retrieve(profile.subscribed_product_id);
                if (userData.activeSubscription && product) userData.activeSubscription.productName = product.name;
             } catch (prodErr: unknown) {
                const error = prodErr as Error;
                console.warn(`Error fetching product ${profile.subscribed_product_id} (fallback): ${error.message}`);
             }
          }
        }
      } else if (profile?.stripe_subscription_id) { // Handle non-active states if needed from profile
        userData.activeSubscription = {
            subscriptionId: profile.stripe_subscription_id,
            status: profile.subscription_status || 'unknown',
            productId: profile.subscribed_product_id,
            productName: 'N/A (Subscription not active)',
            billingInterval: profile.subscription_interval,
            currentPeriodEnd: profile.subscription_current_period_end ? new Date(profile.subscription_current_period_end).toISOString() : null,
            cancelAtPeriodEnd: null, // Not in profile
        };
        if(profile.subscribed_product_id && profile.subscription_status !== 'active') {
            try {
                const product = await stripe.products.retrieve(profile.subscribed_product_id);
                if (userData.activeSubscription && product) userData.activeSubscription.productName = product.name;
             } catch (prodErr: unknown) {
                const error = prodErr as Error;
                console.warn(`Error fetching product ${profile.subscribed_product_id} for non-active sub: ${error.message}`);
             }
        }
      }

      // Populate one-time purchases from profile.purchases (array of product IDs)
      if (profile?.purchases && Array.isArray(profile.purchases)) {
        for (const purchasedProductId of profile.purchases) {
          if (typeof purchasedProductId === 'string') {
            try {
              const product = await stripe.products.retrieve(purchasedProductId);
              userData.oneTimePurchases.push({
                productId: purchasedProductId,
                productName: product.name || 'Unknown Product',
              });
            } catch (otpError: unknown) {
              const error = otpError as Error;
              console.warn(`Error fetching Stripe product ${purchasedProductId} for one-time purchase (user ${authUser.id}): ${error.message}`);
              userData.oneTimePurchases.push({
                productId: purchasedProductId,
                productName: 'Product (Stripe API error)',
              });
            }
          }
        }
      }
      resultData.push(userData);
    }

    return NextResponse.json(resultData);

  } catch (error: unknown) {
    const err = error as Error;
    console.error("General error in GET /api/admin/stripe-users:", err.message, err.stack);
    return NextResponse.json({ error: `An unexpected error occurred: ${err.message}` }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  console.log("API route /api/admin/stripe-users PUT called");

  try {
    const { userId, isAdmin } = await request.json();

    if (!userId || typeof isAdmin !== 'boolean') {
      return NextResponse.json({ error: 'Missing userId or isAdmin flag, or isAdmin is not a boolean' }, { status: 400 });
    }
    const { data: { user: existingUser }, error: fetchError } = await supabaseAdmin.auth.admin.getUserById(userId);

    if (fetchError) {
        console.error(`Error fetching user ${userId} for update:`, fetchError);
        return NextResponse.json({ error: `Failed to fetch user for update: ${fetchError.message}` }, { status: 500 });
    }
    if (!existingUser) {
        return NextResponse.json({ error: `User ${userId} not found.`}, { status: 404 });
    }
    const updatedAppMetadata = { 
      ...existingUser.app_metadata, 
      is_admin: isAdmin 
    };
    const { data, error } = await supabaseAdmin.auth.admin.updateUserById(
      userId,
      { app_metadata: updatedAppMetadata } 
    );
    if (error) {
      console.error(`Error updating user ${userId} admin status:`, error);
      return NextResponse.json({ error: `Failed to update user admin status: ${error.message}` }, { status: 500 });
    }
    // Ensure consistent naming if old interface was used in response
    return NextResponse.json({ message: 'User admin status updated successfully', user: data.user });
  } catch (error: unknown) {
    const err = error as Error;
    console.error("General error in PUT /api/admin/stripe-users:", err.message);
    if (err instanceof SyntaxError && err.message.includes('JSON')) {
        return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 });
    }
    return NextResponse.json({ error: `An unexpected error occurred: ${err.message}` }, { status: 500 });
  }
}
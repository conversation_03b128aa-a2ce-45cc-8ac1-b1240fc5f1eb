import { NextResponse } from 'next/server';
import { supabaseAdmin } from '../../../../lib/supabase/admin';
import <PERSON><PERSON> from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-06-30.basil',
});

interface SyncUserProfilePayload {
  userId: string;
}

interface ProfileDataToUpsert {
  id: string;
  email: string;
  stripe_customer_id?: string | null;
  stripe_subscription_id?: string | null;
  subscription_status?: string | null;
  subscribed_product_id?: string | null;
  subscription_price_id?: string | null;
  subscription_interval?: string | null;
  subscription_current_period_end?: string | null;
  purchases?: string[];
  updated_at?: string;
}

export async function POST(request: Request) {
  console.log("API route /api/admin/sync-user-profile POST called");
  const { userId } = await request.json() as SyncUserProfilePayload;

  if (!userId) {
    return NextResponse.json({ error: 'User ID is required.' }, { status: 400 });
  }

  try {
    // 1. Fetch Auth User
    const { data: { user: authUser }, error: authUserError } = await supabaseAdmin.auth.admin.getUserById(userId);
    if (authUserError) {
      console.error(`Error fetching auth user ${userId}:`, authUserError.message);
      return NextResponse.json({ error: `Failed to fetch auth user: ${authUserError.message}` }, { status: 500 });
    }
    if (!authUser) {
      return NextResponse.json({ error: `Auth user ${userId} not found.` }, { status: 404 });
    }
    if (!authUser.email) {
      return NextResponse.json({ error: `Auth user ${userId} does not have an email.` }, { status: 400 });
    }

    const userEmail = authUser.email;
    let profileDataToUpsert: ProfileDataToUpsert = { id: userId, email: userEmail };

    // 2. Fetch Stripe Customer by email
    let stripeCustomerId: string | null = null;
    try {
      const customers = await stripe.customers.list({ email: userEmail, limit: 1 });
      if (customers.data.length > 0) {
        stripeCustomerId = customers.data[0].id;
        profileDataToUpsert.stripe_customer_id = stripeCustomerId;
      } else {
        console.log(`No Stripe customer found for email: ${userEmail}`);
      }
    } catch (e: unknown) {
      const error = e as Error;
      console.error(`Error fetching Stripe customer for email ${userEmail}:`, error.message);
    }
    
    // 3. If Stripe Customer ID exists, fetch subscription and one-time purchases
    if (stripeCustomerId) {
      // Fetch Active Subscription
      try {
        const subscriptions = await stripe.subscriptions.list({
          customer: stripeCustomerId,
          status: 'active',
          limit: 1,
          expand: ['data.items.data.price'],
        });

        if (subscriptions.data.length > 0) {
          const activeSub = subscriptions.data[0];
          const firstItem = activeSub.items.data[0];
          const price = firstItem?.price;

          profileDataToUpsert = {
            ...profileDataToUpsert,
            stripe_subscription_id: activeSub.id,
            subscription_status: activeSub.status,
            subscribed_product_id: typeof price?.product === 'string' ? price.product : null,
            subscription_price_id: price?.id || null,
            subscription_interval: price?.recurring?.interval || null,
            subscription_current_period_end: firstItem?.current_period_end
              ? new Date(firstItem.current_period_end * 1000).toISOString()
              : null,
          };
        } else {
          // No active subscription, ensure these fields are nulled in the profile
          profileDataToUpsert = {
            ...profileDataToUpsert,
            stripe_subscription_id: null,
            subscription_status: null,
            subscribed_product_id: null,
            subscription_price_id: null,
            subscription_interval: null,
            subscription_current_period_end: null,
          };
        }
      } catch (e: unknown) {
        const error = e as Error;
        console.error(`Error fetching Stripe subscriptions for customer ${stripeCustomerId}:`, error.message);
        // Consider how to handle partial failures - e.g., still update profile with customer_id
      }

      // Fetch One-Time Purchases (Product IDs)
      try {
        const paymentIntents = await stripe.paymentIntents.list({
          customer: stripeCustomerId,
          limit: 100, // Adjust as needed
        });

        const oneTimeProductIds = new Set<string>();
        for (const pi of paymentIntents.data) {
          // Check for 'succeeded' status and product_id in metadata
          // and ensure it's not linked to the active subscription ID (if one exists)
          // to avoid double-counting subscription setup fees if they appear as PIs
          if (pi.status === 'succeeded' && pi.metadata?.product_id && 
              (!pi.metadata?.subscription_id || (profileDataToUpsert.stripe_subscription_id && pi.metadata.subscription_id !== profileDataToUpsert.stripe_subscription_id))) {
            if (pi.metadata.type === 'once') { // Rely on metadata type if available
                 oneTimeProductIds.add(pi.metadata.product_id);
            } else if (!('invoice' in pi)) { // Heuristic: one-time purchases might not have an invoice or a subscription_id
                 // This heuristic might need refinement based on how one-time purchases are created
                 // For now, let's be more specific if 'type: once' is in metadata
            }
          }
        }
        profileDataToUpsert.purchases = Array.from(oneTimeProductIds);
      } catch (e: unknown) {
        const error = e as Error;
        console.error(`Error fetching Stripe payment intents for customer ${stripeCustomerId}:`, error.message);
      }
    } else {
        // No Stripe customer ID means no subscriptions or Stripe OTPs to sync.
        // Nullify all Stripe-related fields for the profile.
        profileDataToUpsert = {
            ...profileDataToUpsert, // keep id and email
            stripe_customer_id: null,
            stripe_subscription_id: null,
            subscription_status: null,
            subscribed_product_id: null,
            subscription_price_id: null,
            subscription_interval: null,
            subscription_current_period_end: null,
            purchases: [], // Empty array for OTPs
        };
    }

    // 4. Upsert Profile Data
    // Ensure `updated_at` is set. The trigger should handle this, but explicit set is safer.
    profileDataToUpsert.updated_at = new Date().toISOString();

    const { data: upsertedProfile, error: upsertError } = await supabaseAdmin
      .from('profiles')
      .upsert(profileDataToUpsert, { onConflict: 'id' })
      .select()
      .single();

    if (upsertError) {
      console.error(`Error upserting profile for user ${userId}:`, upsertError.message);
      return NextResponse.json({ error: `Failed to upsert profile: ${upsertError.message}` }, { status: 500 });
    }

    return NextResponse.json({ 
      message: 'User profile synced successfully.',
      profile: upsertedProfile 
    });

  } catch (error: unknown) {
    const err = error as Error;
    console.error(`General error in POST /api/admin/sync-user-profile for user ${userId}:`, err.message, err.stack);
    return NextResponse.json({ error: `An unexpected error occurred: ${err.message}` }, { status: 500 });
  }
} 
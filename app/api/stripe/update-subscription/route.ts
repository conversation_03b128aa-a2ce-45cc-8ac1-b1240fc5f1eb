import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/admin';
import { supabaseAdmin } from '@/lib/supabase/admin';
import Stripe from 'stripe';

export async function POST(req: NextRequest) {
  try {
    const { 
      userId,
      subscriptionId, 
      subscriptionItemId, 
      newPriceId 
    } = await req.json();

    if (!userId || !subscriptionId || !subscriptionItemId || !newPriceId) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId, subscriptionId, subscriptionItemId, and newPriceId are required' },
        { status: 400 }
      );
    }

    const subscription: Stripe.Subscription = await stripe.subscriptions.retrieve(subscriptionId, { expand: ['schedule'] });
    const newPrice = await stripe.prices.retrieve(newPriceId, { expand: ['product'] });
    const oldPrice = subscription.items.data[0].price;

    const oldProduct = await stripe.products.retrieve(oldPrice.product as string);
    const newProduct = newPrice.product as Stripe.Product;

    const oldTier = oldProduct.metadata?.tier ? parseInt(oldProduct.metadata.tier, 10) : Infinity;
    const newTier = newProduct.metadata?.tier ? parseInt(newProduct.metadata.tier, 10) : Infinity;
    
    const intervalValues: { [key: string]: number } = { day: 1, week: 7, month: 30, year: 365 };
    const oldIntervalDays = oldPrice.recurring ? intervalValues[oldPrice.recurring.interval] * (oldPrice.recurring.interval_count || 1) : 0;
    const newIntervalDays = newPrice.recurring ? intervalValues[newPrice.recurring.interval] * (newPrice.recurring.interval_count || 1) : 0;

    const isDowngrade = newTier < oldTier || 
                        (newTier === oldTier && (newPrice.unit_amount || 0) < (oldPrice.unit_amount || 0)) ||
                        (newTier === oldTier && (newPrice.unit_amount || 0) === (oldPrice.unit_amount || 0) && newIntervalDays < oldIntervalDays);

    let responseData: Record<string, unknown>;

    if (subscription.schedule) {
      const scheduleId = typeof subscription.schedule === 'string' ? subscription.schedule : subscription.schedule.id;
      await stripe.subscriptionSchedules.release(scheduleId);
    }

    if (isDowngrade) {
      const schedule = await stripe.subscriptionSchedules.create({
        from_subscription: subscriptionId,
      });

      await stripe.subscriptionSchedules.update(schedule.id, {
        end_behavior: 'release',
        phases: [
          {
            items: [{ price: oldPrice.id, quantity: subscription.items.data[0].quantity }],
            start_date: subscription.items.data[0].current_period_start,
            end_date: subscription.items.data[0].current_period_end,
            proration_behavior: 'none',
          },
          {
            items: [{ price: newPriceId, quantity: subscription.items.data[0].quantity }],
            start_date: subscription.items.data[0].current_period_end,
            proration_behavior: 'none',
          }
        ],
      });
      
      const scheduledEffectiveDate = new Date(subscription.items.data[0].current_period_end * 1000);
      
      await supabaseAdmin
        .from('profiles')
        .update({
          scheduled_subscription_id: subscriptionId,
          scheduled_product_id: newProduct.id,
          scheduled_price_id: newPriceId,
          scheduled_interval: newPrice.recurring?.interval,
          scheduled_effective_date: scheduledEffectiveDate.toISOString(),
          scheduled_change_type: 'downgrade',
        })
        .eq('id', userId);

      responseData = {
        success: true,
        scheduled: true,
        effectiveDate: scheduledEffectiveDate.toISOString(),
        productName: newProduct.name,
        interval: newPrice.recurring?.interval,
      };

    } else {
      const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
        items: [
          {
            id: subscriptionItemId,
            price: newPriceId,
          }
        ],
        proration_behavior: 'always_invoice',
      });

      await supabaseAdmin
        .from('profiles')
        .update({
          scheduled_subscription_id: null,
          scheduled_product_id: null,
          scheduled_price_id: null,
          scheduled_interval: null,
          scheduled_effective_date: null,
          scheduled_change_type: null,
        })
        .eq('id', userId);

      responseData = {
        success: true,
        scheduled: false,
        subscriptionStatus: updatedSubscription.status,
        currentPeriodEnd: new Date(updatedSubscription.items.data[0].current_period_end * 1000).toISOString(),
        productName: newProduct.name,
        interval: newPrice.recurring?.interval,
      };
    }

    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('email')
      .eq('id', userId)
      .single();

    if (profile) {
      responseData.userEmail = profile.email;
    }

    return NextResponse.json(responseData);
  } catch (error: unknown) {
    console.error('[UPDATE_SUBSCRIPTION_API_ERROR]', error);
    return NextResponse.json(
      { error: 'Failed to update subscription: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
} 
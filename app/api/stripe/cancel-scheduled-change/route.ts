import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/admin';
import { supabaseAdmin } from '@/lib/supabase/admin';

export async function POST(req: NextRequest) {
  try {
    const { userId, subscriptionId } = await req.json();

    if (!userId || !subscriptionId) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId and subscriptionId are required' },
        { status: 400 }
      );
    }

    // Retrieve the subscription to find its schedule
    const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ['schedule'],
    });

    if (!subscription.schedule) {
      return NextResponse.json(
        { error: 'No scheduled change found for this subscription.' },
        { status: 404 }
      );
    }
    
    const scheduleId = typeof subscription.schedule === 'string' 
      ? subscription.schedule 
      : subscription.schedule.id;

    // Release the schedule to cancel the pending update
    await stripe.subscriptionSchedules.release(scheduleId);

    // Clear the scheduled change fields in the database
    const { error: dbError } = await supabaseAdmin
      .from('profiles')
      .update({
        scheduled_subscription_id: null,
        scheduled_product_id: null,
        scheduled_price_id: null,
        scheduled_interval: null,
        scheduled_effective_date: null,
        scheduled_change_type: null,
      })
      .eq('id', userId);

    if (dbError) {
      console.error('[CANCEL_SCHEDULE_API] Supabase error:', dbError.message);
      // Don't fail the request, but log the error
    }

    return NextResponse.json({
      success: true,
      message: 'Scheduled change has been successfully cancelled.',
    });

  } catch (error: unknown) {
    console.error('[CANCEL_SCHEDULE_API_ERROR]', error);
    return NextResponse.json(
      { error: 'Failed to cancel scheduled change: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
} 
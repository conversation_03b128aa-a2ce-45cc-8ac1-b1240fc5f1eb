import { NextResponse } from 'next/server';
import { createClient as createSupabaseServerClient } from '@/lib/supabase/server';
import { initializeSupabaseAdmin } from '@/lib/supabase/admin';
import Stripe from 'stripe';

// Initialize Stripe client
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-05-28.basil', // Use your desired API version
  typescript: true,
});

export async function POST(request: Request) {
  try {
    // 1. Authenticate the user making the request
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'You must be logged in to cancel a subscription.' },
        { status: 401 }
      );
    }

    // 2. Get the subscription ID from the request body
    const { subscriptionId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Bad Request', message: 'Subscription ID is required.' },
        { status: 400 }
      );
    }

    // 3. Verify the subscription belongs to the user by checking the profile
    const supabaseAdmin = initializeSupabaseAdmin();
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('stripe_subscription_id, subscribed_product_id, subscription_current_period_end')
      .eq('id', user.id)
      .eq('stripe_subscription_id', subscriptionId)
      .single();

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'Forbidden', message: 'Subscription not found or you do not own this subscription.' },
        { status: 403 }
      );
    }

    // 4. Get the subscription from Stripe to get current period end
    const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId);

    // 5. Cancel the subscription in Stripe (set to cancel at period end)
    const canceledSubscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });

    // 6. Update the profile in Supabase to reflect cancellation
    const { error: updateError } = await supabaseAdmin
      .from('profiles')
      .update({
        subscription_status: canceledSubscription.status,
        cancel_at_period_end: canceledSubscription.cancel_at_period_end,
      })
      .eq('id', user.id)
      .eq('stripe_subscription_id', subscriptionId);

    if (updateError) {
      console.error(`Error updating profile for user ${user.id} after Stripe cancellation:`, JSON.stringify(updateError));
      // Log the error but continue, as Stripe cancellation was successful
    }

    console.log(`Subscription ${subscriptionId} scheduled for cancellation at period end for user ${user.id}.`);

    // 7. Get product name for the response
    let productName = 'Your subscription';
    if (profile.subscribed_product_id) {
      try {
        const product = await stripe.products.retrieve(profile.subscribed_product_id);
        productName = product.name;
      } catch (productError) {
        console.error('Error fetching product name:', productError);
      }
    }

    // Get current period end from subscription items
    const currentPeriodEnd = stripeSubscription.items?.data[0]?.current_period_end 
      ? new Date(stripeSubscription.items.data[0].current_period_end * 1000)
      : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // fallback to 30 days from now

    return NextResponse.json({
      success: true,
      message: 'Subscription successfully scheduled for cancellation at the end of the current period.',
      subscription: {
        id: canceledSubscription.id,
        status: canceledSubscription.status,
        cancel_at_period_end: canceledSubscription.cancel_at_period_end,
        current_period_end: currentPeriodEnd.toISOString(),
        product_name: productName,
      }
    });

  } catch (error: unknown) {
    console.error('Error in cancel-subscription API:', error);
    // Differentiate Stripe errors from other errors if possible
    if (error && typeof error === 'object' && 'type' in error && typeof error.type === 'string' && error.type.startsWith('Stripe')) {
      const stripeError = error as Stripe.errors.StripeError;
      return NextResponse.json(
        { error: stripeError.type, message: stripeError.message },
        { status: stripeError.statusCode || 500 }
      );
    }
    return NextResponse.json(
      { error: 'Server Error', message: error instanceof Error ? error.message : 'An unexpected error occurred while canceling the subscription.' },
      { status: 500 }
    );
  }
} 
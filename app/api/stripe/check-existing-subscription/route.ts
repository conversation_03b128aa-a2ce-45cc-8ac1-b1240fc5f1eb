import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { stripe } from '@/lib/stripe/admin';
import Stripe from 'stripe';

// Helper to map interval strings to a sortable value
const intervalOrder = (interval: string | null | undefined): number => {
  if (!interval) return 0;
  switch (interval.toLowerCase()) {
    case 'day': return 1;
    case 'week': return 2;
    case 'month': return 3;
    case 'year': return 4;
    default: return 0;
  }
};

// Helper function to calculate a normalized monthly price for comparison
const getNormalizedMonthlyPrice = (price: Stripe.Price): number => {
  if (price.unit_amount === null || !price.recurring) {
    // This should not happen for valid recurring prices, return a high value or 0 to indicate error/non-comparable
    console.warn(`[PRICE_NORMALIZATION] Price ${price.id} is missing unit_amount or recurring info.`);
    return Infinity; 
  }

  const amount = price.unit_amount;
  const interval = price.recurring.interval;
  const intervalCount = price.recurring.interval_count || 1;

  switch (interval) {
    case 'day':
      return (amount / intervalCount) * 30; // Approximate days in a month
    case 'week':
      return (amount / intervalCount) * 4.333; // Approximate weeks in a month (52/12)
    case 'month':
      return amount / intervalCount;
    case 'year':
      return amount / (intervalCount * 12);
    default:
      console.warn(`[PRICE_NORMALIZATION] Unknown interval ${interval} for price ${price.id}.`);
      return Infinity; // Unknown interval, treat as non-comparable or highest price
  }
};

export async function POST(req: NextRequest) {
  try {
    const { userEmail, newPriceId } = await req.json();

    if (!userEmail || !newPriceId) {
      return NextResponse.json({ error: 'Missing userEmail or newPriceId' }, { status: 400 });
    }

    const { data: userProfile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('stripe_subscription_id, subscription_status')
      .eq('email', userEmail)
      .single();

    if (profileError || !userProfile) {
      console.warn(`Profile not found or error for email ${userEmail}:`, profileError?.message);
      return NextResponse.json({ 
        isUpgrade: false, 
        isDowngrade: false,
        reason: 'Profile not found or error fetching profile.' 
      });
    }

    const currentStripeSubscriptionId = userProfile.stripe_subscription_id;
    const currentSubscriptionStatus = userProfile.subscription_status;

    if (!currentStripeSubscriptionId || !['active', 'trialing'].includes(currentSubscriptionStatus?.toLowerCase() || '')) {
      return NextResponse.json({ 
        isUpgrade: false,
        isDowngrade: false,
        reason: 'No active current subscription found in profile.' 
      });
    }

    let currentStripeSub: Stripe.Subscription;
    try {
      currentStripeSub = await stripe.subscriptions.retrieve(currentStripeSubscriptionId, { 
        expand: ['items.data.price.product'] 
      });
      if (!currentStripeSub || (currentStripeSub.status !== 'active' && currentStripeSub.status !== 'trialing')) {
        return NextResponse.json({ 
          isUpgrade: false, 
          isDowngrade: false, 
          reason: 'Current Stripe subscription not active or not found.' 
        });
      }
    } catch (stripeError: unknown) {
      console.error(`Error fetching current subscription ${currentStripeSubscriptionId} from Stripe:`, 
        stripeError instanceof Error ? stripeError.message : 'Unknown error');
      return NextResponse.json({ 
        isUpgrade: false, 
        isDowngrade: false, 
        reason: 'Error fetching current subscription details from Stripe.' 
      });
    }
    
    const currentPriceItem = currentStripeSub.items.data[0]?.price;
    if (!currentPriceItem || typeof currentPriceItem.product === 'string' || !currentPriceItem.product || !currentPriceItem.recurring) {
        console.error('Current subscription price item, expanded product, or recurring info is invalid:', currentPriceItem);
        return NextResponse.json({ 
          isUpgrade: false, 
          isDowngrade: false,
          reason: 'Invalid current subscription price data, product details, or recurring info from Stripe.' 
        });
    }

    const currentProduct = currentPriceItem.product as Stripe.Product;
    const currentPlanInterval = currentPriceItem.recurring.interval; // Safe due to check above
    const actualCurrentProductId = currentProduct.id;
    const currentPeriodEndTimestamp = currentStripeSub.items.data[0].current_period_end;
    const currentPeriodEnd = new Date(currentPeriodEndTimestamp * 1000).toISOString();

    let newPrice: Stripe.Price;
    try {
      newPrice = await stripe.prices.retrieve(newPriceId, { expand: ['product'] });
    } catch (error: unknown) {
      console.error(`Error fetching new price ${newPriceId} from Stripe:`, 
        error instanceof Error ? error.message : 'Unknown error');
      return NextResponse.json({ error: 'Invalid newPriceId or error fetching price details.' }, { status: 400 });
    }

    // Handle one-time purchases explicitly
    if (newPrice.type === 'one_time') {
      console.log(`[CHECK_SUBSCRIPTION_API] New price ${newPriceId} is for a one-time purchase.`);
      if (!newPrice.product || typeof newPrice.product === 'string') {
        console.error('New price data for one-time purchase has missing or unexpanded product:', newPrice);
        return NextResponse.json({ error: 'Invalid product data for one-time purchase.' }, { status: 400 });
      }
      const oneTimeProduct = newPrice.product as Stripe.Product;
      return NextResponse.json({ 
        isUpgrade: false,
        isDowngrade: false,
        reason: 'Selected item is a one-time purchase.',
        newPriceId: newPriceId,
        newPlanInfo: {
            interval: 'one-time', 
            productId: oneTimeProduct.id,
            productName: oneTimeProduct.name,
        }
      });
    }

    // For recurring products:
    if (!newPrice.product || typeof newPrice.product === 'string' || !newPrice.recurring?.interval) {
      console.error('New price data or expanded product is invalid (for recurring product type):', newPrice);
      return NextResponse.json({ error: 'Invalid new price data from Stripe for recurring product (expanded product or interval missing).' }, { status: 400 });
    }
    const newProduct = newPrice.product as Stripe.Product;
    const newPlanInterval = newPrice.recurring.interval; // Safe due to check above
    const newStripeProductId = newProduct.id;

    // Prepare base plan info for both current and new plans
    const currentPlanInfoBase = {
        interval: currentPlanInterval,
        productId: actualCurrentProductId,
        productName: currentProduct.name,
        currentPeriodEnd: currentPeriodEnd
    };
    const newPlanInfoBase = {
        interval: newPlanInterval,
        productId: newStripeProductId,
        productName: newProduct.name
    };

    // Price comparison for upgrade/downgrade determination
    const currentNormalizedPrice = getNormalizedMonthlyPrice(currentPriceItem);
    const newNormalizedPrice = getNormalizedMonthlyPrice(newPrice);

    console.log(`[PRICE_COMPARISON] Current Product: ${currentProduct.name} (Normalized Price: ${currentNormalizedPrice}), New Product: ${newProduct.name} (Normalized Price: ${newNormalizedPrice})`);

    // First, check if products are the same
    if (actualCurrentProductId === newStripeProductId) {
        console.log(`[SAME_PRODUCT] Product: ${currentProduct.name}. Current Interval: ${currentPlanInterval}, New Interval: ${newPlanInterval}`);
        
        // For same product, compare intervals
        if (intervalOrder(newPlanInterval) > intervalOrder(currentPlanInterval)) {
            return NextResponse.json({
                isUpgrade: true,
                isDowngrade: false,
                currentSubscriptionId: currentStripeSubscriptionId,
                newPriceId: newPriceId,
                currentPlanInfo: currentPlanInfoBase,
                newPlanInfo: newPlanInfoBase,
                reason: 'Upgrade to a longer interval for the same product.'
            });
        } else if (intervalOrder(newPlanInterval) < intervalOrder(currentPlanInterval)) {
            return NextResponse.json({
                isUpgrade: false,
                isDowngrade: true,
                currentSubscriptionId: currentStripeSubscriptionId,
                newPriceId: newPriceId,
                currentPlanInfo: currentPlanInfoBase,
                newPlanInfo: newPlanInfoBase,
                subscriptionItemId: currentStripeSub.items.data[0].id,
                reason: 'Downgrade to a shorter interval for the same product.'
            });
        } else {
            return NextResponse.json({
                isUpgrade: false,
                isDowngrade: false,
                reason: 'Same product and interval. No change needed.'
            });
        }
    } else {
        // Different products, compare normalized prices
        if (newNormalizedPrice > currentNormalizedPrice && newNormalizedPrice !== Infinity && currentNormalizedPrice !== Infinity) {
            return NextResponse.json({
                isUpgrade: true,
                isDowngrade: false,
                currentSubscriptionId: currentStripeSubscriptionId,
                newPriceId: newPriceId,
                currentPlanInfo: currentPlanInfoBase,
                newPlanInfo: newPlanInfoBase,
                reason: `Upgrade to a higher value product: '${newProduct.name}' from '${currentProduct.name}'.`
            });
        } else if (newNormalizedPrice < currentNormalizedPrice && newNormalizedPrice !== Infinity && currentNormalizedPrice !== Infinity) {
            return NextResponse.json({
                isUpgrade: false,
                isDowngrade: true,
                currentSubscriptionId: currentStripeSubscriptionId,
                newPriceId: newPriceId,
                currentPlanInfo: currentPlanInfoBase,
                newPlanInfo: newPlanInfoBase,
                subscriptionItemId: currentStripeSub.items.data[0].id,
                reason: `Downgrade to a lower value product: '${newProduct.name}' from '${currentProduct.name}'.`
            });
        } else {
            // Same price or incomparable
            return NextResponse.json({
                isUpgrade: false,
                isDowngrade: false,
                reason: `Product change from '${currentProduct.name}' to '${newProduct.name}' is a lateral move (similar price value).`
            });
        }
    }

  } catch (error: unknown) {
    console.error('[CHECK_SUBSCRIPTION_API_ERROR]', error);
    return NextResponse.json({ 
      error: 'Internal server error: ' + (error instanceof Error ? error.message : 'Unknown error') 
    }, { status: 500 });
  }
} 
import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase/admin';

/**
 * Cleanup abandoned anonymous conversations
 * Can be called manually or via cron job
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, maxAgeHours = 48 } = body;

    console.log('Starting cleanup of anonymous conversations', { sessionId, maxAgeHours });

    let query = supabaseAdmin
      .from('conversations')
      .select('id, session_id, created_anonymously_at')
      .eq('is_anonymous', true)
      .is('user_id', null);

    // If sessionId provided, clean up specific session
    if (sessionId) {
      query = query.eq('session_id', sessionId);
    } else {
      // Otherwise, clean up conversations older than maxAgeHours
      const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000).toISOString();
      query = query.lt('created_anonymously_at', cutoffTime);
    }

    const { data: conversationsToDelete, error: findError } = await query;

    if (findError) {
      console.error('Error finding conversations to delete:', findError);
      return NextResponse.json({ error: 'Failed to find conversations' }, { status: 500 });
    }

    if (!conversationsToDelete || conversationsToDelete.length === 0) {
      console.log('No conversations found for cleanup');
      return NextResponse.json({ 
        message: 'No conversations to clean up',
        deleted: 0 
      });
    }

    console.log(`Found ${conversationsToDelete.length} conversations to delete`);

    const conversationIds = conversationsToDelete.map(conv => conv.id);

    // Delete messages first (foreign key constraint)
    const { error: messagesDeleteError } = await supabaseAdmin
      .from('messages')
      .delete()
      .in('conversation_id', conversationIds);

    if (messagesDeleteError) {
      console.error('Error deleting messages:', messagesDeleteError);
      return NextResponse.json({ error: 'Failed to delete messages' }, { status: 500 });
    }

    // Delete conversations
    const { error: conversationsDeleteError } = await supabaseAdmin
      .from('conversations')
      .delete()
      .in('id', conversationIds);

    if (conversationsDeleteError) {
      console.error('Error deleting conversations:', conversationsDeleteError);
      return NextResponse.json({ error: 'Failed to delete conversations' }, { status: 500 });
    }

    console.log(`Successfully deleted ${conversationIds.length} conversations and their messages`);

    return NextResponse.json({
      message: 'Conversations cleaned up successfully',
      deleted: conversationIds.length,
      conversationIds
    });

  } catch (error) {
    console.error('Error in cleanup-anonymous-conversations:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint for automated cleanup (can be called by cron jobs)
 */
export async function GET() {
  try {
    console.log('Running automated cleanup of abandoned anonymous conversations');

    // Clean up conversations older than 48 hours
    const maxAgeHours = 48;
    const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000).toISOString();

    const { data: conversationsToDelete, error: findError } = await supabaseAdmin
      .from('conversations')
      .select('id, session_id, created_anonymously_at')
      .eq('is_anonymous', true)
      .is('user_id', null)
      .lt('created_anonymously_at', cutoffTime);

    if (findError) {
      console.error('Error finding conversations to delete:', findError);
      return NextResponse.json({ error: 'Failed to find conversations' }, { status: 500 });
    }

    if (!conversationsToDelete || conversationsToDelete.length === 0) {
      console.log('No abandoned conversations found for cleanup');
      return NextResponse.json({ 
        message: 'No abandoned conversations to clean up',
        deleted: 0 
      });
    }

    console.log(`Found ${conversationsToDelete.length} abandoned conversations to delete`);

    const conversationIds = conversationsToDelete.map(conv => conv.id);

    // Delete messages first (foreign key constraint)
    const { error: messagesDeleteError } = await supabaseAdmin
      .from('messages')
      .delete()
      .in('conversation_id', conversationIds);

    if (messagesDeleteError) {
      console.error('Error deleting messages:', messagesDeleteError);
      return NextResponse.json({ error: 'Failed to delete messages' }, { status: 500 });
    }

    // Delete conversations
    const { error: conversationsDeleteError } = await supabaseAdmin
      .from('conversations')
      .delete()
      .in('id', conversationIds);

    if (conversationsDeleteError) {
      console.error('Error deleting conversations:', conversationsDeleteError);
      return NextResponse.json({ error: 'Failed to delete conversations' }, { status: 500 });
    }

    console.log(`Successfully deleted ${conversationIds.length} abandoned conversations`);

    return NextResponse.json({
      message: 'Abandoned conversations cleaned up successfully',
      deleted: conversationIds.length,
      maxAgeHours,
      cutoffTime
    });

  } catch (error) {
    console.error('Error in automated cleanup:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { getAuthContext, validateAccess, matchesConversationOwnership, createMessageData } from '@/lib/auth/apiAuth';

export async function POST(request: NextRequest) {
  console.log('Starting submit_message handler');
  try {
    const body = await request.json();
    console.log('Request body:', JSON.stringify(body, null, 2));
    const { conversationId, message } = body;

    if (!conversationId || !message) {
      console.log('Missing required fields:', { conversationId: !!conversationId, message: !!message });
      return NextResponse.json({ error: 'Conversation ID and message are required' }, { status: 400 });
    }

    // Get authentication context
    const authContext = await getAuthContext(request);

    // Validate access
    const accessValidation = validateAccess(authContext);
    if (!accessValidation.isValid) {
      console.log('Access validation failed:', accessValidation.error);
      return NextResponse.json({ error: accessValidation.error }, { status: 401 });
    }

    const userInfo = authContext.isAuthenticated
      ? `user: ${authContext.user?.id}`
      : `session: ${authContext.sessionId}`;
    console.log('Processing message for', userInfo);

    // First, resolve the actual Supabase conversation ID (handle both Supabase ID and Dify conversation ID)
    console.log('Resolving conversation ID:', conversationId);
    const { data: conversation, error: conversationError } = await supabaseAdmin
      .from('conversations')
      .select('id, dify_summary_id, user_id, session_id, is_anonymous')
      .or(`id.eq.${conversationId},dify_summary_id.eq.${conversationId}`)
      .single();

    if (conversationError || !conversation) {
      console.error('Error finding conversation:', conversationError);
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    // Check ownership
    if (!matchesConversationOwnership(conversation, authContext)) {
      console.log('Access denied for conversation ownership');
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const supabaseConversationId = conversation.id;
    console.log('Resolved to Supabase conversation ID:', supabaseConversationId);

    // Get the current message count to determine message_number
    console.log('Fetching message count for conversation:', supabaseConversationId);
    const { count, error: countError } = await supabaseAdmin
      .from('messages')
      .select('id', { count: 'exact', head: true })
      .eq('conversation_id', supabaseConversationId);

    if (countError) {
      console.error('Error getting message count:', countError);
      return NextResponse.json({ error: 'Failed to get message count' }, { status: 500 });
    }

    const nextMessageNumber = (count ?? 0) + 1;
    console.log('Next message number:', nextMessageNumber);

    // Create message data based on auth context
    const messageData = createMessageData(authContext, supabaseConversationId, nextMessageNumber, message, true);

    // Store the user message in the messages table using admin client to bypass RLS
    console.log('Inserting new message');
    const { data: newMessage, error: messageError } = await supabaseAdmin
      .from('messages')
      .insert(messageData)
      .select()
      .single();

    if (messageError) {
      console.error('Error storing message:', {
        error: messageError,
        details: messageError.details,
        hint: messageError.hint,
        code: messageError.code
      });
      return NextResponse.json({ 
        error: 'Failed to store message',
        details: messageError.message 
      }, { status: 500 });
    }

    console.log('Message stored successfully:', newMessage.id);

    // Update conversation's updated_at timestamp
    console.log('Updating conversation timestamp');
    const { error: updateError } = await supabaseAdmin
      .from('conversations')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', supabaseConversationId);

    if (updateError) {
      console.error('Error updating conversation timestamp:', updateError);
      // Don't return error here as the message was already stored successfully
    }

    console.log('Request completed successfully');
    const responseData = {
      success: true,
      messageId: newMessage.id,
    };
    return NextResponse.json(responseData);

  } catch (error: unknown) {
    console.error('Unhandled error in submit_message API:', {
      error: error,
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 
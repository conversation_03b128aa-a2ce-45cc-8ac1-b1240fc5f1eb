import { NextResponse } from 'next/server';
import axios from 'axios';
import { createClient } from '@/lib/supabase/server';

const DIFY_API_KEY = process.env.DIFY_API_KEY_SUMMARY;
const DIFY_BASE_URL = process.env.DIFY_BASE_URL || 'http://localhost/v1';

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  const requestData = await request.json();
  const idToRename = params.id;
  const newName = requestData.name;

  if (!newName) {
    return NextResponse.json({ 
      message: 'Name is required in the request body' 
    }, { status: 400 });
  }
  
  if (!idToRename) {
    return NextResponse.json({ 
      message: 'Conversation ID is required in the URL path' 
    }, { status: 400 });
  }
  
  try {
    const supabase = await createClient();
    const {
      data: { user }
    } = await supabase.auth.getUser();

    if (!user) {
        return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }
    const userId = `user-${user.id}`;

    // Find the conversation in Supabase by either its own ID or the Dify conversation ID
    const { data: conversation, error: fetchError } = await supabase
      .from('conversations')
      .select('id, dify_summary_id')
      .or(`id.eq.${idToRename},dify_summary_id.eq.${idToRename}`)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !conversation) {
      console.error(`Conversation not found for id: ${idToRename} for user ${user.id}`, fetchError);
      return NextResponse.json({ message: 'Conversation not found' }, { status: 404 });
    }

    const supabaseConversationId = conversation.id;
    const difyConversationId = conversation.dify_summary_id;

    console.log(`Renaming conversation, Supabase ID: ${supabaseConversationId}, Dify ID: ${difyConversationId}`);

    // 1. Update the title in public.conversations
    const { data: updatedConversation, error: updateError } = await supabase
      .from('conversations')
      .update({ title: newName, updated_at: new Date().toISOString() })
      .eq('id', supabaseConversationId)
      .select()
      .single();

    if (updateError) {
      console.error(`Error renaming conversation ${supabaseConversationId} in Supabase:`, updateError);
      return NextResponse.json({ message: 'Error renaming conversation in database', error: updateError.message }, { status: 500 });
    }
    console.log(`Successfully renamed conversation ${supabaseConversationId} in Supabase`);
    
    // 2. Rename in Dify if a Dify ID exists
    if (difyConversationId) {
      try {
        console.log(`Attempting to rename Dify conversation ${difyConversationId}`);
        await axios.post(
          `${DIFY_BASE_URL}/conversations/${difyConversationId}/name`,
          {
            name: newName,
            auto_generate: false,
            user: userId
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${DIFY_API_KEY}`
            }
          }
        );
        console.log(`Successfully sent rename request to Dify for conversation ${difyConversationId}`);
      } catch (difyError: unknown) {
        // Log the error from Dify but don't fail the overall request
        if (axios.isAxiosError(difyError)) {
          console.warn(`Failed to rename conversation ${difyConversationId} in Dify. The conversation is already renamed in our database.`, {
              message: difyError.message,
              code: difyError.code,
              status: difyError.response?.status,
              data: difyError.response?.data,
          });
        } else {
          const error = difyError as Error;
          console.warn(`Failed to rename conversation ${difyConversationId} in Dify. The conversation is already renamed in our database.`, {
              message: error.message,
          });
        }
      }
    } else {
        console.log(`No Dify conversation ID found for Supabase conversation ${supabaseConversationId}, skipping Dify rename.`);
    }

    return NextResponse.json(updatedConversation, { status: 200 });
    
  } catch (error: unknown) {
    console.error(`Error in rename process for conversation ${idToRename}:`, error);
    
    let errorMessage = 'Internal Server Error';
    
    if (axios.isAxiosError(error)) {
      errorMessage = error.response?.data?.message || error.message;
    } else {
      const err = error as Error;
      errorMessage = err.message;
    }
    
    return NextResponse.json({ 
      message: 'Error renaming conversation', 
      error: errorMessage 
    }, { status: 500 });
  }
} 
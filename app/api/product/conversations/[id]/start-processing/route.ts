import { NextRequest, NextResponse } from 'next/server';
import { startBackgroundWorker } from '@/lib/background-processing/worker';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { getAuthContext, validateAccess, matchesConversationOwnership } from '@/lib/auth/apiAuth';

function validateYouTubeUrl(url: string): string | null {
  const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/);
  return match ? match[1] : null;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: conversationId } = await params;

  try {
    const body = await request.json();
    const { youtubeUrl } = body;

    if (!youtubeUrl) {
      return NextResponse.json({ error: 'YouTube URL is required' }, { status: 400 });
    }

    const videoId = validateYouTubeUrl(youtubeUrl);
    if (!videoId) {
      return NextResponse.json({ error: 'Invalid YouTube URL format' }, { status: 400 });
    }

    // Get authentication context
    const authContext = await getAuthContext(request);

    // Validate access
    const accessValidation = validateAccess(authContext);
    if (!accessValidation.isValid) {
      return NextResponse.json({ error: accessValidation.error }, { status: 401 });
    }

    // Verify conversation ownership
    const { data: conversation, error: convError } = await supabaseAdmin
      .from('conversations')
      .select('id, processing_status, user_id, session_id, is_anonymous')
      .eq('id', conversationId)
      .single();

    if (convError || !conversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    // Check ownership
    if (!matchesConversationOwnership(conversation, authContext)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if already processing
    if (conversation.processing_status === 'pending' || 
        conversation.processing_status === 'fetching' ||
        conversation.processing_status === 'optimizing' ||
        conversation.processing_status === 'summarizing') {
      return NextResponse.json({ 
        error: 'Conversation is already being processed',
        status: conversation.processing_status 
      }, { status: 409 });
    }

    // Update conversation to pending status and set youtube_video_id
    const { error: updateError } = await supabaseAdmin
      .from('conversations')
      .update({
        processing_status: 'pending',
        youtube_video_id: videoId,
        processing_started_at: new Date().toISOString(),
        processing_error: null,
        processing_progress: {
          currentStep: 'pending',
          transcriptProgress: 0,
          optimizationProgress: 0,
          summaryProgress: 0,
          transcriptLength: 0,
          currentStepText: 'Queued for processing...'
        }
      })
      .eq('id', conversationId);

    if (updateError) {
      console.error('Error updating conversation:', updateError);
      return NextResponse.json({ error: 'Failed to start processing' }, { status: 500 });
    }

    // Start the background worker (if not already running)
    startBackgroundWorker();

    return NextResponse.json({ 
      success: true, 
      message: 'Processing started',
      conversationId,
      status: 'pending'
    });

  } catch (error) {
    console.error('Error starting processing:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 
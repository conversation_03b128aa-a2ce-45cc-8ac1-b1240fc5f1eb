import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: conversationId } = await params;

  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Reset the processing status to null for the conversation
    const { error } = await supabase
      .from('conversations')
      .update({ 
        processing_status: null,
        processing_error: null,
        processing_progress: null,
        current_step_text: null
      })
      .eq('id', conversationId)
      .eq('user_id', user.id);

    if (error) {
      console.error('Error resetting conversation status:', error);
      return NextResponse.json({ error: 'Failed to reset conversation status' }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true,
      message: 'Conversation status reset successfully'
    });

  } catch (error) {
    console.error('Error resetting conversation status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { getAuthContext, validateAccess, getConversationFilter, createConversationData } from '@/lib/auth/apiAuth';

export async function GET(request: NextRequest) {
  try {
    // Get authentication context
    const authContext = await getAuthContext(request);

    // Validate access
    const accessValidation = validateAccess(authContext);
    if (!accessValidation.isValid) {
      return NextResponse.json(
        { message: accessValidation.error },
        { status: 401 }
      );
    }

    // Get conversation filter based on auth context
    const filter = getConversationFilter(authContext);

    // Fetch conversations from Supabase using admin client
    let query = supabaseAdmin
      .from('conversations')
      .select('id, title, created_at, updated_at, is_pinned, dify_summary_id, processing_status, youtube_video_id, is_anonymous')
      .order('updated_at', { ascending: false })
      .limit(20);

    // Apply filter based on auth context
    if (filter.user_id) {
      query = query.eq('user_id', filter.user_id);
    } else if (filter.session_id) {
      query = query.eq('session_id', filter.session_id).eq('is_anonymous', true);
    }

    const { data: conversations, error } = await query;

    if (error) {
      console.error('Error fetching conversations:', error);
      return NextResponse.json(
        { message: 'Error fetching conversations', error: error.message }, 
        { status: 500 }
      );
    }

    // Transform the data to match the expected format (similar to Dify response)
    const transformedData = {
      data: conversations?.map(conv => ({
        id: conv.dify_summary_id || conv.id, // Use dify_summary_id if available, fallback to id
        name: conv.title || 'Processing…',
        created_at: Math.floor(new Date(conv.created_at).getTime() / 1000),
        updated_at: Math.floor(new Date(conv.updated_at).getTime() / 1000),
        processing_status: conv.processing_status,
        youtube_video_id: conv.youtube_video_id,
        supabase_id: conv.id, // Include the Supabase ID for internal reference
      })) || []
    };

    return NextResponse.json(transformedData);
    
  } catch (error: unknown) {
    console.error('Error fetching conversations:', error);
    
    const err = error as Error;
    return NextResponse.json(
      { message: 'Error fetching conversations', error: err.message }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authentication context
    const authContext = await getAuthContext(request);

    // Validate access
    const accessValidation = validateAccess(authContext);
    if (!accessValidation.isValid) {
      return NextResponse.json(
        { message: accessValidation.error },
        { status: 401 }
      );
    }

    const userInfo = authContext.isAuthenticated
      ? `user: ${authContext.user?.id}`
      : `session: ${authContext.sessionId}`;
    console.log('Creating new conversation for', userInfo);

    // Create conversation data based on auth context
    const conversationData = createConversationData(authContext);

    // Create a new conversation in Supabase using admin client
    const { data: newConversation, error } = await supabaseAdmin
      .from('conversations')
      .insert(conversationData)
      .select('id, title, created_at, updated_at')
      .single();

    if (error) {
      console.error('Error creating conversation:', error);
      return NextResponse.json(
        { message: 'Error creating conversation', error: error.message }, 
        { status: 500 }
      );
    }
    
    console.log('Successfully created conversation:', newConversation);
    return NextResponse.json({ 
      conversation_id: newConversation.id,
      title: newConversation.title,
      created_at: newConversation.created_at,
      updated_at: newConversation.updated_at
    }, { status: 201 });
    
  } catch (error: unknown) {
    console.error('Error creating conversation:', error);
    
    const err = error as Error;
    return NextResponse.json({ 
      message: 'Error creating conversation', 
      error: err.message 
    }, { status: 500 });
  }
}
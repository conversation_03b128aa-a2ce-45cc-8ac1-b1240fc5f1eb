import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { getAuthContext, validateAccess, matchesConversationOwnership, createMessageData } from '@/lib/auth/apiAuth';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const conversationId = searchParams.get('conversation_id');

  if (!conversationId) {
    return NextResponse.json({ message: 'Conversation ID is required' }, { status: 400 });
  }

  try {
    // Get authentication context
    const authContext = await getAuthContext(request);

    // Validate access
    const accessValidation = validateAccess(authContext);
    if (!accessValidation.isValid) {
      return NextResponse.json(
        { message: accessValidation.error },
        { status: 401 }
      );
    }

    const userInfo = authContext.isAuthenticated
      ? `user: ${authContext.user?.id}`
      : `session: ${authContext.sessionId}`;
    console.log(`Fetching messages for conversation: ${conversationId}, ${userInfo}`);

    // First, verify the user owns this conversation
    const { data: conversation, error: convError } = await supabaseAdmin
      .from('conversations')
      .select('id, user_id, session_id, is_anonymous')
      .eq('id', conversationId)
      .single();

    if (convError || !conversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    // Check ownership
    if (!matchesConversationOwnership(conversation, authContext)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Fetch messages from Supabase messages table using admin client
    const { data: messages, error } = await supabaseAdmin
      .from('messages')
      .select('id, content, message_number, created_at, user_message')
      .eq('conversation_id', conversationId)
      .order('message_number', { ascending: true });

    if (error) {
      console.error('Error fetching messages:', error);
      return NextResponse.json(
        { message: 'Error fetching messages', error: error.message }, 
        { status: 500 }
      );
    }

    // Transform the data to match the expected format from the frontend
    const transformedData = {
      data: messages?.map(msg => ({
        id: msg.id,
        query: msg.user_message ? msg.content : null, // Only set query for user messages
        answer: !msg.user_message ? msg.content : null, // Only set answer for AI messages
        created_at: Math.floor(new Date(msg.created_at).getTime() / 1000),
        message_number: msg.message_number,
        user_message: msg.user_message
      })) || []
    };

    console.log(`Fetched ${transformedData.data.length} messages`);
    return NextResponse.json(transformedData);
    
  } catch (error: unknown) {
    console.error('Error fetching messages:', error);
    
    return NextResponse.json(
      { message: 'Error fetching messages', error: error instanceof Error ? error.message : 'Unknown error' }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { conversationId, content } = body;

    if (!conversationId || !content) {
      return NextResponse.json({ error: 'Conversation ID and content are required' }, { status: 400 });
    }

    // Get authentication context
    const authContext = await getAuthContext(request);

    // Validate access
    const accessValidation = validateAccess(authContext);
    if (!accessValidation.isValid) {
      return NextResponse.json({ error: accessValidation.error }, { status: 401 });
    }

    // First, resolve the actual Supabase conversation ID
    const { data: conversation, error: conversationError } = await supabaseAdmin
      .from('conversations')
      .select('id, dify_summary_id, user_id, session_id, is_anonymous')
      .or(`id.eq.${conversationId},dify_summary_id.eq.${conversationId}`)
      .single();

    if (conversationError || !conversation) {
      console.error('Error finding conversation:', conversationError);
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    // Check ownership
    if (!matchesConversationOwnership(conversation, authContext)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const supabaseConversationId = conversation.id;

    // Get the current message count to determine message_number
    const { count, error: countError } = await supabaseAdmin
      .from('messages')
      .select('id', { count: 'exact', head: true })
      .eq('conversation_id', supabaseConversationId);

    if (countError) {
      console.error('Error getting message count:', countError);
      return NextResponse.json({ error: 'Failed to get message count' }, { status: 500 });
    }

    const nextMessageNumber = (count ?? 0) + 1;

    // Create message data based on auth context
    const messageData = createMessageData(authContext, supabaseConversationId, nextMessageNumber, content, false);

    // Store the AI message in the messages table using admin client
    const { data: newMessage, error: messageError } = await supabaseAdmin
      .from('messages')
      .insert(messageData)
      .select()
      .single();

    if (messageError) {
      console.error('Error storing AI message:', messageError);
      return NextResponse.json({ 
        error: 'Failed to store AI message',
        details: messageError.message 
      }, { status: 500 });
    }

    // Update conversation's updated_at timestamp
    const { error: updateError } = await supabaseAdmin
      .from('conversations')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', supabaseConversationId);

    if (updateError) {
      console.error('Error updating conversation timestamp:', updateError);
    }

    return NextResponse.json({
      success: true,
      messageId: newMessage.id,
    });

  } catch (error: unknown) {
    console.error('Error in messages POST API:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 
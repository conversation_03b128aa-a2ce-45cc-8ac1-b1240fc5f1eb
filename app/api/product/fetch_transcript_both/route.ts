import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { getAuthContext, validateAccess, matchesConversationOwnership } from '@/lib/auth/apiAuth';

export const dynamic = 'force-dynamic';

export async function POST(req: NextRequest) {
  console.log('Starting fetch_transcript_both handler');
  
  try {
    const body = await req.json();
    
    const { conversationId } = body;
    
    if (!conversationId) {
      return NextResponse.json({ 
        success: false, 
        error: 'Missing required parameter: conversationId' 
      }, { status: 400 });
    }

    // Get authentication context
    const authContext = await getAuthContext(req);

    // Validate access
    const accessValidation = validateAccess(authContext);
    if (!accessValidation.isValid) {
      return NextResponse.json({
        success: false,
        error: accessValidation.error
      }, { status: 401 });
    }

    // Fetch the conversation with both transcript fields
    // Handle both Supabase ID and Dify conversation ID
    const { data: conversation, error: conversationError } = await supabaseAdmin
      .from('conversations')
      .select('transcript, optimized_transcript, user_id, session_id, is_anonymous')
      .or(`id.eq.${conversationId},dify_summary_id.eq.${conversationId}`)
      .single();

    if (conversationError || !conversation) {
      return NextResponse.json({
        success: false,
        error: `Conversation ${conversationId} not found`
      }, { status: 404 });
    }

    // Check ownership
    if (!matchesConversationOwnership(conversation, authContext)) {
      return NextResponse.json({
        success: false,
        error: 'Access denied'
      }, { status: 403 });
    }

    // Extract transcript content from the combined structure
    let rawTranscript = null;
    if (conversation.transcript) {
      // Check if it's the new combined structure
      if (conversation.transcript.transcript && conversation.transcript.transcript.content) {
        rawTranscript = conversation.transcript.transcript;
      } else {
        // Fallback for old structure
        rawTranscript = conversation.transcript;
      }
    }

    const optimizedTranscript = conversation.optimized_transcript;
    const hasOptimized = !!optimizedTranscript;

    return NextResponse.json({
      success: true,
      rawTranscript,
      optimizedTranscript,
      hasOptimized
    });

  } catch (error) {
    console.error('Error in fetch_transcript_both:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 });
  }
} 
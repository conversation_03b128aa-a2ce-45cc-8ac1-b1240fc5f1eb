import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { getAuthContext, validateAccess } from '@/lib/auth/apiAuth';

/**
 * Transfer anonymous conversations to authenticated user account
 * Called after user login/signup
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId } = body;

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Get authentication context
    const authContext = await getAuthContext(request);
    
    // Must be authenticated to transfer conversations
    if (!authContext.isAuthenticated || !authContext.user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const userId = authContext.user.id;
    console.log(`Transferring anonymous conversations from session ${sessionId} to user ${userId}`);

    // Find all anonymous conversations for this session
    const { data: anonymousConversations, error: findError } = await supabaseAdmin
      .from('conversations')
      .select('id, title, created_at, created_anonymously_at')
      .eq('session_id', sessionId)
      .eq('is_anonymous', true)
      .is('user_id', null);

    if (findError) {
      console.error('Error finding anonymous conversations:', findError);
      return NextResponse.json({ error: 'Failed to find conversations' }, { status: 500 });
    }

    if (!anonymousConversations || anonymousConversations.length === 0) {
      console.log('No anonymous conversations found for session:', sessionId);
      return NextResponse.json({ 
        message: 'No conversations to transfer',
        transferred: 0 
      });
    }

    console.log(`Found ${anonymousConversations.length} anonymous conversations to transfer`);

    // Transfer conversations to user account
    const conversationIds = anonymousConversations.map(conv => conv.id);
    
    const { data: updatedConversations, error: updateError } = await supabaseAdmin
      .from('conversations')
      .update({
        user_id: userId,
        session_id: null,
        is_anonymous: false,
        transferred_to_user_at: new Date().toISOString()
      })
      .in('id', conversationIds)
      .select('id, title');

    if (updateError) {
      console.error('Error transferring conversations:', updateError);
      return NextResponse.json({ error: 'Failed to transfer conversations' }, { status: 500 });
    }

    // Update messages to associate with user (for messages that have user_id)
    // Note: Anonymous messages might not have user_id, which is fine
    const { error: messagesUpdateError } = await supabaseAdmin
      .from('messages')
      .update({ user_id: userId })
      .in('conversation_id', conversationIds)
      .is('user_id', null);

    if (messagesUpdateError) {
      console.warn('Warning: Failed to update some messages:', messagesUpdateError);
      // Don't fail the whole operation for this
    }

    console.log(`Successfully transferred ${updatedConversations?.length || 0} conversations`);

    return NextResponse.json({
      message: 'Conversations transferred successfully',
      transferred: updatedConversations?.length || 0,
      conversations: updatedConversations
    });

  } catch (error) {
    console.error('Error in transfer-anonymous-conversations:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

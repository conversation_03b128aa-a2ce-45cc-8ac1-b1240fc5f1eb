.root {
  background-color: var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-xxs);
  outline: 1px solid var(--mantine-color-gray-4);
  --progress-radius: 3px;
  padding: 1px;
}

.section {
  border-top: 1px solid var(--mantine-color-gray-4);
  border-bottom: 1px solid var(--mantine-color-gray-4);
  border-left: 1px solid var(--mantine-color-gray-4);
  transition: all 0.5s ease;
}

.section:first-child {
  border-left: 1px solid var(--mantine-color-gray-4);
}

.section:last-child {
  border-right: 1px solid var(--mantine-color-gray-4);
}

.section[data-completed="true"] {
  background-color: var(--mantine-color-green-4);
  border-color: var(--mantine-color-green-6);
}

.section[data-completed="false"] {
  background-color: var(--mantine-color-blue-4);
  border-color: var(--mantine-color-blue-6);
}

.label {
  font-size: var(--mantine-font-size-xs);
  color: var(--mantine-color-green-6);
  text-transform: uppercase;
  font-weight: 600 !important;
}

.label[data-completed="true"] {
  color: var(--mantine-color-green-6);
}

.label[data-completed="false"] {
  color: var(--mantine-color-blue-6);
}

.progressContainer[data-completed="true"] {
  margin-top: 100px;
  transition: margin-top 0.5s ease 1s;
}
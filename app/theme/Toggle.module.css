.root {
  border-radius: var(--mantine-radius-xs);
}

.body {
  display: flex;
  align-items: center;
}

.track {
  cursor: pointer;
  transition: background-color 150ms ease, border-color 150ms ease;
  border-radius: var(--mantine-radius-xs);
  outline: 1px solid rgba(0, 0, 0, 0.05);
  outline-offset: -1px;
}

.track[data-checked] {
  display: inherit;
}

.thumb {
  transition: transform 150ms ease;
  border-radius: 5px;
  outline: 1px solid rgba(0, 0, 0, 0.05);
}


.thumb:before {
  background-color: transparent;
}

.track[data-checked] .thumb {
  display: inherit;
}


.label {
  cursor: pointer;
  text-transform: uppercase;
  font-weight: 600;
  margin-bottom: -.1em;
}

.description {
  font-size: var(--mantine-font-size-xs);
}

.error {
  font-size: var(--mantine-font-size-xs);
  color: var(--mantine-color-red-6);
  margin-top: 2px;
}

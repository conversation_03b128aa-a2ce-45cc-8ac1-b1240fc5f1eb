.container {
  width: 100%;
  padding: 0 !important;
  height: 100%;
  display: flex;
  margin: 0 !important;
  max-width: 100% !important;
}

.root {
  border: 1px solid var(--mantine-color-gray-3);
  padding: 0 !important;
  display: flex;
  flex: 1;
  min-height: 0; /* Important for nested flex containers */
}

.list {
  position: relative;
  gap: var(--mantine-spacing-xs);
  padding: var(--mantine-spacing-sm);
  width: 12em;
  background-color: var(--mantine-color-gray-0);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  @mixin dark {
    background-color: var(--mantine-color-dark-7);
  }
}

.list[data-orientation="vertical"] {
  border-right: 1px solid var(--mantine-color-gray-3);
}

.list[data-orientation="horizontal"] {
  border-bottom: 1px solid var(--mantine-color-gray-3);
}

.indicator {
  background-color: var(--mantine-color-black);
  border-radius: 6px;
  height: 100%;
  z-index: 0;
  border: none;
  outline: none;
  margin: 1px 0 0 1px;
  outline-offset: -3px;
  outline: 3px solid var(--mantine-color-gray-1);
  
  @mixin dark {
    background-color: var(--mantine-color-blue-8);
  }
}

.tab {
  border-radius: var(--mantine-radius-xxs);
  transition: color 100ms ease, background-color 500ms ease;
  height: 2.75em;
  opacity: 1;
  padding: 0.5em 1em;
  background-color: var(--mantine-color-white);
  color: var(--mantine-color-black);
  box-shadow: var(--mantine-shadow-sm);
  border: 1px solid var(--mantine-color-gray-3);
  z-index: 1;
  width: 100%;

  &:hover {
    color: var(--mantine-color-gray-9);
    background-color: var(--mantine-color-gray-1);
    
    @mixin dark {
      color: var(--mantine-color-gray-0);
      background-color: var(--mantine-color-dark-5);
    }
  }

  &[data-active] {
    color: var(--mantine-color-white);
    background-color: transparent;
    font-weight: 600;
  }

  @mixin dark {
    color: var(--mantine-color-dark-2);

    &[data-active] {
      color: var(--mantine-color-white);
    }
  }
}

.panel {
  padding: var(--mantine-spacing-xl);
  background-color: var(--mantine-color-white);
  width: 100%;
  height: 100%;
  flex: 1;
  overflow-y: auto;
  min-height: 0; /* Important for flex child overflow */
  
  @mixin dark {
    background-color: var(--mantine-color-dark-7);
    border-color: var(--mantine-color-dark-4);
  }
}

/* Add styles for the root tabs element */
.tabs {
  display: flex;
  width: 100%;
  height: 100%;
  box-shadow: none !important;
  min-height: 0; /* Important for flex child overflow */
}

.hidden {
  opacity: 0;
  pointer-events: none;
}

.visible {
  opacity: 1;
  pointer-events: auto;
}
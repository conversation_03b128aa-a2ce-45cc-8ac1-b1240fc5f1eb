.button {
  border-radius: var(--mantine-radius-xxs);
  transition: color 100ms ease, background-color 500ms ease;
  height: 2.75em;
  opacity: 1;
  position: relative;
  z-index: 0;
  padding: 0.5em 1em;
  outline: 1px solid var(--mantine-color-gray-3);
  background-color: var(--mantine-color-white);
  color: var(--mantine-color-black);
  box-shadow: var(--mantine-shadow-sm);
  .label {
    font-weight: 500;
  }
}

.button[data-variant="outline"] {
  border: none;
}

.button:active {
  transform: translateY(0px);
}

.button:focus {
  outline: 1px solid var(--mantine-color-black);
  outline-offset: 0;
  box-shadow: none;
}

/* Disabled state */
.button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Color Variants */
.button[custom-color="white"] {
  outline-color: var(--mantine-color-black);
  transition: background-color 500ms ease;
  color: var(--mantine-color-black);
}

.button[custom-color="white"]:not([disabled]):hover {
  background: var(--mantine-color-black);
  color: var(--mantine-color-white);
  border-color: var(--mantine-color-black);
}

.button[custom-color="black"] {
  background: linear-gradient(to bottom, var(--mantine-color-gray-9), var(--mantine-color-black)), var(--mantine-color-black);
  outline-color: var(--mantine-color-white);
  outline-offset: 0px;
  border: 2px solid var(--mantine-color-black);
  translate: 0 -.5px;
  color: var(--mantine-color-white);
}

.button[custom-color="black"]:not([disabled]):hover {
  background: linear-gradient(to bottom, var(--mantine-color-gray-0), var(--mantine-color-white));
  color: var(--mantine-color-black);
  outline-color: var(--mantine-color-black);
}

.button[custom-color="success"] {
  background: var(--mantine-color-green-4);
}

.button[custom-color="success"]:hover {
  background: var(--mantine-color-green-9);
}

.button[custom-color="success"]:focus {
  outline: 5ch solid var(--mantine-color-black);
}

.button[custom-color="success-disabled"] {
  background: hsl(var(--su) / var(--tw-bg-opacity, 0.7));
  color: hsl(var(--suc) / var(--tw-text-opacity, 0.7));
  cursor: not-allowed;
}

.button[custom-color="success"]:not([active]):focus {
  outline: 5px solid var(--mantine-color-black);
}

/* Variants */
.button[data-variant="link"] {
  background: transparent;
  outline: none;
  border: none;
  padding: .15em;
  height: auto;
  box-shadow: none;
  border-radius: .15em;
  .label {
    font-weight: normal;
    color: var(--mantine-color-black);
    font-size: var(--mantine-font-size-sm);
  }
}

.button[data-variant="link"]:focus {
  outline: 3px solid var(--mantine-color-black) !important;
}

.button[data-variant="navlink"] {
  background: transparent;
  outline: none;
  padding: 0.75em 0.9em;
  font-weight: 700;
  height: auto;
  box-shadow: none;
  border-radius: var(--mantine-radius-xxs);
  .label {
    color: var(--mantine-color-gray-8);
  }
}

.button[data-variant="navlink"]:hover {
  outline: 2px solid var(--mantine-color-gray-8) !important;
  background-color: var(--mantine-color-gray-8);
  color: var(--mantine-color-white);
  .label {
    color: var(--mantine-color-white);
  }
}

.button[data-variant="ghost"] {
  background: transparent;
  color: var(--mantine-color-black);
  outline: 1px solid var(--mantine-color-gray-3);
  outline-offset: 0px;
}

.button[data-variant="ghost"]:hover {
  background: var(--mantine-color-primary-7);
  color: var(--mantine-color-white);
  outline: 1px solid var(--mantine-color-primary-7);
  outline-offset: 0px;
}

/* Sidebar variant */
.button[data-variant="sidebar"] {
  outline: none;
  box-shadow: none;
  padding: .5em;
  font-size: var(--mantine-font-size-md);
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background-color: transparent;
  transition: border-radius .25s ease, background-color .25s ease, border .25s ease;
  .label {
    font-weight: 500;
    color: var(--mantine-color-gray-7);
    font-size: var(--mantine-font-size-sm);
    text-align: left;
  }
}


.button[data-variant="sidebar"]:hover {
  background-color: var(--mantine-color-gray-1);
  color: var(--mantine-color-gray-7);
  border-radius: var(--mantine-radius-xxs);
  background: var(--mantine-color-gray-1);
  border: 1px solid var(--mantine-color-gray-3);
}

.button[data-variant="sidebar"]:active {
  background-color: var(--mantine-color-gray-2);
}

.button[data-variant="sidebar"]:focus {
  outline: none;
}
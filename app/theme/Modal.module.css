.header {
  background-color: var(--mantine-color-white);
  padding: var(--mantine-spacing-md) var(--mantine-spacing-lg);
  margin: 0;
}

.title {
  font-weight: 500;
  font-size: var(--mantine-font-size-lg);
  color: var(--mantine-color-dark-9);
  margin-bottom: var(--mantine-spacing-sm);
}

.close {
  color: var(--mantine-color-gray-5);
  transition: color 200ms ease;
}

.close:hover {
  color: var(--mantine-color-dark-9);
  background-color: transparent;
}

.body {
  padding: var(--mantine-spacing-sm);
}

.overlay {
  background-color: rgba(1, 1, 1, 0.35);
  backdrop-filter: blur(2px);
}


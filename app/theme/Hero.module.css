.root {
    margin: 5rem auto;
    align-items: flex-start;
    gap: var(--mantine-spacing-xxl);
}

  
  .content {
    width: 66%;
    @media (max-width: var(--mantine-breakpoint-md)) {
      max-width: 100%;
      margin-right: 0;
    }
  }
  
  .title {
    color: light-dark(var(--mantine-color-black), var(--mantine-color-white));
    font-family: Outfit, var(--mantine-font-family);
    font-size: 4rem !important;
    line-height: 0.9 !important;
    font-weight: 500;
  
    @media (max-width: $mantine-breakpoint-sm) {
      font-size: 3rem !important;
    }
  }
  
  
  .image {
    width: 25%;
    background-color: var(--mantine-color-gray-1);
  
    @media (max-width: var(--mantine-breakpoint-md)) {
      display: none;
    }
  }

  .title {
    text-align: center;
  }

  .control {
    margin: 0 auto;
    align-self: center;
  }
  
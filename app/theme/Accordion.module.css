/* Accordion Root */
.accordion {
  border-radius: var(--mantine-radius-xxs);
}

/* Accordion Item */
.item {
  border: 1px solid var(--mantine-color-black);
  border-radius: var(--mantine-radius-xxs);
  margin-bottom: 0.5rem;
  box-shadow: .25em .25em 0 0 var(--mantine-color-black);
  background-color: var(--mantine-color-white);
  transition: all 200ms ease;
}

.item:hover {
  transform: translateY(-2px);
  box-shadow: .35em .35em 0 0 var(--mantine-color-black);
}

.item:last-child {
  margin-bottom: 0;
}

/* Accordion Control (Header/Button) */
.control {
  padding: 1rem 1.25rem;
  background-color: transparent;
  border: none;
  border-radius: var(--mantine-radius-xxs);
  font-weight: 500;
  font-family: var(--font-kantumruy-pro);
  color: var(--mantine-color-black);
  transition: background-color 200ms ease;
  width: 100%;
}

.control:hover {
  background-color: var(--mantine-color-gray-0);
}

.control:focus {
  background-color: var(--mantine-color-black);
  color: var(--mantine-color-white);
  outline-offset: -5px;
  outline: none;
  .label {
    color: var(--mantine-color-white);
  }
  .content {
    color: var(--mantine-color-white);
  }
  .chevron {
    color: var(--mantine-color-white);
  }
}

/* Accordion Label */
.label {
  font-weight: 500;
  font-size: var(--mantine-font-size-md);
  color: var(--mantine-color-black);
}

/* Accordion Chevron */
.chevron {
  color: var(--mantine-color-black);
  transition: transform 200ms ease;
  font-size: 1.2rem;
}

.chevron[data-rotate] {
  transform: rotate(180deg);
}

/* Accordion Content */
.content {
  padding: 0 1.25rem 1.25rem 1.25rem;
  color: var(--mantine-color-gray-8);
  font-family: var(--font-kantumruy-pro);
  line-height: 1.5;
}

/* Accordion Panel */
.panel {

  margin-top: 0;
}

/* Active/Open State */
.item[data-active] {
  border-color: var(--mantine-color-black);
  background-color: var(--mantine-color-black);
  color: var(--mantine-color-white);
  .control {
    background-color: var(--mantine-color-black) !important;
  }
  .label {
    color: var(--mantine-color-white);
  }
  .content {
    color: var(--mantine-color-white);
  }
  .control:focus {
    outline: none;
  }
}

.item[data-active] .control {
  background-color: var(--mantine-color-white);
  border-bottom: none;
}



/* Disabled State */
.item[data-disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

.item[data-disabled] .control {
  cursor: not-allowed;
  pointer-events: none;
}

/* Variant: Elevated */
.item[data-variant="elevated"] {
  border: none;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
}

.item[data-variant="elevated"]:hover {
  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

/* Variant: Filled */
.item[data-variant="filled"] {
  background-color: var(--mantine-color-gray-0);
  border-color: var(--mantine-color-gray-3);
}

.item[data-variant="filled"] .control {
  background-color: var(--mantine-color-gray-1);
}

.item[data-variant="filled"] .control:hover {
  background-color: var(--mantine-color-gray-2);
} 
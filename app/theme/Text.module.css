.text {
    line-height: 1.25;
    margin: 0;
    padding: 0;
    font-family: var(--font-kantumruy-pro);
}

/* Ensure consistent text colors */
.text[data-dimmed] {
    color: var(--mantine-color-dimmed);
}

/* Ensure consistent font weights */
.text[data-weight="500"] {
    font-weight: 500;
}

.text[data-weight="600"] {
    font-weight: 600;
}

/* Ensure consistent sizes */
.text[data-size="sm"] {
    font-size: var(--mantine-font-size-sm);
}

.text[data-size="md"] {
    font-size: var(--mantine-font-size-md);
}

.text[data-size="lg"] {
    font-size: var(--mantine-font-size-lg);
}

.text[data-size="xl"] {
    font-size: var(--mantine-font-size-xl);
}
.root {
  margin-top: var(--mantine-spacing-xxs);
}

.input {
  border-radius: var(--mantine-radius-xxs);
  border: 1px solid var(--mantine-color-gray-3);
  background-color: var(--mantine-color-white);
  font-size: var(--mantine-font-size-sm);
  transition: border-color 100ms ease;
  padding: 1em;
  font-weight: 600;
  height: 3em;
}

.input:focus-within {
  outline: 1px solid var(--mantine-color-primary);
}

.label {
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--mantine-color-gray-7);
}

/* Style for disabled state */
.input[data-disabled] {
  background-color: var(--mantine-color-gray-1);
  cursor: not-allowed;
}

.input::placeholder {
  font-size: inherit;
  color: var(--mantine-color-gray-5);
  padding: 0;
  margin: 0;
}

.wrapper {
  position: relative;
  width: 100%;
}

.section {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--mantine-spacing-xl);
  color: var(--mantine-color-gray-5);
}

.required {
  color: var(--mantine-color-red-6);
  margin-left: var(--mantine-spacing-xs);
}

.description {
  margin-top: var(--mantine-spacing-xs);
  font-size: var(--mantine-font-size-sm);
  color: var(--mantine-color-gray-6);
}

.error {
  margin-top: var(--mantine-spacing-xs);
  font-size: var(--mantine-font-size-sm);
  color: var(--mantine-color-red-6);
}

.innerInput {
  width: 100%;
  height: 100%;
  padding: 0 var(--mantine-spacing-md);
  border: none;
  background: transparent;
  font-size: inherit;
  color: inherit;
  padding: 0 1.25em;
}

.innerInput:focus {
  outline: none;
}

.visibilityToggle {
  background: transparent;
  border: none;
  padding: 0;
  margin-right: 1.25em;
  cursor: pointer;
  color: var(--mantine-color-gray-5);
  transition: color 150ms ease;
}

.visibilityToggle:hover {
  color: var(--mantine-color-gray-7);
}

/* Inline input styles */

.inlineInput {
  height: auto !important;
  padding: 0;
  border: none;
  background: var(--mantine-color-gray-3);
  line-height: 0 !important;
  min-height: 0 !important;
}

.inlineInput:focus-within {
  outline: none;
  border: none;
}








.content {
    width: 90vw !important;
    min-height: 90vh !important;
    display: flex;
    flex-direction: column;
    padding: 0 !important;
    margin: 0 !important;
  }

  .header {
    position: relative;
    flex-shrink: 0;
    height: 2em !important;
    background-color: var(--mantine-color-gray-0) !important;
    h2 {
      margin:0 !important;
    }
  }

  .body {
    flex: 1;
    display: flex;
    overflow: hidden;
    padding: 0 !important;
    margin: 0 !important;
    min-height: 0; /* This is important for flex child overflow */
  }

  .close {
    position: absolute;
    top: var(--mantine-spacing-md);
    right: var(--mantine-spacing-md);
  }

  .title {
    font-size: 1rem !important;
    font-weight: 400 !important;
    margin-bottom: 1em;
  }

  .tabTitle {
    font-size: 1rem !important;
    font-weight: 600 !important;
    margin-bottom: 0.5em;
  }
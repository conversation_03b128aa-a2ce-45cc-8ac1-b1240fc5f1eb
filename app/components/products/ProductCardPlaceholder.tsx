import React from 'react';
import { Card, Skeleton, Stack, Group } from '@mantine/core';

const ProductCardPlaceholder: React.FC = () => {
  return (
    <Card shadow="sm" padding="md" radius="md" withBorder mt="xl">
      <Stack gap="md" className="h-full">
        <Stack gap="sm">
          <Skeleton height={28} width="70%" />
          <Group>
            <Skeleton height={22} width="40%" />
          </Group>
          <Skeleton height={16} mt="sm" />
          <Skeleton height={16} width="90%" />
        </Stack>

        <Stack gap="md">
          <Stack gap="xs">
            <Skeleton height={40} width="60%" />
          </Stack>
          <Skeleton height={42} mt="md" />
        </Stack>
      </Stack>
    </Card>
  );
};

export default ProductCardPlaceholder; 
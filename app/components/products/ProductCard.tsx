'use client';

import React from 'react';
import { Card, Badge, Text, Group, Stack, Button } from '@mantine/core';
import { Price, Product, ActiveSubscriptionDetails, getNormalizedMonthlyPrice } from '@/app/utils/pricingUtils';
import { Balancer } from 'react-wrap-balancer';

// Copied from app/subscribe/page.tsx
// export interface Price { ... }
// export interface Product { ... }
// interface ActiveSubscriptionDetails { ... }
// End copied types

interface UserProfile {
  id: string;
  email: string | null;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  subscription_status: string | null;
  subscribed_product_id: string | null;
  subscription_price_id: string | null;
  subscription_interval: string | null;
  subscription_current_period_end: string | null;
  cancel_at_period_end: boolean | null;
  purchases: string[] | null;
}

interface ProductCardProps {
  product: Product;
  isSubscriptionProduct: boolean;
  currentSelectedInterval?: string;
  activeSub: ActiveSubscriptionDetails | null;
  purchasedProductIds: string[];
  subscribingPriceId: string | null;
  loadingUserData: boolean;
  allProducts: Product[]; // For getActiveSubscriptionPrice
  onSubscribeClick: (priceId: string, productId: string) => void;
  // getClientIntervalOrderValue is simple enough to be inlined or passed if complex
  showCancelButton?: boolean;
  onCancelClick?: () => void; // Modified to take no arguments
  userProfile?: UserProfile | null;
  scheduledProductId?: string | null;
  scheduledEffectiveDate?: string | null;
  onCancelScheduledChange?: () => void;
  isCancellingSchedule?: boolean;
  onUndoCancellation?: () => void;
  isUndoingCancellation?: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  isSubscriptionProduct,
  currentSelectedInterval,
  activeSub,
  purchasedProductIds,
  subscribingPriceId,
  loadingUserData,
  allProducts,
  onSubscribeClick,
  showCancelButton,
  onCancelClick,
  userProfile,
  scheduledProductId,
  scheduledEffectiveDate,
  onCancelScheduledChange,
  isCancellingSchedule,
  onUndoCancellation,
  isUndoingCancellation,
}) => {
  const isActiveSubscriptionPlan =
    isSubscriptionProduct &&
    product.id === activeSub?.productId &&
    product.prices.some(
      (price) =>
        price.id === activeSub?.priceId &&
        (activeSub?.status === 'active' || activeSub?.status === 'trialing')
    );

  const isSubscriptionCancelling = 
    isActiveSubscriptionPlan && 
    activeSub?.cancelAtPeriodEnd === true;

  const isScheduledForDowngrade = 
    isActiveSubscriptionPlan && 
    !!scheduledProductId;

  const isScheduledPlan = product.id === scheduledProductId;

  const isOneTimeProductType = product.prices.every((p) => p.type === 'one_time');
  const hasBeenPurchasedAsOneTime =
    purchasedProductIds.includes(product.id) && isOneTimeProductType;

  const getClientIntervalOrderValue = (price: Price): number => {
    if (price.type === 'one_time') {
      return 5; 
    }
    if (price.recurring?.interval) {
      switch (price.recurring.interval.toLowerCase()) {
        case 'day': return 1;
        case 'week': return 2;
        case 'month': return 3;
        case 'year': return 4;
        default: return 4.5; 
      }
    }
    return 4.5; 
  };


  let pricesToDisplay = product.prices;
  if (isSubscriptionProduct && currentSelectedInterval) {
    pricesToDisplay = product.prices.filter((price) => {
      return price.type === 'recurring' && price.recurring?.interval === currentSelectedInterval;
    });
  }
  const sortedPricesToDisplay = [...pricesToDisplay].sort(
    (a, b) => getClientIntervalOrderValue(a) - getClientIntervalOrderValue(b)
  );

  const findEquivalentMonthlyPrice = (productId: string): number | null => {
    const productForPrice = allProducts.find(p => p.id === productId);
    if (!productForPrice) return null;
    const monthlyPrice = productForPrice.prices.find(
      (p) =>
        p.type === 'recurring' &&
        p.recurring?.interval === 'month' &&
        p.unit_amount !== null
    );
    return monthlyPrice?.unit_amount || null;
  };

  const calculateSavings = (yearlyAmount: number, monthlyAmount: number): number => {
    const yearlyTotal = yearlyAmount;
    const monthlyTotal = monthlyAmount * 12;
    return Math.round(((monthlyTotal - yearlyTotal) / monthlyTotal) * 100);
  };

  const getActiveSubscriptionPrice = (): Price | null => {
    if (!activeSub?.priceId) return null;
    for (const p of allProducts) {
      const foundPrice = p.prices.find((price) => price.id === activeSub.priceId);
      if (foundPrice) return foundPrice;
    }
    return null;
  };

  const activePrice = getActiveSubscriptionPrice();

  const getButtonProps = (price: Price) => {
    const isCurrentPriceOfActiveSubscription =
      isActiveSubscriptionPlan && price.id === activeSub?.priceId;
    const isPurchasedOneTime =
      hasBeenPurchasedAsOneTime && price.type === 'one_time' && product.id === product.id;
    const isProcessing = subscribingPriceId === price.id;
    const isScheduled = product.id === scheduledProductId;

    let buttonText = 'Choose Plan';
    let buttonColor = 'gray';
    let onClickAction = () => onSubscribeClick(price.id, product.id);
    let isDisabled = isProcessing || loadingUserData;
    let hidden = false;

    if (isScheduled) {
      buttonText = isCancellingSchedule ? 'Cancelling...' : 'Cancel Downgrade';
      buttonColor = 'red';
      onClickAction = () => onCancelScheduledChange && onCancelScheduledChange();
      isDisabled = isCancellingSchedule || loadingUserData;
    } else if (isCurrentPriceOfActiveSubscription) {
      buttonText = 'Current Plan';
      buttonColor = 'gray';
      isDisabled = true;
      hidden = true;
    } else if (isPurchasedOneTime) {
      buttonText = 'Purchased';
      buttonColor = 'gray';
      isDisabled = true;
    } else if (activePrice && activeSub?.status === 'active' && price.type === 'recurring') {
      const currentNormalizedPrice = getNormalizedMonthlyPrice(activePrice);
      const newNormalizedPrice = getNormalizedMonthlyPrice(price);
      
      if (currentNormalizedPrice === Infinity || newNormalizedPrice === Infinity) {
        // Default button text and color
      } else {
        const activeProductId = activeSub.productId;
        const isSameProduct = activeProductId === product.id;
        const isDifferentInterval =
          activePrice.recurring?.interval !== price.recurring?.interval;

        if (isSameProduct && isDifferentInterval) {
          buttonText = 'Change billing period';
          buttonColor = 'green';
        } else if (newNormalizedPrice > currentNormalizedPrice) {
          buttonText = 'Upgrade';
          buttonColor = 'green';
        } else if (newNormalizedPrice < currentNormalizedPrice) {
          buttonText = 'Downgrade';
          buttonColor = 'gray';
        } else if (price.recurring?.interval === activePrice.recurring?.interval) {
          buttonText = 'Switch Plan';
          buttonColor = 'gray';
        } else if (price.recurring?.interval && activePrice.recurring?.interval) {
          const currentIntervalOrder = getClientIntervalOrderValue(activePrice);
          const newIntervalOrder = getClientIntervalOrderValue(price);
          
          if (newIntervalOrder > currentIntervalOrder) {
            buttonText = 'Upgrade';
            buttonColor = 'green';
          } else if (newIntervalOrder < currentIntervalOrder) {
            buttonText = 'Downgrade';
            buttonColor = 'gray';
          } else {
            buttonText = 'Switch Plan';
            buttonColor = 'gray';
          }
        }
      }
    }

    if (isCurrentPriceOfActiveSubscription || isPurchasedOneTime) {
      isDisabled = true;
    }

    return { buttonText, buttonColor, isDisabled, onClickAction, hidden };
  }; 

  return (
    <Card
      shadow="sm"
      padding="lg"
      radius="md"
      withBorder
      className={`h-full ${
        isActiveSubscriptionPlan 
          ? 'active' 
          : hasBeenPurchasedAsOneTime 
            ? 'border-[3px] border-[hsl(var(--p))]'
            : ''
      }`}
      data-status={isActiveSubscriptionPlan ? 'active' : ''}
    >
      {product.images && product.images.length > 0 && (
        <Card.Section>
          <img
            src={product.images[0]}
            alt={product.name}
            className="w-full h-48 object-cover"
          />
        </Card.Section>
      )}

      <Stack gap="md" className="h-full">
        {/* Plan Details Section */}
        <Stack gap="sm">
          <Text size="xl" fw={600}>{product.name}</Text>
          <Group>
            {hasBeenPurchasedAsOneTime && (
              <Badge color="violet" variant="light">Purchased</Badge>
            )}
            {isActiveSubscriptionPlan && !isSubscriptionCancelling && !isScheduledForDowngrade && (
              <Badge color="green" variant="outline">Current Subscription</Badge>
            )}
            {isSubscriptionCancelling && !isScheduledForDowngrade &&(
              <Badge color="orange" variant="outline">Cancelling</Badge>
            )}
            {isScheduledForDowngrade && (
              <Badge color="orange" variant="outline">Cancellation Scheduled</Badge>
            )}
            {isScheduledPlan && (
              <Badge color="primary" variant="light">Scheduled</Badge>
            )}
          </Group>

          {product.description && (
            <Balancer>
              <Text size="sm" c="dimmed">{product.description}</Text>
            </Balancer>
          )}

          {isSubscriptionCancelling && userProfile?.subscription_current_period_end && (
            <Text size="sm" c="orange.7" fw={500}>
              Subscription ends on {new Date(userProfile.subscription_current_period_end).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </Text>
          )}

          {isScheduledForDowngrade && userProfile?.subscription_current_period_end && (
            <Text size="sm" c="orange.7" fw={500}>
              Ends on {new Date(userProfile.subscription_current_period_end).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </Text>
          )}

          {isScheduledPlan && scheduledEffectiveDate && (
            <Text size="sm" c="primary.7" fw={500}>
              Starts on {new Date(scheduledEffectiveDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </Text>
          )}

          {product.marketing_features && product.marketing_features.length > 0 && (
            <Stack gap="xs">
              {product.marketing_features.map((feature, index) => (
                <Text key={index} size="sm" c="gray.7">• {feature}</Text>
              ))}
            </Stack>
          )}

          {product.metadata.product_group && (
            <Text size="sm" c="dimmed">Group: {product.metadata.product_group}</Text>
          )}
        </Stack>

        {/* Prices and Action Buttons Section - Aligned to Bottom */}
        <Stack gap="md" mt="auto">
          {/* Prices Section */}
          {sortedPricesToDisplay.length > 0 ? (
            <Stack gap="xs">
              {sortedPricesToDisplay.map((price) => {
                const isCurrentPriceOfActiveSubscription =
                  isActiveSubscriptionPlan && price.id === activeSub?.priceId;
                return (
                    <Stack key={`${price.id}-display`}  justify="space-between" align="left" mb="0" gap="0">
                        <Text size="xxl" fw={600} className={isCurrentPriceOfActiveSubscription ? 'text-[var(--mantine-color-green-7)]' : ''}>
                          {price.unit_amount === 0 || price.unit_amount === null ? (
                            'Free'
                          ) : (
                            <>
                              {'$'}{(price.unit_amount / 100 % 1 === 0 ? Math.floor(price.unit_amount / 100) : (price.unit_amount / 100).toFixed(2))}
                              <Text span size="sm" c="dimmed">/{price.recurring?.interval || 'one-time'}</Text>
                            </>
                          )}
                          {(price.unit_amount === 0 || price.unit_amount === null) && (
                            <Text span size="sm" c="dimmed"> forever</Text>
                          )}
                        </Text>
                   
                        {price.recurring?.interval === 'year' && (
                          price.unit_amount !== null && price.unit_amount > 0 ? (
                            <Text size="sm" c="dimmed">
                              {'$'}{((price.unit_amount / 12) / 100).toFixed(2)}/month
                              {(() => {
                                const monthlyEquivalent = findEquivalentMonthlyPrice(
                                  product.id
                                );
                                if (monthlyEquivalent && monthlyEquivalent > 0) {
                                  const savingsPercent = calculateSavings(
                                    price.unit_amount!,
                                    monthlyEquivalent
                                  );
                                  if (savingsPercent > 0) {
                                    return (
                                      <Text span c="green.7" fw={500}>
                                        {' '}(–{savingsPercent}%)
                                      </Text>
                                    );
                                  }
                                }
                                return null;
                              })()}
                            </Text>
                          ) : (
                            <Text size="sm" c="transparent">
                              &nbsp;
                            </Text>
                          )
                        )}
                    </Stack>
                );
              })}
            </Stack>
          ) : (
            <Text c="dimmed" ta="center" py="md">
              No pricing options available for this plan.
            </Text>
          )}

          {/* Action Buttons Section */}
          {sortedPricesToDisplay.length > 0 && (
            <Stack gap="xs">
              {sortedPricesToDisplay.map((price) => {
                const { buttonText, buttonColor, isDisabled, onClickAction, hidden } = getButtonProps(price);

                if (isScheduledPlan && price.type === 'recurring') {
                  return (
                    <div key={`${price.id}-action`}>
                                            <Button
                          onClick={onClickAction}
                          disabled={isDisabled}
                          fullWidth
                          color={buttonColor}
                        >
                        {buttonText}
                      </Button>
                    </div>
                  );
                }
                
                if (!isScheduledPlan) {
                  return (
                    <div key={`${price.id}-action`}>
                      {!hidden && (
                        <Button
                          onClick={onClickAction}
                          disabled={isDisabled}
                          fullWidth
                          color={buttonColor}
                        >
                          {buttonText}
                        </Button>
                      )}
                      {isActiveSubscriptionPlan && 
                       price.id === activeSub?.priceId && 
                       showCancelButton && 
                       onCancelClick && 
                       !isSubscriptionCancelling &&
                       !isScheduledForDowngrade && (
                        <Button
                          onClick={onCancelClick}
                          variant="outline"
                          fullWidth
                          color="red"
                        >
                          Cancel Subscription
                        </Button>
                      )}
                      {isActiveSubscriptionPlan &&
                       price.id === activeSub?.priceId &&
                       isScheduledForDowngrade &&
                       onCancelScheduledChange && (
                        <Button
                          onClick={onCancelScheduledChange}
                          variant="outline"
                          fullWidth
                          color="red"
                          loading={isCancellingSchedule}
                        >
                          Cancel Downgrade
                        </Button>
                      )}
                      {isActiveSubscriptionPlan && 
                       price.id === activeSub?.priceId && 
                       isSubscriptionCancelling && 
                       onUndoCancellation && (
                        <Button
                          onClick={onUndoCancellation}
                          variant="outline"
                          fullWidth
                          color="green"
                          loading={isUndoingCancellation}
                        >
                          {isUndoingCancellation ? 'Undoing...' : 'Undo Cancellation'}
                        </Button>
                      )}
                    </div>
                  );
                }
                return null;
              })}
            </Stack>
          )}
        </Stack>
      </Stack>
    </Card>
  );
};

export default ProductCard;

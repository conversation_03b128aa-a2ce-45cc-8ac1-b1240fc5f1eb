'use client';

import { useEffect, useState } from 'react';
import { Box, Text, Code, Stack } from '@mantine/core';
import { useUserData } from '@/contexts/UserDataContext';
import { useAnonymousTransfer } from '@/app/hooks/useAnonymousTransfer';

interface AnonymousDebugInfoProps {
  conversationId?: string | null;
  conversationProgress?: any;
  pendingConversations?: Set<string>;
}

export function AnonymousDebugInfo({ 
  conversationId, 
  conversationProgress, 
  pendingConversations 
}: AnonymousDebugInfoProps) {
  const { user } = useUserData();
  const { getSessionInfo, isAnonymousMode } = useAnonymousTransfer();
  const [sessionInfo, setSessionInfo] = useState<any>(null);

  useEffect(() => {
    setSessionInfo(getSessionInfo());
  }, [getSessionInfo]);

  // Only show in development or for debugging
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <Box style={{ 
      position: 'fixed', 
      bottom: 20, 
      left: 20, 
      background: 'rgba(0,0,0,0.8)', 
      color: 'white', 
      padding: 16, 
      borderRadius: 8,
      fontSize: 12,
      maxWidth: 400,
      zIndex: 1000
    }}>
      <Stack gap="xs">
        <Text fw={600}>Debug Info</Text>
        
        <div>
          <Text size="xs" fw={500}>User Status:</Text>
          <Code size="xs">{user ? `Authenticated: ${user.id}` : 'Anonymous'}</Code>
        </div>

        <div>
          <Text size="xs" fw={500}>Anonymous Mode:</Text>
          <Code size="xs">{isAnonymousMode() ? 'Yes' : 'No'}</Code>
        </div>

        {sessionInfo && (
          <div>
            <Text size="xs" fw={500}>Session Info:</Text>
            <Code size="xs">{JSON.stringify(sessionInfo, null, 2)}</Code>
          </div>
        )}

        <div>
          <Text size="xs" fw={500}>Conversation ID:</Text>
          <Code size="xs">{conversationId || 'None'}</Code>
        </div>

        <div>
          <Text size="xs" fw={500}>Pending Conversations:</Text>
          <Code size="xs">{pendingConversations ? Array.from(pendingConversations).join(', ') || 'None' : 'None'}</Code>
        </div>

        {conversationProgress && (
          <div>
            <Text size="xs" fw={500}>Progress:</Text>
            <Code size="xs">{JSON.stringify(conversationProgress, null, 2)}</Code>
          </div>
        )}
      </Stack>
    </Box>
  );
}

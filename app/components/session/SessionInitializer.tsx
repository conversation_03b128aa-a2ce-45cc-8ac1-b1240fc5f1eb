'use client';

import { useEffect } from 'react';
import { sessionManager } from '@/lib/session/sessionManager';

/**
 * Component that initializes the session manager on app startup
 * Should be placed in the root layout
 */
export function SessionInitializer() {
  useEffect(() => {
    // Initialize session management when the app starts
    sessionManager.initialize();
  }, []);

  // This component doesn't render anything
  return null;
}

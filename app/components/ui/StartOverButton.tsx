'use client';

import { useState } from 'react';
import { But<PERSON>, Modal, Text, Group, Stack } from '@mantine/core';
import { IconRefresh } from '@tabler/icons-react';
import { useAnonymousTransfer } from '@/app/hooks/useAnonymousTransfer';
import { useRouter } from 'next/navigation';
import { useUserData } from '@/contexts/UserDataContext';

interface StartOverButtonProps {
  variant?: 'default' | 'subtle' | 'outline';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  redirectTo?: string;
}

export function StartOverButton({ 
  variant = 'outline', 
  size = 'sm',
  redirectTo = '/'
}: StartOverButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isClearing, setIsClearing] = useState(false);
  const { clearAnonymousSession, isAnonymousMode } = useAnonymousTransfer();
  const { user } = useUserData();
  const router = useRouter();

  // Only show for anonymous users
  if (user?.id || !isAnonymousMode()) {
    return null;
  }

  const handleStartOver = async () => {
    setIsClearing(true);
    
    try {
      const result = await clearAnonymousSession();
      
      if (result.success) {
        // Redirect to landing page or specified route
        router.push(redirectTo);
      } else {
        console.error('Failed to clear session:', result.error);
        // Still redirect even if cleanup failed
        router.push(redirectTo);
      }
    } catch (error) {
      console.error('Error during start over:', error);
      // Still redirect even if there was an error
      router.push(redirectTo);
    } finally {
      setIsClearing(false);
      setIsModalOpen(false);
    }
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        leftSection={<IconRefresh size={16} />}
        onClick={() => setIsModalOpen(true)}
        disabled={isClearing}
      >
        Start Over
      </Button>

      <Modal
        opened={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Start Over"
        centered
      >
        <Stack gap="md">
          <Text>
            This will delete your current conversation and start fresh. 
            This action cannot be undone.
          </Text>
          
          <Text size="sm" c="dimmed">
            Tip: Sign up or log in to save your conversations permanently!
          </Text>

          <Group justify="flex-end" gap="sm">
            <Button
              variant="default"
              onClick={() => setIsModalOpen(false)}
              disabled={isClearing}
            >
              Cancel
            </Button>
            <Button
              color="red"
              onClick={handleStartOver}
              loading={isClearing}
            >
              Start Over
            </Button>
          </Group>
        </Stack>
      </Modal>
    </>
  );
}

'use client';

import { Container, Group, Anchor, Text, Box } from '@mantine/core';
import { IconBrandTwitter, IconBrandYoutube, IconBrandInstagram } from '@tabler/icons-react';
import { Logo } from './Logo';
import Link from 'next/link';

const data = [
  {
    title: 'Product',
    links: [
      { label: 'About', link: '/about' },
      { label: 'Pricing', link: '/subscribe' },
      { label: 'Dashboard', link: '/dashboard' },
    ],
  },
  {
    title: 'Support',
    links: [
      { label: 'FAQ', link: '/faq' },
      { label: 'Contact', link: '/contact' },
      { label: 'Privacy Policy', link: '/privacy' },
    ],
  },
];

export function Footer() {
  const groups = data.map((group) => {
    const links = group.links.map((link, index) => (
      <Anchor
        key={index}
        component={Link}
        href={link.link}
        size="sm"
        c="dimmed"
        style={{ display: 'block' }}
        mb="xs"
      >
        {link.label}
      </Anchor>
    ));

    return (
      <div key={group.title} style={{ width: 160 }}>
        <Text fw={500} size="sm" mb="md">
          {group.title}
        </Text>
        {links}
      </div>
    );
  });

  return (
    <Box component="footer" mt="xl" p="xl" style={{ borderTop: '1px solid var(--mantine-color-gray-3)' }}>
      <Container size="xl">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', flexWrap: 'wrap' }}>
          <div style={{ maxWidth: 200 }}>
            <Logo />
            <Text size="xs" c="dimmed" mt="md">
              Transform YouTube videos into text with ease. Your content, simplified.
            </Text>
          </div>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 40 }}>
            {groups}
          </div>
        </div>

        <Group justify="space-between" mt="md">
          <Text c="dimmed" size="sm">
            © 2024 YouTubeToText. All rights reserved.
          </Text>

          <Group gap="xs" justify="flex-end" wrap="nowrap">
            <Anchor component="a" href="https://twitter.com" target="_blank" c="dimmed" size="sm">
              <IconBrandTwitter size={18} stroke={1.5} />
            </Anchor>
            <Anchor component="a" href="https://youtube.com" target="_blank" c="dimmed" size="sm">
              <IconBrandYoutube size={18} stroke={1.5} />
            </Anchor>
            <Anchor component="a" href="https://instagram.com" target="_blank" c="dimmed" size="sm">
              <IconBrandInstagram size={18} stroke={1.5} />
            </Anchor>
          </Group>
        </Group>
      </Container>
    </Box>
  );
}

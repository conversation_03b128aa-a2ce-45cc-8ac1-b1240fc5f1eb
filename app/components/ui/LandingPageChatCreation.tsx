'use client';

import { useCallback } from 'react';
import { Container, Paper, Stack, Title, Text, Button, Group } from '@mantine/core';
import { useRouter } from 'next/navigation';
import NewChatInput from '@/app/product/components/ui/NewChatInput';

interface LandingPageChatCreationProps {
  onClose?: () => void;
  showTitle?: boolean;
  containerSize?: string;
}

export function LandingPageChatCreation({
  onClose,
  showTitle = true,
  containerSize = "sm"
}: LandingPageChatCreationProps) {
  const router = useRouter();

  const handleConversationCreated = useCallback((
    conversationId: string,
    title: string,
    summary: string | null,
    videoUrl?: string
  ) => {
    // Immediately redirect to the conversation page
    router.push(`/product/${conversationId}`);
  }, [router]);



  return (
    <Container size={containerSize} px="md">
      <Paper shadow="lg" radius="md" p="xl" withBorder>
        <Stack gap="lg">
          {showTitle && (
            <Stack gap="xs" ta="center">
              <Title order={2}>Try it now</Title>
              <Text c="dimmed">
                Paste any YouTube URL to see how we transform video content into useful text
              </Text>
            </Stack>
          )}

          <Stack gap="md">
            <NewChatInput
              onConversationCreated={handleConversationCreated}
              useBackground={true}
              variant="main"
            />

            {onClose && (
              <Group justify="center">
                <Button variant="subtle" onClick={onClose}>
                  Cancel
                </Button>
              </Group>
            )}
          </Stack>
        </Stack>
      </Paper>
    </Container>
  );
}

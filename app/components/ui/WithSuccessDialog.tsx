'use client';

import React from 'react';
import SuccessDialog from './SuccessDialog';
import { useSuccessDialog } from '@/app/hooks/useSuccessDialog';

interface WithSuccessDialogProps {
  children: React.ReactNode;
  successTitle?: string;
  successMessage?: string;
  showHomepageButton?: boolean;
  showDashboardButton?: boolean;
}

export default function WithSuccessDialog({ 
  children, 
  successTitle,
  successMessage,
  showHomepageButton = true,
  showDashboardButton = true
}: WithSuccessDialogProps) {
  const { 
    isSuccessDialogOpen, 
    successSessionId, 
    closeSuccessDialog, 
    goToHomepage, 
    goToDashboard 
  } = useSuccessDialog();

  return (
    <>
      {children}
      <SuccessDialog
        opened={isSuccessDialogOpen}
        onClose={closeSuccessDialog}
        title={successTitle}
        message={successMessage}
        sessionId={successSessionId}
        onGoToHomepage={showHomepageButton ? goToHomepage : undefined}
        onGoToDashboard={showDashboardButton ? goToDashboard : undefined}
      />
    </>
  );
} 
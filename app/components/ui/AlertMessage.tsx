'use client';

import { useState, useEffect, useRef } from 'react';

interface AlertMessageProps {
  message: string | null;
  type: 'success' | 'error';
  duration?: number; // Time visible before fade starts
  fadeDuration?: number; // Duration of the fade animation
}

const DEFAULT_DURATION = 2500; // ms
const DEFAULT_FADE_DURATION = 500; // ms

const AlertMessage: React.FC<AlertMessageProps> = ({
  message,
  type,
  duration = DEFAULT_DURATION,
  fadeDuration = DEFAULT_FADE_DURATION,
}) => {
  const [internalMessage, setInternalMessage] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [opacity, setOpacity] = useState(0);

  const fadeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const removeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Clear previous timeouts if message changes or component unmounts
    const clearTimeouts = () => {
      if (fadeTimeoutRef.current) clearTimeout(fadeTimeoutRef.current);
      if (removeTimeoutRef.current) clearTimeout(removeTimeoutRef.current);
    };

    if (message) {
      clearTimeouts();
      setInternalMessage(message);
      setIsVisible(true);
      setOpacity(1); // Become fully visible immediately

      fadeTimeoutRef.current = setTimeout(() => {
        setOpacity(0); // Start fading out
      }, duration);

      removeTimeoutRef.current = setTimeout(() => {
        setIsVisible(false);
        setInternalMessage(null); // Clear message after fade out
      }, duration + fadeDuration);
    } else {
      // If message becomes null externally, start fade out immediately if it was visible
      // Or, if it was already fading, let it continue.
      // If it wasn't visible, do nothing.
      if (isVisible && opacity === 1) { // Was visible and not already fading
        clearTimeouts(); // Clear scheduled fade/remove
        setOpacity(0); // Start fading out now
        removeTimeoutRef.current = setTimeout(() => {
          setIsVisible(false);
          setInternalMessage(null);
        }, fadeDuration);
      }
    }

    return clearTimeouts; // Cleanup on unmount or if effect re-runs
  }, [message, duration, fadeDuration]); // CORRECTED: Removed isVisible and opacity from deps

  useEffect(() => {
    if (isVisible) {
      setOpacity(1);
    } else {
      setOpacity(0);
    }
  }, [isVisible, opacity]);

  if (!isVisible || !internalMessage) {
    return null;
  }

  const baseClasses = 'p-3 rounded mb-4 transition-opacity ease-in-out';
  const typeClasses = type === 'success' 
    ? 'bg-green-100 text-green-700' 
    : 'bg-red-100 text-red-500';
  const opacityClass = opacity === 1 ? 'opacity-100' : 'opacity-0';

  return (
    <div 
      className={`${baseClasses} ${typeClasses} ${opacityClass}`}
      style={{ transitionDuration: `${fadeDuration}ms` }}
    >
      {internalMessage}
    </div>
  );
};

export default AlertMessage; 
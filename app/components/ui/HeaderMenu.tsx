'use client';

import { IconChevronDown } from '@tabler/icons-react';
import { Burger, Center, Container, Group, Menu, Drawer, Stack, Button } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import classes from '@/app/theme/HeaderMenu.module.css';
import UserNav from './UserNav';
import { Logo } from './Logo';
import Link from 'next/link';

interface MenuItem {
  link: string;
  label: string;
  links?: MenuItem[];
}

const links: MenuItem[] = [
  { link: '/about', label: 'About' },
  { link: '/subscribe', label: 'Pricing' },
  { link: '/faq', label: 'FAQ' },
  { link: '/feedback', label: 'Feedback' },
];

export function HeaderMenu() {
  const [opened, { toggle, close }] = useDisclosure(false);

  const items = links.map((link) => {
    const menuItems = link.links?.map((item: MenuItem) => (
      <Menu.Item key={item.link}>{item.label}</Menu.Item>
    ));

    if (menuItems) {
      return (
        <Menu key={link.label} trigger="hover" transitionProps={{ exitDuration: 0 }} withinPortal>
          <Menu.Target>
            <a
              href={link.link}
              className={classes.link}
              onClick={(event) => event.preventDefault()}
            >
              <Center>
                <span className={classes.linkLabel}>{link.label}</span>
                <IconChevronDown size={14} stroke={1.5} />
              </Center>
            </a>
          </Menu.Target>
          <Menu.Dropdown>{menuItems}</Menu.Dropdown>
        </Menu>
      );
    }

    return (
      <Button
        key={link.label}
        variant="navlink"
        color="black"
        onClick={(event) => event.preventDefault()}
      >
        <Link href={link.link}>{link.label}</Link>
      </Button>
    );
  });

  const mobileItems = links.map((link) => (
    <a
      key={link.label}
      href={link.link}
      className={classes.link}
      onClick={(event) => {
        event.preventDefault();
        close();
      }}
    >
      {link.label}
    </a>
  ));

  return (
    <header className={classes.header}>
      <Container size="full" style={{ position: 'relative', zIndex: 1 }}>
        <div className={classes.inner}>
          <div className="flex items-center h-full gap-4 absolute left-4 -top-[.15rem]">
            <Burger opened={opened} onClick={toggle} size="sm" hiddenFrom="sm" />
            <Logo />
          </div>
          
          <Group gap={5} visibleFrom="sm">
            {items}
          </Group>
          <UserNav />
        </div>
      </Container>

      <Drawer
        opened={opened}
        onClose={close}
        size="100%"
        padding="md"
        hiddenFrom="sm"
        zIndex={1000}
        withCloseButton={false}
      >
        <Stack pt={60} align="center" gap="lg">
          {mobileItems}
        </Stack>
      </Drawer>
    </header>
  );
}
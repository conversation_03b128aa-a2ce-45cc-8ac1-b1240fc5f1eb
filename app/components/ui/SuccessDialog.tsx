'use client';

import React from 'react';
import { Modal, Title, Text, Button, Flex, Stack } from '@mantine/core';
import { IconCheck } from '@tabler/icons-react';

interface SuccessDialogProps {
  opened: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
  sessionId?: string | null;
  onGoToHomepage?: () => void;
  onGoToDashboard?: () => void;
}

export default function SuccessDialog({ 
  opened, 
  onClose, 
  title = "Subscription Successful!", 
  message = "Thank you for subscribing. Your payment has been processed.",
  sessionId,
  onGoToHomepage,
  onGoToDashboard
}: SuccessDialogProps) {
  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Flex align="center" gap="sm">
          <IconCheck size={24} color="green" />
          <Title order={3} c="green">{title}</Title>
        </Flex>
      }
      centered
      size="md"
    >
      <Stack gap="md">
        <Text size="lg">{message}</Text>
        
        {sessionId && (
          <Text size="sm" c="dimmed">
            Your Checkout Session ID: {sessionId}
          </Text>
        )}
        
        <Text>
          You will receive a confirmation email shortly. You can now access your new features.
        </Text>
        
        <Flex gap="md" justify="flex-end" mt="lg">
          {onGoToHomepage && (
            <Button variant="outline" onClick={onGoToHomepage}>
              Go to Homepage
            </Button>
          )}
          {onGoToDashboard && (
            <Button onClick={onGoToDashboard}>
              Go to Dashboard
            </Button>
          )}
          <Button variant="filled" onClick={onClose}>
            Continue
          </Button>
        </Flex>
      </Stack>
    </Modal>
  );
} 
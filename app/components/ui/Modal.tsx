"use client";

import React from "react";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  id: string;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  children,
  title,
  id
}) => {
  return (
    <dialog id={id} className="modal" onClose={onClose} open={isOpen}>
      <div className="modal-box">
        {title && (
          <h3 className="font-[500] text-lg">{title}</h3>
        )}
        {children}
      </div>
    </dialog>
  );
};

export default Modal;

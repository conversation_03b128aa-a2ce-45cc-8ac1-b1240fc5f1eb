'use client';

import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';

export default function SignOutButton() {
  const router = useRouter();
  const supabase = createClient();

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/auth'); // Redirect to login page after sign out
    router.refresh(); // Ensure server components re-evaluate
  };

  return (
    <button
      onClick={handleSignOut}
      className="btn justify-start"
    >
      Sign Out
    </button>
  );
} 
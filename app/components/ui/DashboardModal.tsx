'use client';

import { Modal } from '@mantine/core';
import DashboardTabs from '@/app/dashboard/DashboardTabs';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import type { User } from '@supabase/supabase-js';
import { Product } from '@/app/utils/pricingUtils';
import Loader from './Loader';
import classes from '@/app/theme/DashboardModal.module.css';

interface DashboardModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Subscription {
  stripe_subscription_id: string;
  status: string;
  stripe_product_id: string | null;
  current_period_end: string;
  cancel_at_period_end: boolean;
}

interface Purchase {
  stripe_product_id: string;
}

interface Profile {
  stripe_subscription_id: string | null;
  subscription_status: string | null;
  subscribed_product_id: string | null;
  subscription_current_period_end: string | null;
  purchases: string[];
}

export default function DashboardModal({ isOpen, onClose }: DashboardModalProps) {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [subscriptions, setSubscriptions] = useState<Subscription[] | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [purchases, setPurchases] = useState<Purchase[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      const supabase = createClient();
      
      // Get user
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      if (!currentUser) {
        router.push('/auth?message=Please log in to view this page');
        return;
      }
      setUser(currentUser);

      // Get user profile and subscription data
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', currentUser.id)
        .single();

      if (profile) {
        const typedProfile = profile as Profile;
        // Set subscriptions
        if (typedProfile.stripe_subscription_id) {
          setSubscriptions([{
            stripe_subscription_id: typedProfile.stripe_subscription_id,
            status: typedProfile.subscription_status || 'unknown',
            stripe_product_id: typedProfile.subscribed_product_id,
            current_period_end: typedProfile.subscription_current_period_end || new Date().toISOString(),
            cancel_at_period_end: false
          }]);
        }

        // Set purchases
        if (typedProfile.purchases && Array.isArray(typedProfile.purchases)) {
          setPurchases(typedProfile.purchases.map((productId: string) => ({
            stripe_product_id: productId
          })));
        }
      }

      // Get products
      const { data: productsData } = await supabase
        .from('products')
        .select('*')
        .eq('active', true);
      
      if (productsData) {
        setProducts(productsData);
      }

      setIsLoading(false);
    };

    if (isOpen) {
      fetchData();
    }
  }, [isOpen, router]);

  if (!isOpen || !user) return null;

  return (
    <Modal
      opened={isOpen}
      onClose={onClose}
      title={`Settings, ${user.user_metadata?.name || user.email}`}
      size="full"
      classNames={classes}
      centered
    >
      {isLoading ? (
        <div className="py-8">
          <Loader text="Loading your dashboard..." />
        </div>
      ) : (
        <DashboardTabs
          isAdmin={user?.app_metadata?.is_admin === true}
          loggedInUserId={user.id}
          subscriptions={subscriptions}
          products={products}
          purchases={purchases}
        />
      )}
    </Modal>
  );
} 
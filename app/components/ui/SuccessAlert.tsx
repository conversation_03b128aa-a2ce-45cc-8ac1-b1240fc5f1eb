'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';

export default function SuccessAlert() {
  const searchParams = useSearchParams();
  const [visible, setVisible] = useState(false);
  const message = searchParams.get('message');
  const status = searchParams.get('status') || 'info';

  useEffect(() => {
    if (message) {
      setVisible(true);
      // Auto-hide after 5 seconds
      const timer = setTimeout(() => {
        setVisible(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  if (!message || !visible) return null;

  return (
    <div role="alert" className={`alert fixed font-[500] text-base left-1/2 top-8 -translate-x-1/2 z-50 p-4 rounded-2xl !text-center shadow-lg transition-opacity duration-300 border-2 ${
      status === 'success' ? 'alert-success text-success-content border-success' : 
      status === 'error' ? 'alert-error text-error-content border-error' : 
      'alert-warning text-warning-content border-warning'
    }`}>
      <div className="flex items-center">
        {status === 'success' && (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        )}
        <p>{message}</p>
        <button 
          onClick={() => setVisible(false)}
          className="ml-4 hover:text-gray-700 hidden"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  );
}
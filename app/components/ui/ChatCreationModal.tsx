'use client';

import { Modal } from '@mantine/core';
import { LandingPageChatCreation } from './LandingPageChatCreation';

interface ChatCreationModalProps {
  opened: boolean;
  onClose: () => void;
}

export function ChatCreationModal({ opened, onClose }: ChatCreationModalProps) {
  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title="Try VideoText"
      size="lg"
      centered
      overlayProps={{
        backgroundOpacity: 0.55,
        blur: 3,
      }}
    >
      <LandingPageChatCreation 
        onClose={onClose}
        showTitle={false}
        containerSize="full"
      />
    </Modal>
  );
}

import React from 'react';

interface LoaderProps {
  text?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const sizeClasses = {
  sm: 'w-8 h-8 border-2',
  md: 'w-12 h-12 border-4',
  lg: 'w-16 h-16 border-4'
};

export default function Loader({ text, size = 'md', className = '' }: LoaderProps) {
  return (
    <div className={`flex flex-col justify-center items-center ${className}`}>
      <div 
        className={`${sizeClasses[size]} border-primary-600 border-solid border-t-transparent rounded-full animate-spin`}
      />
      {text && (
        <p className="mt-4 text-lg font-semibold text-primary-700">
          {text}
        </p>
      )}
    </div>
  );
} 
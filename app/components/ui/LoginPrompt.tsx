'use client';

import { Box, Text, Button } from '@mantine/core';
import { useRouter } from 'next/navigation';
import styles from './LoginPrompt.module.css';

export default function LoginPrompt() {
	const router = useRouter();

	const handleLoginClick = () => {
		router.push('/auth');
	};

	return (
		<Box className={styles.loginPrompt}>
			<Text size="sm" className={styles.promptText}>
				Log in to save chats
			</Text>
			<Button 
				size="xs" 
				variant="filled"
				onClick={handleLoginClick}
				className={styles.loginButton}
			>
				Log in
			</Button>
		</Box>
	);
} 
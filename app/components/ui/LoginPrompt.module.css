.loginPrompt {
	position: fixed;
	top: 20px;
	right: 20px;
	background: var(--mantine-color-dark-7);
	border: 1px solid var(--mantine-color-dark-4);
	border-radius: 8px;
	padding: 12px 16px;
	display: flex;
	align-items: center;
	gap: 12px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
	z-index: 1000;
	backdrop-filter: blur(8px);
}

.promptText {
	color: var(--mantine-color-gray-3);
	font-weight: 500;
	white-space: nowrap;
}

.loginButton {
	background: var(--mantine-color-blue-6);
	color: white;
	border: none;
	font-weight: 600;
	min-width: 60px;
}

.loginButton:hover {
	background: var(--mantine-color-blue-7);
}

/* Light theme adjustments */
@media (prefers-color-scheme: light) {
	.loginPrompt {
		background: white;
		border-color: var(--mantine-color-gray-3);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	}
	
	.promptText {
		color: var(--mantine-color-gray-7);
	}
} 
'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import type { User } from '@supabase/supabase-js';
import { useAuthContext } from '@/contexts/AuthContext';
import { Button, Menu, Group, Text } from '@mantine/core';
import { IconChevronDown, IconDashboard, IconLogout } from '@tabler/icons-react';
import AuthButtons from './AuthButtons';
import DashboardModal from './DashboardModal';

export default function UserNav() {
  const supabase = createClient();
  const router = useRouter();
  const pathname = usePathname();
  const [user, setUser] = useState<User | null>(null);
  const [loadingInitial, setLoadingInitial] = useState(true);
  const { isAuthenticating } = useAuthContext();
  const [isDashboardOpen, setIsDashboardOpen] = useState(false);

  useEffect(() => {
    async function getUserSession() {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        setUser(session.user);
      }
      setLoadingInitial(false);
    }

    getUserSession();

    const { data: authListenerData } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        setUser(session?.user ?? null);
        setLoadingInitial(false);
      }
    );

    return () => {
      authListenerData?.subscription.unsubscribe();
    };
  }, [supabase.auth]); 

  if (isAuthenticating) {
    return (
      <nav>
        {/* Optionally, show a very minimal loading indicator or just nothing */}
      </nav>
    );
  }

  return (
    <nav className="fixed top-2 right-2 z-100">
      {loadingInitial ? (
        <p>Loading...</p>
      ) : user ? (
        <>
          <Menu
            width={260}
            position="bottom-end"
            transitionProps={{ transition: 'pop-top-right' }}
            withinPortal
            radius="xxs"
            trigger="hover"
          >
            <Menu.Target>
              <Button 
                variant="outline"
                color="black"
                rightSection={<IconChevronDown size={16} stroke={1.5} />}
              >
                <Group gap="xs">
                  <Text size="sm" fw={500}>{user.user_metadata?.name || user.email}</Text>
                </Group>
              </Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item 
                leftSection={<IconDashboard size={16} stroke={1.5} />}
                onClick={() => setIsDashboardOpen(true)}
              >
                Dashboard
              </Menu.Item>
              <Menu.Item
                color="red"
                variant="outline"
                leftSection={<IconLogout size={16} stroke={1.5} />}
                onClick={async () => {
                  await supabase.auth.signOut();
                  router.push('/');
                }}
              >
                Sign Out
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item disabled>
                <Text size="xs" c="dimmed">
                  Signed in as {user.user_metadata?.name || user.email}
                  {user.email && user.user_metadata?.name && ` (${user.email})`}
                </Text>
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>

          <DashboardModal 
            isOpen={isDashboardOpen} 
            onClose={() => setIsDashboardOpen(false)} 
          />
        </>
      ) : (
        pathname && !pathname.startsWith('/auth') && <AuthButtons />
      )}
    </nav>
  );
} 
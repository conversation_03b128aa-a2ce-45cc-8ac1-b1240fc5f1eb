'use client';

import { useEffect, useState } from 'react';
import { useUserData } from '@/contexts/UserDataContext';
import { useAnonymousTransfer } from '@/app/hooks/useAnonymousTransfer';
import { Notification } from '@mantine/core';
import { IconCheck, IconX } from '@tabler/icons-react';

/**
 * Component that handles transferring anonymous conversations when user logs in
 * Should be placed in the app layout or auth callback pages
 */
export function AnonymousTransferHandler() {
  const { user } = useUserData();
  const { transferAnonymousConversations, isAnonymousMode } = useAnonymousTransfer();
  const [transferStatus, setTransferStatus] = useState<{
    show: boolean;
    success: boolean;
    message: string;
    count?: number;
  }>({ show: false, success: false, message: '' });

  useEffect(() => {
    // Only run transfer when user becomes authenticated and we have anonymous conversations
    if (user?.id && isAnonymousMode()) {
      handleTransfer();
    }
  }, [user?.id]); // Only depend on user ID changes

  const handleTransfer = async () => {
    try {
      const result = await transferAnonymousConversations();
      
      if (result.success && result.transferred > 0) {
        setTransferStatus({
          show: true,
          success: true,
          message: `Successfully saved ${result.transferred} conversation${result.transferred === 1 ? '' : 's'} to your account!`,
          count: result.transferred
        });
      } else if (result.success && result.transferred === 0) {
        // No conversations to transfer - don't show notification
        setTransferStatus({ show: false, success: true, message: '' });
      } else {
        setTransferStatus({
          show: true,
          success: false,
          message: result.error || 'Failed to save conversations to your account'
        });
      }
    } catch (error) {
      console.error('Transfer error:', error);
      setTransferStatus({
        show: true,
        success: false,
        message: 'An error occurred while saving your conversations'
      });
    }

    // Auto-hide notification after 5 seconds
    if (transferStatus.show) {
      setTimeout(() => {
        setTransferStatus(prev => ({ ...prev, show: false }));
      }, 5000);
    }
  };

  if (!transferStatus.show) {
    return null;
  }

  return (
    <div style={{ 
      position: 'fixed', 
      top: 20, 
      right: 20, 
      zIndex: 1000,
      maxWidth: 400
    }}>
      <Notification
        icon={transferStatus.success ? <IconCheck size={18} /> : <IconX size={18} />}
        color={transferStatus.success ? 'green' : 'red'}
        title={transferStatus.success ? 'Conversations Saved!' : 'Transfer Failed'}
        onClose={() => setTransferStatus(prev => ({ ...prev, show: false }))}
        withCloseButton
      >
        {transferStatus.message}
      </Notification>
    </div>
  );
}

import { UserDataProvider } from "@/contexts/UserDataContext"
import { AuthProvider } from "@/contexts/AuthContext"
import { ColorSchemeScript, mantineHtmlProps } from '@mantine/core';
import '@mantine/core/styles.css';
import 'mantine-contextmenu/styles.css';
import './globals.css';
import contextMenuClasses from './theme/Menu.module.css';
import { theme } from './theme';
import { kantumruyPro, atkinsonHyperlegible } from './fonts';
import ClientProvider from './components/ClientProvider';
import { ContextMenuProvider } from 'mantine-contextmenu';

// Initialize background processing (server-side only)
if (typeof window === 'undefined') {
  import('@/lib/background-processing/startup').catch(console.error);
}


export const metadata = {
  title: 'Videotext',
  description: 'I have followed setup instructions carefully',
  appleWebApp: {
    title: 'Your video assistant',
    statusBarStyle: 'black-translucent',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" {...mantineHtmlProps}>
      <head>
        <ColorSchemeScript />
      </head>
      <body className={`${kantumruyPro.variable} ${atkinsonHyperlegible.variable}`}>
        <ClientProvider theme={theme}>
          <AuthProvider>
            <UserDataProvider>
              <ContextMenuProvider classNames={{
                root: contextMenuClasses.contextMenuRoot,
                item: contextMenuClasses.contextMenuItem,
              }}>

                {children}
              </ContextMenuProvider>
            </UserDataProvider>
          </AuthProvider>
        </ClientProvider>
      </body>
    </html>
  );
}

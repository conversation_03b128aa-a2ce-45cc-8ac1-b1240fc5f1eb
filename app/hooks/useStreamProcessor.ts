
'use client';

import { useState, useEffect, useCallback } from 'react';
import { NodeStatus } from '@/app/product/components/dify/NodeStatusDisplay';

// Define the shape of the data that the stream will handle
export interface StreamData {
  accumulatedAnswer: string;
  messageId: string;
  conversationId: string | null;
  progress?: {
    eventCount: number;
    messageCount: number;
    accumulatedLength: number;
    hasAnswer: boolean;
    answerLength: number;
  };
}

// Options for the custom hook
interface UseStreamProcessorOptions {
  url: string;
  body: object;
  enabled: boolean;
  onStreamStart?: () => void;
  onStreamEnd?: () => void;
  onNodeStatusChange?: (statusOrUpdater: NodeStatus | ((prev: NodeStatus) => NodeStatus)) => void;
  onData: (data: StreamData) => void;
  onError?: (error: string) => void;
  onProgressUpdate?: (progress: { type: string; message?: string; [key: string]: any }) => void;
}

export function useStreamProcessor({
  url,
  body,
  enabled,
  onStreamStart,
  onStreamEnd,
  onNodeStatusChange,
  onData,
  onError,
  onProgressUpdate,
}: UseStreamProcessorOptions) {
  const [isProcessing, setIsProcessing] = useState(false);

  const processStream = useCallback(async () => {
    if (!enabled || isProcessing) return;

    setIsProcessing(true);
    onStreamStart?.();

    onNodeStatusChange?.({
      currentNode: 'Starting...',
      currentNodeIndex: 0,
      totalNodes: 0,
      workflowRunId: null,
      status: 'running',
    });

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No reader available');

      let accumulatedAnswer = '';
      let messageId = '';
      let conversationId: string | null = null;
      const nodesMap = new Map();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const events = chunk.split('\n\n').filter(Boolean);

        for (const eventStr of events) {
          if (!eventStr.startsWith('data: ')) continue;

          const jsonStr = eventStr.substring(6);
          try {
            const event = JSON.parse(jsonStr);

            switch (event.event) {
              case 'workflow_started':
                onNodeStatusChange?.(prev => ({
                  ...prev!,
                  workflowRunId: event.workflow_run_id,
                  status: 'running',
                }));
                break;
              case 'node_started':
                nodesMap.set(event.data.node_id, event.data);
                onNodeStatusChange?.(prev => ({
                  ...prev!,
                  currentNode: event.data.title,
                  currentNodeIndex: event.data.index,
                  totalNodes: Math.max(prev!.totalNodes, nodesMap.size),
                  status: 'running',
                }));
                break;
              case 'node_finished':
                onNodeStatusChange?.(prev => ({
                  ...prev!,
                  status: 'running',
                  currentNode: `${event.data.title} (completed)`,
                }));
                break;
              case 'workflow_finished':
                onNodeStatusChange?.(prev => ({
                  ...prev!,
                  status: 'finished',
                  currentNode: 'Workflow Finished',
                }));
                break;
              case 'message':
                if (event.conversation_id && !conversationId) {
                  conversationId = event.conversation_id;
                }
                if (event.message_id) {
                  messageId = event.message_id;
                }
                accumulatedAnswer += event.answer || '';
                
                // Handle progress information if available
                if (event.progress) {
                  onData({ 
                    accumulatedAnswer, 
                    messageId, 
                    conversationId,
                    progress: event.progress
                  });
                }
                break;
              case 'message_end':
                if (event.conversation_id && !conversationId) {
                    conversationId = event.conversation_id;
                }
                // When the message ends, we can pass the data
                onData({ accumulatedAnswer, messageId, conversationId });
                break;
              case 'error':
                throw new Error(event.message || 'Unknown error occurred during stream');
              case 'progress_update':
                onProgressUpdate?.({
                  type: event.data.type,
                  message: event.data.message,
                  ...event.data
                });
                break;
            }
          } catch (err) {
            console.warn('Failed to parse stream event:', err);
          }
        }
      }
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        onError?.(errorMessage);
        onNodeStatusChange?.({
            currentNode: 'Error',
            currentNodeIndex: 0,
            totalNodes: 0,
            workflowRunId: null,
            status: 'finished',
        });
    } finally {
        setTimeout(() => {
            onNodeStatusChange?.({
              currentNode: '',
              currentNodeIndex: 0,
              totalNodes: 0,
              workflowRunId: null,
              status: 'none',
            });
          }, 3000);

      setIsProcessing(false);
      onStreamEnd?.();
    }
  }, [enabled, isProcessing, url, body, onStreamStart, onStreamEnd, onNodeStatusChange, onData, onError, onProgressUpdate]);

  useEffect(() => {
    processStream();
  }, [processStream]);
} 
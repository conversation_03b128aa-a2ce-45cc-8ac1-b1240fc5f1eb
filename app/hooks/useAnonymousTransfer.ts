'use client';

import { useCallback } from 'react';
import { sessionManager, getSessionHeaders } from '@/lib/session/sessionManager';

export interface TransferResult {
  success: boolean;
  transferred: number;
  conversations?: Array<{ id: string; title: string }>;
  error?: string;
}

export function useAnonymousTransfer() {
  
  /**
   * Transfer anonymous conversations to authenticated user account
   * Call this after successful login/signup
   */
  const transferAnonymousConversations = useCallback(async (): Promise<TransferResult> => {
    try {
      // Get the current session ID before clearing it
      const sessionId = sessionManager.getSessionId();
      
      if (!sessionId) {
        return {
          success: true,
          transferred: 0
        };
      }

      console.log('Transferring anonymous conversations for session:', sessionId);

      // Call the transfer API
      const response = await fetch('/api/product/transfer-anonymous-conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getSessionHeaders()
        },
        body: JSON.stringify({ sessionId })
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('Transfer failed:', result.error);
        return {
          success: false,
          transferred: 0,
          error: result.error || 'Failed to transfer conversations'
        };
      }

      console.log('Transfer successful:', result);

      // Clear the anonymous session after successful transfer
      sessionManager.clearSession();

      return {
        success: true,
        transferred: result.transferred || 0,
        conversations: result.conversations
      };

    } catch (error) {
      console.error('Error transferring anonymous conversations:', error);
      return {
        success: false,
        transferred: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }, []);

  /**
   * Clean up current anonymous session (for "start over" functionality)
   */
  const clearAnonymousSession = useCallback(async (): Promise<{ success: boolean; error?: string }> => {
    try {
      const sessionId = sessionManager.getSessionId();
      
      if (!sessionId) {
        return { success: true };
      }

      console.log('Clearing anonymous session:', sessionId);

      // Call the cleanup API for this specific session
      const response = await fetch('/api/product/cleanup-anonymous-conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ sessionId })
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('Cleanup failed:', result.error);
        // Still clear the local session even if server cleanup fails
        sessionManager.clearSession();
        return {
          success: false,
          error: result.error || 'Failed to clean up conversations'
        };
      }

      console.log('Cleanup successful:', result);

      // Clear the local session
      sessionManager.clearSession();

      return { success: true };

    } catch (error) {
      console.error('Error clearing anonymous session:', error);
      // Still clear the local session even if server cleanup fails
      sessionManager.clearSession();
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }, []);

  /**
   * Get current session info
   */
  const getSessionInfo = useCallback(() => {
    return sessionManager.getSessionInfo();
  }, []);

  /**
   * Check if user is in anonymous mode
   */
  const isAnonymousMode = useCallback(() => {
    return sessionManager.getSessionId() !== null;
  }, []);

  return {
    transferAnonymousConversations,
    clearAnonymousSession,
    getSessionInfo,
    isAnonymousMode
  };
}

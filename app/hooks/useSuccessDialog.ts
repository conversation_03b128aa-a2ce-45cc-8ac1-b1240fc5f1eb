'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';

export function useSuccessDialog() {
  const [isSuccessDialogOpen, setIsSuccessDialogOpen] = useState(false);
  const [successSessionId, setSuccessSessionId] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const success = searchParams.get('success');
    const sessionId = searchParams.get('session_id');
    
    if (success === 'true' && sessionId) {
      setSuccessSessionId(sessionId);
      setIsSuccessDialogOpen(true);
      
      // Clean up URL parameters after showing dialog
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('success');
      newUrl.searchParams.delete('session_id');
      
      // Replace current URL without the success parameters
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, [searchParams]);

  const closeSuccessDialog = () => {
    setIsSuccessDialogOpen(false);
    setSuccessSessionId(null);
  };

  const goToHomepage = () => {
    closeSuccessDialog();
    router.push('/');
  };

  const goToDashboard = () => {
    closeSuccessDialog();
    router.push('/dashboard');
  };

  return {
    isSuccessDialogOpen,
    successSessionId,
    closeSuccessDialog,
    goToHomepage,
    goToDashboard,
  };
} 
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { useAuthContext } from '@/contexts/AuthContext';
import SuccessAlert from '@/app/components/ui/SuccessAlert';

export default function ResetPasswordPage() {
  const router = useRouter();
  const supabase = createClient();
  const { setIsAuthenticating } = useAuthContext();
  
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [isCheckingSession, setIsCheckingSession] = useState(true);

  // Check if the user is authenticated via password recovery
  useEffect(() => {
    const checkAuth = async () => {
      try {
        setIsCheckingSession(true);
        const { data } = await supabase.auth.getSession();
        
        if (!data?.session) {
          // No active session, redirect to login
          setMessage('Your password reset link has expired or is invalid. Please request a new one.');
          setTimeout(() => {
            router.push('/auth');
          }, 3000);
          return;
        }
        
        // Set a cookie to indicate this is a password recovery session
        document.cookie = "sb-recovery-mode=true; path=/; max-age=1800"; // 30 minutes
        
        setIsVerified(true);
      } catch (err) {
        console.error('Error checking auth:', err);
        setMessage('An error occurred while verifying your session.');
      } finally {
        setIsCheckingSession(false);
      }
    };
    
    checkAuth();
    
    // Listen for auth state changes
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('[ResetPasswordPage] Auth event:', event, session);
      
      if (event === "PASSWORD_RECOVERY") {
        // Set a cookie to indicate this is a password recovery session
        document.cookie = "sb-recovery-mode=true; path=/; max-age=1800"; // 30 minutes
        setIsVerified(true);
      } else if (event === "SIGNED_OUT") {
        // Clear the recovery mode cookie
        document.cookie = "sb-recovery-mode=; path=/; max-age=0";
        router.push('/auth');
      }
    });
    
    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [supabase, router]);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (password !== confirmPassword) {
      setMessage('Passwords do not match.');
      return;
    }
    
    if (password.length < 6) {
      setMessage('Password must be at least 6 characters long.');
      return;
    }
    
    setIsSubmitting(true);
    setMessage('');
    setIsAuthenticating(true);
    
    try {
      const { error } = await supabase.auth.updateUser({ password });
      
      if (error) {
        setMessage(`Error: ${error.message}`);
      } else {
        setMessage('Password updated successfully! Redirecting to login page...');
        
        // Clear the recovery mode cookie
        document.cookie = "sb-recovery-mode=; path=/; max-age=0";
        
        // Sign out the user after password reset to ensure they're not authenticated
        await supabase.auth.signOut();
        
        setTimeout(() => {
          router.push('/auth');
        }, 2000);
      }
    } catch (err) {
      console.error('Error updating password:', err);
      setMessage('An unexpected error occurred while updating your password.');
    } finally {
      setIsSubmitting(false);
      setIsAuthenticating(false);
    }
  };

  if (isCheckingSession) {
    return (
      <div className="h-screen flex flex-col justify-center items-center">
        <div className="w-12 h-12 border-4 border-primary-600 border-solid border-t-transparent rounded-full animate-spin"></div>
        <p className="mt-4 text-lg font-semibold text-primary-700">Verifying your session...</p>
      </div>
    );
  }

  const inputStyle = "input input-bordered w-full"; // Reusable style

  return (
    <div className="h-screen flex flex-col justify-center items-center">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2>
          Reset Your Password
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        {/* Add SuccessAlert component */}
        <SuccessAlert />
        
        <div className="bg-white shadow sm:rounded-lg p-4">
          {!isVerified ? (
            <div className="p-4 text-center">
              <p className="text-red-600">{message}</p>
              <button 
                onClick={() => router.push('/auth')}
                className="mt-4 btn btn-primary"
              >
                Return to Login
              </button>
            </div>
          ) : (
            <form onSubmit={handleResetPassword} className="space-y-6 p-4">
              <p className=" text-gray-600">Please enter your new password below.</p>
              <div>
                <label htmlFor="password" className="block   text-gray-700">
                  New Password
                </label>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className={inputStyle}
                  disabled={isSubmitting}
                />
              </div>
              <div>
                <label htmlFor="confirm-password" className="block   text-gray-700">
                  Confirm New Password
                </label>
                <input
                  id="confirm-password"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  className={inputStyle}
                  disabled={isSubmitting}
                />
              </div>

              {message && <p className={` ${message.startsWith('Error:') ? 'text-red-600' : 'text-green-600'}`}>{message}</p>}

              <button 
                type="submit" 
                className="btn btn-primary w-full bg-primary-600 hover:bg-primary-700"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Updating Password...' : 'Reset Password'}
              </button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}

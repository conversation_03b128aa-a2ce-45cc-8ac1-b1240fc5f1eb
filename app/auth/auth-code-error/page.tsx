'use client';

import { useRouter } from 'next/navigation';
import SuccessAlert from '@/app/components/ui/SuccessAlert';

export default function AuthCodeErrorPage() {
  const router = useRouter();

  return (
    <div className="h-screen flex flex-col justify-center items-center">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2>
          Authentication Error
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        {/* Add SuccessAlert component */}
        <SuccessAlert />
        
        <div className="bg-white shadow sm:rounded-lg p-6">
          <div className="space-y-6">
            <p className="text-base text-gray-700">
              There was an error processing your authentication request. This could be due to:
            </p>
            <ul className="list-disc pl-5  text-gray-600 space-y-1">
              <li>An expired or invalid authentication link</li>
              <li>A technical issue with the authentication service</li>
            </ul>
            <p className=" text-gray-600 mt-4">
              Please try again or contact support if the problem persists.
            </p>
            <div className="flex items-center justify-center pt-2">
              <button
                onClick={() => router.push('/auth')}
                className="btn btn-primary w-full"
              >
                Return to Login Page
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

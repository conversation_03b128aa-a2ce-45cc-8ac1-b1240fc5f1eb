import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: Request) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  // if "next" is in param, use it as the redirect URL
  const next = searchParams.get('next') ?? '/subscribe';
  
  console.log('Callback route params:', { code, next });
  
  // Handle normal auth code flow
  if (code) {
    const supabase = await createClient();
    const { error } = await supabase.auth.exchangeCodeForSession(code);
    console.log('Callback code exchange result:', { error });
    if (!error) {
      return NextResponse.redirect(`${origin}${next}?message=Successfully authenticated&status=success`);
    }
  }

  // Return the user to an error page with instructions
  console.log('Callback redirecting to error page');
  return NextResponse.redirect(`${origin}/auth/auth-code-error?message=Authentication failed&status=error`);
}

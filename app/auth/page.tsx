'use client';

import { useState, FormEvent, useEffect, useRef, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { useAuthContext } from '@/contexts/AuthContext';
import SuccessAlert from '@/app/components/ui/SuccessAlert';
import { 
  FloatingIndicator, 
  Tabs, 
  Button, 
  TextInput, 
  PasswordInput, 
  Alert, 
  Title,
  Stack, 
  Container, 
  Group, 
  Text, 
  em, 
  Image 
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { IconInfoCircle } from '@tabler/icons-react';
import { Balancer } from 'react-wrap-balancer';
// Style imports now come from theme.ts

export default function AuthPage() {  
  const router = useRouter();
  const searchParams = useSearchParams();
  const returnUrl = searchParams.get('returnUrl') || '/subscribe';
  
  const supabase = createClient();
  const { setIsAuthenticating } = useAuthContext();

  const [activeTab, setActiveTab] = useState<string>('login');
  const [showResetPasswordTab, setShowResetPasswordTab] = useState(false);
  const [resetPasswordStep, setResetPasswordStep] = useState<'request' | 'update'>('request');

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [name, setName] = useState('');
  const [statusMessage, setStatusMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [refsReady, setRefsReady] = useState(false);
  const [showResendConfirmation, setShowResendConfirmation] = useState(false);

  // Media query to determine if we should show the left panel
  const showLeftPanel = useMediaQuery(`(min-width: ${em(992)})`); // Show on md and above

  // Set initial tab from URL hash
  useEffect(() => {
    const hash = window.location.hash.substring(1);
    if (hash === 'signup' || hash === 'login') {
      setActiveTab(hash);
    }
  }, []);

  // Update URL hash when tab changes
  useEffect(() => {
    if (activeTab === 'login' || activeTab === 'signup') {
      router.replace(`#${activeTab}`, { scroll: false });
    }
  }, [activeTab, router]);

  // Refs for floating indicator
  const rootRef = useRef<HTMLDivElement | null>(null);
  const controlsRefs = useRef<Record<string, HTMLButtonElement | null>>({});
  
  const setControlRef = useCallback((val: string) => (node: HTMLButtonElement | null) => {
    controlsRefs.current[val] = node;
  }, []);

  // Trigger a re-render once refs have been assigned after first mount
  useEffect(() => {
    // run only after initial mount, refs will be assigned before this effect runs
    setRefsReady(true);
  }, []);

  const clearFormStates = (clearEmail = false) => {
    if (clearEmail) setEmail('');
    setPassword('');
    setNewPassword('');
    setConfirmNewPassword('');
    setName('');
    // message is usually cleared separately or on tab change
  };
  
  // Session check - password reset detection removed
  useEffect(() => {
    const checkSession = async () => {
      try {
        // Get session
        const { data } = await supabase.auth.getSession();
        
        if (data?.session) {
          console.log('Session exists:', data.session);
          
          // If user is already logged in and has a returnUrl, redirect them
          if (returnUrl && returnUrl !== '/') {
            router.push(returnUrl);
          }
        }
      } catch (err) {
        console.error('Error checking session:', err);
      }
    };
    
    if (typeof window !== 'undefined') {
      checkSession();
    }
  }, [supabase, router, returnUrl]);

  const handleSignUp = async (event: FormEvent) => {
    event.preventDefault();
    setStatusMessage('');
    setIsSubmitting(true);
    setIsAuthenticating(true);
    
    // Construct the redirect URL for after confirmation
    const redirectTo = `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirm?next=${encodeURIComponent(returnUrl)}`;

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectTo,
        data: { name },
      },
    });

    if (error) {
      setStatusMessage(`Error: ${error.message}`);
      setIsAuthenticating(false);
      setShowResendConfirmation(false);
    } else if (data.user?.identities?.length === 0) {
      setStatusMessage('This email is already registered. Please sign in.');
      setActiveTab('login');
      setIsAuthenticating(false);
      setShowResendConfirmation(false);
    } else {
      setStatusMessage('Check your email for the confirmation link to sign in.');
      setActiveTab('login');
      clearFormStates(false);
      setIsAuthenticating(false);
      setShowResendConfirmation(true); // Show resend option after successful signup
    }
    setIsSubmitting(false);
  };

  const handleSignIn = async (event: FormEvent) => {
    event.preventDefault();
    setStatusMessage('');
    setIsSubmitting(true);
    setIsAuthenticating(true);

    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      // Check if the error is related to email not being confirmed
      if (error.message.toLowerCase().includes('email not confirmed') || 
          error.message.toLowerCase().includes('signup requires a verification')) {
        setStatusMessage(`Error: ${error.message}`);
        setShowResendConfirmation(true); // Show resend option for unconfirmed emails
      } else {
        setStatusMessage(`Error: ${error.message}`);
        setShowResendConfirmation(false);
      }
      setIsSubmitting(false);
      setIsAuthenticating(false);
      return;
    }
      
    // If there's a returnUrl, redirect there instead of home
    if (returnUrl && returnUrl !== '/') {
      router.push(returnUrl);
    } else {
      router.push('/');
    }
    // AuthContext will handle setIsAuthenticating(false) on navigation/session update
  };

  const handleResendConfirmation = async () => {
    if (!email) {
      setStatusMessage('Please enter your email address.');
      return;
    }

    setStatusMessage('');
    setIsSubmitting(true);

    try {
      const redirectTo = `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirm?next=${encodeURIComponent(returnUrl)}`;
      
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
        options: {
          emailRedirectTo: redirectTo,
        }
      });

      if (error) {
        setStatusMessage(`Error: ${error.message}`);
      } else {
        setStatusMessage('Confirmation email sent! Please check your inbox.');
        setShowResendConfirmation(false);
      }
    } catch (err) {
      console.error('Resend confirmation error:', err);
      setStatusMessage('Error: An unexpected error occurred while resending confirmation.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePasswordResetRequest = async (event: FormEvent) => {
    event.preventDefault();
    if (!email) {
      setStatusMessage('Please enter your email address.');
      return;
    }
    setStatusMessage('');
    setIsSubmitting(true);
    
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/reset-password`,
      });
      
      if (error) {
        setStatusMessage(`Error: ${error.message}`);
      } else {
        setStatusMessage('Password reset link sent! Check your email inbox.');
      }
    } catch (err) {
      console.error('Password reset error:', err);
      setStatusMessage('Error: An unexpected error occurred.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdatePassword = async (event: FormEvent) => {
    event.preventDefault();
    if (newPassword !== confirmNewPassword) {
      setStatusMessage('Error: Passwords do not match.');
      return;
    }
    if (!newPassword) {
      setStatusMessage('Error: Password cannot be empty.');
      return;
    }
    setStatusMessage('');
    setIsSubmitting(true);
    
    // Password update functionality has been removed
    // Placeholder UI only
    
    setIsSubmitting(false);
    setStatusMessage('This functionality has been disabled.');
  };

  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('[AuthPage] Auth event:', event, session);
      
      if (event === "SIGNED_IN") {
        // For regular sign-ins or completed sign-ups, redirect is handled by signIn/signUp
        // setIsAuthenticating(false) is handled by AuthContext or page navigation
        console.log("Signed in event handled, router push or context should take over.");
        setShowResendConfirmation(false); // Hide resend option when signed in
      } else if (event === "SIGNED_OUT") {
        setActiveTab('login');
        setShowResetPasswordTab(false);
        setShowResendConfirmation(false);
        clearFormStates(true);
      } else if (event === "PASSWORD_RECOVERY") {
        console.log("Password recovery event detected");
        
        // Set a cookie to indicate this is a password recovery session
        document.cookie = "sb-recovery-mode=true; path=/; max-age=1800"; // 30 minutes
        
        // If we're on the auth page when a password recovery link is clicked,
        // we should redirect to reset-password page
        router.push('/auth/reset-password');
      }
    });

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [supabase, router, setIsAuthenticating]); // Add setIsAuthenticating if it's stable

  // Effect to clear message and manage form states when activeTab changes
  useEffect(() => {
    setIsSubmitting(false);

    // If not moving to resetPassword tab, ensure it's hidden and step is reset
    if (activeTab !== 'resetPassword') {
      setShowResetPasswordTab(false);
      setResetPasswordStep('request');
    }
    
    // Clear passwords when switching tabs, conditionally clear email
    if (activeTab === 'login') {
        clearFormStates(false); // Keep email if switching from signup/reset request
    } else if (activeTab === 'signup') {
        clearFormStates(false); // Keep email if switching from login
        setShowResendConfirmation(false); // Hide resend when switching to signup
    } else if (activeTab === 'resetPassword') {
        if (resetPasswordStep === 'request') {
            // Keep email for the request form
            setPassword(''); setNewPassword(''); setConfirmNewPassword('');
        } else {
            // For update step, email might be from session context or can be cleared if needed
             setPassword(''); // Clear old main password field
        }
        setShowResendConfirmation(false); // Hide resend when in reset password mode
    }

  }, [activeTab, resetPasswordStep]); // Add resetPasswordStep dependency


  useEffect(() => {
    return () => {
      setIsAuthenticating(false); // Cleanup on unmount
    };
  }, [setIsAuthenticating]);
  


  return (

    <Group grow={showLeftPanel}>
    {showLeftPanel && (
      <Stack 
        justify="center" 
        align="center" 
        h="100vh"
        style={{
          background: 'linear-gradient(to right, var(--mantine-color-primary-8), var(--mantine-color-primary-5) 85%, var(--mantine-color-primary-4 ))',
          borderRight: '1px solid var(--mantine-color-primary-7)'
        }}
      >
        <Image
          src="/logo-negative.svg"
          alt="Logo"
          w="200"
          h="auto"
        />
      </Stack>
    )}

    <Stack h="100vh" w="100%" px="xl" style={{ position: 'relative' }}>
      {/* Back to home button */}
      <Button
        size="sm"
        onClick={() => router.push('/')}
        style={{
          position: 'absolute',
          top: '1rem',
          left: '1rem',
          zIndex: 10
        }}
      >
        ← Back to Home
      </Button>

      {/* Centered content */}
      <Stack justify="center" align="center" h="100%" w="100%">
        <Container size="md" w="100%">
          <Title order={2} ta="left" fw={600} mb="xs" ml="md">
            {activeTab === 'resetPassword' && resetPasswordStep === 'request' && 'Forgot Your Password?'}
            {activeTab === 'resetPassword' && resetPasswordStep === 'update' && 'Set New Password'}
            {activeTab === 'login' && 'Welcome back!'}
            {activeTab === 'signup' && 'Create a new account'}
          </Title>
        </Container>

      <Container size="xs" w="100%" mt="xl" style={{ position: 'relative' }}>
        <SuccessAlert />
        
        {statusMessage && (
          <Alert
            icon={<IconInfoCircle size="1rem" />}
            color={statusMessage.startsWith('Error:') ? 'red' : 'green'}
            withCloseButton
            onClose={() => setStatusMessage('')}
            mb="md"
            radius="lg"
          >
            {statusMessage.replace(/^Error: /i, '')}
          </Alert>
        )}

        {/* Resend confirmation section */}
        {showResendConfirmation && (
          <Alert
            color="primary"
            mb="md"
            radius="lg"
          >
            <Group justify="space-between" align="center">
              <Text>
                Didn&apos;t receive the confirmation email?
              </Text>
              <Button
                variant="subtle"
                size="xs"
                onClick={handleResendConfirmation}
                loading={isSubmitting}
                disabled={!email}
              >
                Resend
              </Button>
            </Group>
          </Alert>
        )}
        
        {/* Forms themselves will receive reduced opacity and pointer-events when submitting */}
        <Tabs
          value={activeTab}
          onChange={(value) => {
            setStatusMessage('');
            setActiveTab(value || 'login');
          }}
          variant="none"
        >
          <Tabs.List ref={rootRef}>
            <Tabs.Tab value="login" ref={setControlRef('login')}>
              Sign In
            </Tabs.Tab>
            <Tabs.Tab value="signup" ref={setControlRef('signup')}>
              Sign Up
            </Tabs.Tab>
            
              <Tabs.Tab 
                value="resetPassword" 
                ref={setControlRef('resetPassword')}
                  style={{ 
                    opacity: activeTab !== 'resetPassword' ? 0 : 1,
                    pointerEvents: activeTab !== 'resetPassword' ? 'none' : 'auto',
                    display: activeTab !== 'resetPassword' ? 'none' : 'block'
                  }}
              >
                Reset Password
              </Tabs.Tab>
            

            <FloatingIndicator
              target={refsReady && activeTab ? controlsRefs.current[activeTab] : null}
              parent={rootRef.current}
            />
          </Tabs.List>

            
          <Tabs.Panel value="login">
            <Stack component="form" onSubmit={handleSignIn} className={`${isSubmitting ? 'opacity-50 pointer-events-none' : ''}`}>
              <TextInput
                id="login-email"
                name="email"
                type="email"
                autoComplete="email"
                autoFocus
                required
                withAsterisk={false}
                value={email}
                onChange={(e) => setEmail(e.currentTarget.value)}
                placeholder="Email address"
                disabled={isSubmitting}
                aria-label="Email address"
              />
              <PasswordInput
                id="login-password"
                name="password"
                autoComplete="current-password"
                required
                withAsterisk={false}
                value={password}
                onChange={(e) => setPassword(e.currentTarget.value)}
                placeholder="Password"
                disabled={isSubmitting}
                aria-label="Password"
              />
              
              <Button
                type="submit"
                fullWidth
                loading={isSubmitting}
              >
                Sign In
              </Button>
              <Group justify="flex-end">
                <Button
                  variant="link"
                  onClick={() => {
                    setStatusMessage('');
                    setShowResetPasswordTab(true);
                    setActiveTab('resetPassword');
                    setResetPasswordStep('request');
                  }}
                  disabled={isSubmitting}
                >
                  Forgot your password?
                </Button>
              </Group>
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="signup">
            <Stack component="form" onSubmit={handleSignUp}  className={`${isSubmitting ? 'opacity-50 pointer-events-none' : ''}`}>
              <TextInput
                id="signup-name"
                name="name"
                type="text"
                autoComplete="name"
                required
                withAsterisk={false}
                value={name}
                onChange={(e) => setName(e.currentTarget.value)}
                placeholder="Name"
                disabled={isSubmitting}
                aria-label="Name"
              />
              <TextInput
                id="signup-email"
                name="email"
                type="email"
                autoComplete="email"
                required
                withAsterisk={false}
                value={email}
                onChange={(e) => setEmail(e.currentTarget.value)}
                placeholder="Email address"
                disabled={isSubmitting}
                aria-label="Email address"
              />
              <PasswordInput
                id="signup-password"
                name="password"
                autoComplete="new-password"
                required
                withAsterisk={false}
                value={password}
                onChange={(e) => setPassword(e.currentTarget.value)}
                placeholder="Password"
                disabled={isSubmitting}
                aria-label="Password"
              />
              <Button 
                type="submit" 
                fullWidth
                loading={isSubmitting}
              >
                Sign Up
              </Button>
            </Stack>
          </Tabs.Panel>

          {showResetPasswordTab && (
            <Tabs.Panel value="resetPassword">
              {resetPasswordStep === 'request' ? (
                <Stack component="form" onSubmit={handlePasswordResetRequest}  className={`${isSubmitting ? 'opacity-50 pointer-events-none' : ''}`}>
                  <Balancer>Enter your email address and we&apos;ll send you a link to reset your password.</Balancer>
                  <TextInput
                    id="reset-email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    withAsterisk={false}
                    value={email}
                    onChange={(e) => setEmail(e.currentTarget.value)}
                    placeholder="Email address"
                    disabled={isSubmitting}
                    aria-label="Email address"
                  />
                  <Button 
                    type="submit" 
                    fullWidth
                    loading={isSubmitting}
                  >
                    Send Password Reset Link
                  </Button>
                </Stack>
              ) : (
                <Stack component="form" onSubmit={handleUpdatePassword}  className={`${isSubmitting ? 'opacity-50 pointer-events-none' : ''}`}>
                  <Text c="dimmed">Please enter your new password.</Text>
                  <PasswordInput
                    id="new-password"
                    name="newPassword"
                    required
                    withAsterisk={false}
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.currentTarget.value)}
                    placeholder="New Password"
                    disabled={isSubmitting}
                    aria-label="New Password"
                  />
                  <PasswordInput
                    id="confirm-new-password"
                    name="confirmNewPassword"
                    required
                    withAsterisk={false}
                    value={confirmNewPassword}
                    onChange={(e) => setConfirmNewPassword(e.currentTarget.value)}
                    placeholder="Confirm New Password"
                    disabled={isSubmitting}
                    aria-label="Confirm New Password"
                  />
                  <Button 
                    type="submit" 
                    variant="outline"
                    color="black"
                    custom-color="white"
                    fullWidth
                    loading={isSubmitting}
                    disabled={!newPassword || !confirmNewPassword}
                  >
                    Update Password
                  </Button>
                </Stack>
              )}
            </Tabs.Panel>
          )}
        </Tabs>
      </Container>
      </Stack>
    </Stack>
    </Group>

  );
}
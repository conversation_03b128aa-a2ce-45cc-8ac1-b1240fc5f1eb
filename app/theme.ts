"use client";

import { <PERSON><PERSON>, Button, Input, Tabs, TextInput, PasswordInput, FloatingIndicator, createTheme, Card, Switch, Modal, Title, Container, Menu, Badge, Paper, Progress, Text, Accordion, Textarea } from '@mantine/core';
import inputClasses from './theme/Form.module.css';
import buttonClasses from './theme/Buttons.module.css';
import tabClasses from './theme/Tabs.module.css';
import cardClasses from './theme/Card.module.css';
import toggleClasses from './theme/Toggle.module.css';
import modalClasses from './theme/Modal.module.css';
import titleClasses from './theme/Titles.module.css';
import containerClasses from './theme/Container.module.css';
import menuClasses from './theme/Menu.module.css';
import badgeClasses from './theme/Badge.module.css';
import paperClasses from './theme/Paper.module.css';
import textClasses from './theme/Text.module.css';
import accordionClasses from './theme/Accordion.module.css';
import alertClasses from './theme/Alert.module.css';
import progressClasses from './theme/Progress.module.css';
import cx from 'clsx';

const baseHue = 350; // Define your hue variable here
const baseRadius = 1

export const theme = createTheme({
  fontFamily: 'var(--font-kantumruy-pro)',
  autoContrast: true,

  components: {
    Input: Input.extend({
      classNames: (_theme, props) => ({
        wrapper: cx(inputClasses.wrapper, { [inputClasses.inlineWrapper]: props.variant === 'inline' }),
        input: cx(inputClasses.input, { [inputClasses.inlineInput]: props.variant === 'inline' }),
        label: cx(inputClasses.label, { [inputClasses.inlineLabel]: props.variant === 'inline' }),
      }),
    }),
    TextInput: TextInput.extend({
      classNames: (_theme, props) => ({
        wrapper: cx(inputClasses.wrapper, { [inputClasses.inlineWrapper]: props.variant === 'inline' }),
        input: cx(inputClasses.input, { [inputClasses.inlineInput]: props.variant === 'inline' }),
        label: cx(inputClasses.label, { [inputClasses.inlineLabel]: props.variant === 'inline' }),
      }),
    }),
    PasswordInput: PasswordInput.extend({
      classNames: {
        wrapper: inputClasses.wrapper,
        input: inputClasses.input,
        section: inputClasses.section,
        root: inputClasses.root,
        label: inputClasses.label,
        required: inputClasses.required,
        description: inputClasses.description,
        error: inputClasses.error,
        innerInput: inputClasses.innerInput,
        visibilityToggle: inputClasses.visibilityToggle
      }
    }),
    Button: Button.extend({ 
      classNames: {
        root: buttonClasses.button,
        inner: buttonClasses.inner,
        label: buttonClasses.label,
        loader: buttonClasses.loader
      } 
    }),  
    Tabs: Tabs.extend({
      classNames: {
        root: tabClasses.root,
        list: tabClasses.list,
        tab: tabClasses.tab,
        panel: tabClasses.panel
      }
    }),
    FloatingIndicator: FloatingIndicator.extend({
      classNames: {
        root: tabClasses.indicator
      }
    }),
    Card: Card.extend({
      classNames: {
        root: cardClasses.card
      }
    }),
    Switch: Switch.extend({
      classNames: {
        root: toggleClasses.root,
        body: toggleClasses.body,
        track: toggleClasses.track,
        trackLabel: toggleClasses.trackLabel,
        thumb: toggleClasses.thumb,
        input: toggleClasses.input,
        labelWrapper: toggleClasses.labelWrapper,
        label: toggleClasses.label,
        description: toggleClasses.description,
        error: toggleClasses.error
      }
    }),
    Modal: Modal.extend({
      classNames: {
        root: modalClasses.root,
        header: modalClasses.header,
        title: modalClasses.title,
        body: modalClasses.body,
        close: modalClasses.close,
        overlay: modalClasses.overlay,
        content: modalClasses.content
      }
    }),
    Title: Title.extend({
      classNames: {
        root: titleClasses.title
      }
    }),
    Container: Container.extend({
      classNames: {
        root: containerClasses.container
      }
    }),
    Menu: Menu.extend({
      classNames: {
        dropdown: menuClasses.dropdown,
        arrow: menuClasses.arrow,
        overlay: menuClasses.overlay,
        divider: menuClasses.divider,
        label: menuClasses.label,
        item: menuClasses.item,
        itemLabel: menuClasses.itemLabel,
        itemSection: menuClasses.itemSection,
        chevron: menuClasses.chevron,
      }
    }),
    Badge: Badge.extend({
      classNames: {
        root: badgeClasses.badge,
        label: badgeClasses.label
      }
    }),
    Paper: Paper.extend({
      classNames: {
        root: paperClasses.paper
      }
    }),
    Text: Text.extend({
      classNames: {
        root: textClasses.text
      }
    }),
    Accordion: Accordion.extend({
      classNames: {
        root: accordionClasses.accordion,
        item: accordionClasses.item,
        control: accordionClasses.control,
        label: accordionClasses.label,
        chevron: accordionClasses.chevron,
        content: accordionClasses.content,
        panel: accordionClasses.panel
      }
    }),
    Alert: Alert.extend({
      classNames: {
        root: alertClasses.root,
        body: alertClasses.body,
        title: alertClasses.title,
        message: alertClasses.message,
        icon: alertClasses.icon,
        closeButton: alertClasses.closeButton,
      }
    }),
    Textarea: Textarea.extend({
      classNames: (_theme, props) => ({
        wrapper: cx(inputClasses.wrapper, { [inputClasses.inlineWrapper]: props.variant === 'inline' }),
        input: cx(inputClasses.input, { [inputClasses.inlineInput]: props.variant === 'inline' }),
        label: cx(inputClasses.label, { [inputClasses.inlineLabel]: props.variant === 'inline' }),
      }),
    }),
    Progress: Progress.extend({
      classNames: {
        root: progressClasses.root,
        section: progressClasses.section,
        label: progressClasses.label,
      }
    }),
  },

  primaryColor: 'primary',
  fontSizes: {
    xxs: '0.75em',   // 12px
    xs: '0.875em',   // 14px
    sm: '0.9375em',  // 16px
    md: '1em',      // 18px
    lg: '1.25em',  // 20px
    xl: '1.5em',   // 22px
    xxl: '1.75em',   // 24px
    xxxl: '3em',   // 28px
    xxxxl: '4em',   // 32px
  },
  radius: {
    xxs: `${baseRadius * .25}rem`,
    xs: `${baseRadius * .5}rem`,
    sm: `${baseRadius * .75}rem`,
    md: `${baseRadius * 1}rem`,
    lg: `${baseRadius * 1.5}rem`,
    xl: `${baseRadius * 2}rem`,
    xxl: `${baseRadius * 2.5}rem`,
    xxxl: `${baseRadius * 3}rem`,
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
    xxxl: '64px',
  },
  colors: {
    // You can customize your color palette here
    primary: [
      `hsl(${baseHue}, 100%, 10%)`,
      `hsl(${baseHue}, 100%, 15%)`,
      `hsl(${baseHue}, 100%, 20%)`,
      `hsl(${baseHue}, 100%, 25%)`,
      `hsl(${baseHue}, 100%, 30%)`,
      `hsl(${baseHue}, 100%, 35%)`,
      `hsl(${baseHue}, 100%, 40%)`,
      `hsl(${baseHue}, 100%, 45%)`,
      `hsl(${baseHue}, 100%, 50%)`,
      `hsl(${baseHue}, 100%, 60%)`,
    ],
    borders: [
      `hsl(${baseHue}, 50%, 99%)`,
      `hsl(${baseHue}, 50%, 90%)`,
      `hsl(${baseHue}, 50%, 80%)`,
      `hsl(${baseHue}, 50%, 70%)`,
      `hsl(${baseHue}, 50%, 65%)`,
      `hsl(${baseHue}, 50%, 50%)`,
      `hsl(${baseHue}, 50%, 40%)`,
      `hsl(${baseHue}, 50%, 30%)`,
      `hsl(${baseHue}, 50%, 20%)`,
      `hsl(${baseHue}, 50%, 10%)`,
    ],
  },
});

export const formStyles = { /* your form styles */ };
export const buttonStyles = { /* your button styles */ };
export const tabStyles = { 
  hidden: 'opacity-0 pointer-events-none',
  visible: 'opacity-1 pointer-events-auto'
};

